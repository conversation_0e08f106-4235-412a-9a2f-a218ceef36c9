<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Read dimension from config, provide a default
        $dimension = (int) config('embeddings.audio_dimension', 128);

        if ($dimension <= 0) {
            Log::error('Invalid configuration embeddings.audio_dimension found during index migration. Skipping index creation.');
            throw new \InvalidArgumentException('Configuration embeddings.audio_dimension must be a positive integer for index creation.');
        }

        Schema::create('article_audio_embeddings', function (Blueprint $table) use ($dimension) {
            $table->ulid('id')->primary();

            $table->foreignUlid('article_id')
                ->constrained()
                ->cascadeOnDelete();

            $table->foreignUlid('file_id')
                ->constrained()
                ->cascadeOnDelete();

            $table->vector('embedding', $dimension);

            $table->timestamps();
        });

        /**
         * Add HNSW index for cosine similarity search
         */
        Schema::table('article_audio_embeddings', function (Blueprint $table) {
            DB::statement('CREATE INDEX IF NOT EXISTS article_audio_embeddings_embedding_hnsw_idx ON article_audio_embeddings USING hnsw (embedding vector_cosine_ops)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('article_audio_embeddings')) {
            Schema::table('article_audio_embeddings', function (Blueprint $table) {
                $table->dropIndex('article_audio_embeddings_embedding_hnsw_idx');
            });
        }

        Schema::dropIfExists('article_audio_embeddings');
    }
};
