<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('article_metadata_embeddings', function (Blueprint $table) {
            $table->ulid('id')->primary();

            $table->foreignUlid('article_id')
                ->constrained()
                ->cascadeOnDelete();

            $table->vector('embedding');

            $table->text('text');

            $table->boolean('with_metadata');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_metadata_embeddings');
    }
};
