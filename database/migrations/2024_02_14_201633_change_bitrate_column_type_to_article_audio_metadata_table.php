<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('article_audio_metadata', function (Blueprint $table) {
            $table->float('bitrate')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('article_audio_metadata', function (Blueprint $table) {
            $table->integer('bitrate')->change();
        });
    }
};
