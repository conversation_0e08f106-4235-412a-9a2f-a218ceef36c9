<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('playlist_items', function (Blueprint $table) {
            $table->ulid('id')->primary();

            $table->foreignUlid('playlist_id')->constrained()->cascadeOnDelete();

            $table->foreignUlid('article_id')->constrained()->cascadeOnDelete();

            $table->unsignedSmallInteger('rank')->comment('Order of the item within the playlist');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('playlist_items');
    }
};
