<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('title');
            $table->string('storage');
            $table->string('filename_disk');
            $table->string('filename_download');
            $table->string('type');
            $table->bigInteger('filesize');
            $table->string('location');

            $table->string('url')->nullable();

            $table->integer('width')->nullable();
            $table->integer('height')->nullable();
            $table->integer('duration')->nullable();
            $table->text('description')->nullable();

            $table->json('tags')->default('[]');
            $table->json('metadata')->default('{}');

            $table->ulidMorphs('fileable');

            $table->foreignUlid('created_by')
                ->nullable()
                ->constrained(table: 'users');

            $table->foreignUlid('updated_by')
                ->nullable()
                ->constrained(table: 'users');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
