<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('artist_requests', function (Blueprint $table) {
            $table->string('youtube_link')->nullable()->after('description');
            $table->json('social_links')->nullable()->after('youtube_link');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('artist_requests', function (Blueprint $table) {
            $table->dropColumn(['youtube_link', 'social_links']);
        });
    }
};
