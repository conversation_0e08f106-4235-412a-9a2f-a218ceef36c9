<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('playlists', function (Blueprint $table) {
            $table->ulid('id')->primary();

            $table->foreignUlid('user_id')->constrained()->cascadeOnDelete(); // Playlist owner

            $table->string('name');

            $table->text('description')->nullable();

            $table->boolean('is_system_generated')->default(false)->index()->comment('TRUE if generated by recommendation engine');

            $table->string('generation_strategy')->nullable()->comment('e.g., following, like_similarity, popular'); // Store context

            $table->timestamp('generated_at')->nullable()->index()->comment('Timestamp when the system generated this playlist');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('playlists');
    }
};
