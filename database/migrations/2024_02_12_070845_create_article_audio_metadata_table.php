<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('article_audio_metadata', function (Blueprint $table) {
            $table->ulid('id')->primary();

            $table->foreignUlid('article_id')
                ->constrained()
                ->cascadeOnDelete();

            $table->foreignUlid('file_id')
                ->constrained()
                ->cascadeOnDelete();

            // Audio specs
            $table->float('tempo');
            $table->float('duration');
            $table->integer('bitrate');
            $table->integer('sample_rate');
            $table->tinyInteger('channels');
            $table->tinyInteger('layer')->default(3);
            $table->integer('score')->default(0);

            $table->string('encoder_info')->nullable();
            $table->string('encoder_settings')->nullable();

            $table->date('date')->nullable();
            $table->year('year')->nullable();
            $table->string('comment')->nullable();
            $table->json('json')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_audio_metadata');
    }
};
