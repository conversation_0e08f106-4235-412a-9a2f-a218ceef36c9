<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_documents', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->enum('type', ['terms_of_service', 'privacy_policy', 'competition_rules']);
            $table->string('title');
            $table->longText('content');
            $table->decimal('version', 3, 1)->default(1.0);
            $table->boolean('is_active')->default(false);
            $table->timestamp('effective_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['type', 'is_active']);
            $table->index(['type', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_documents');
    }
};
