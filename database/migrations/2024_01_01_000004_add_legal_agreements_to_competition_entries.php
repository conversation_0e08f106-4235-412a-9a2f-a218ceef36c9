<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('competition_entries', function (Blueprint $table) {
            $table->timestamp('terms_agreed_at')->nullable()->after('requirements_met');
            $table->timestamp('privacy_agreed_at')->nullable()->after('terms_agreed_at');
            $table->timestamp('rules_agreed_at')->nullable()->after('privacy_agreed_at');
            $table->string('agreement_ip_address')->nullable()->after('rules_agreed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('competition_entries', function (Blueprint $table) {
            $table->dropColumn([
                'terms_agreed_at',
                'privacy_agreed_at',
                'rules_agreed_at',
                'agreement_ip_address',
            ]);
        });
    }
};
