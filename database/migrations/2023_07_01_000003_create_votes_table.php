<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('votes', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('competition_id');
            $table->ulid('user_id'); // Voter
            $table->ulid('artist_id'); // Artist being voted for
            $table->integer('vote_count')->default(1); // Number of votes cast
            $table->boolean('paid')->default(false);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('competition_id')->references('id')->on('competitions')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('artist_id')->references('id')->on('users')->onDelete('cascade');

            // Remove unique constraint to allow multiple votes from same user to same artist
            // $table->unique(['competition_id', 'user_id', 'artist_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('votes');
    }
};
