<?php

namespace Database\Factories;

use App\Models\Competition;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Competition>
 */
class CompetitionFactory extends Factory
{
    protected $model = Competition::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate realistic date ranges
        $entryStart = $this->faker->dateTimeBetween('-2 months', '+1 month');
        $entryEnd = $this->faker->dateTimeBetween($entryStart, '+2 months');
        $votingStart = $this->faker->dateTimeBetween($entryEnd, '+1 week');
        $votingEnd = $this->faker->dateTimeBetween($votingStart, '+1 month');

        return [
            'name' => $this->faker->randomElement($this->getCompetitionNames()),
            'description' => $this->faker->randomElement($this->getCompetitionDescriptions()),
            'entry_start_date' => $entryStart,
            'entry_end_date' => $entryEnd,
            'voting_start_date' => $votingStart,
            'voting_end_date' => $votingEnd,
            'start_date' => $votingStart, // Backward compatibility
            'end_date' => $votingEnd, // Backward compatibility
            'status' => $this->faker->boolean(80), // 80% chance of being active
            'type' => $this->faker->randomElement(['all', 'refugees_only']),
            'stage' => $this->faker->numberBetween(1, 5),
            'vote_price' => $this->faker->randomElement([0.99, 1.00, 1.50, 2.00, 2.50]),
            'auto_approve' => $this->faker->boolean(60), // 60% chance of auto-approval
            'requirements' => $this->generateRequirements(),
        ];
    }

    /**
     * Generate realistic competition names
     */
    private function getCompetitionNames(): array
    {
        return [
            'Global Music Discovery Awards 2024',
            'Rising Stars Competition',
            'International Talent Showcase',
            'On The Move Talents Awards',
            'Breakthrough Artist Challenge',
            'World Music Festival Competition',
            'Independent Artist Spotlight',
            'Cultural Fusion Music Awards',
            'Next Generation Musicians',
            'Global Voices Competition',
            'Emerging Talent Awards',
            'Cross-Cultural Music Exchange',
            'International New Artist Awards',
            'Unity Through Music Competition',
            'Diverse Sounds Showcase',
        ];
    }

    /**
     * Generate realistic competition descriptions
     */
    private function getCompetitionDescriptions(): array
    {
        return [
            'Discover and celebrate emerging musical talent from around the world. This competition showcases artists who are breaking boundaries and creating innovative sounds that resonate with global audiences.',
            'A platform for rising stars to showcase their unique musical style and connect with fans worldwide. Join thousands of artists competing for recognition and the chance to build their fanbase.',
            'Celebrating diversity in music through an international competition that brings together artists from different cultures and backgrounds. Share your story through music and connect with a global community.',
            'Specifically designed for refugee and displaced artists, this competition provides a platform to share their powerful stories and musical heritage while building new connections in their host communities.',
            'An opportunity for breakthrough artists to gain exposure and recognition in the music industry. Compete with fellow musicians and let your talent shine on the global stage.',
            'Part of our annual music festival series, this competition highlights independent artists who are creating waves in the music scene with their original compositions and performances.',
            'Focusing on independent artists who are self-producing and self-promoting their music. This competition celebrates the entrepreneurial spirit of modern musicians and their creative independence.',
            'A celebration of cultural fusion in music, where artists blend traditional sounds with contemporary styles to create something entirely new and exciting.',
            'Designed for the next generation of musicians who are using technology and social media to revolutionize how music is created, shared, and experienced.',
            'A global platform where voices from every corner of the world come together to compete, collaborate, and celebrate the universal language of music.',
        ];
    }

    /**
     * Generate realistic requirements based on stage
     */
    private function generateRequirements(): array
    {
        $stage = $this->faker->numberBetween(1, 5);

        $requirements = [
            1 => ['min_followers' => 100, 'min_monthly_listeners' => 250, 'min_tracks' => 2],
            2 => ['min_followers' => 250, 'min_monthly_listeners' => 500, 'min_tracks' => 3],
            3 => ['min_followers' => 500, 'min_monthly_listeners' => 1000, 'min_tracks' => 5],
            4 => ['min_followers' => 1000, 'min_monthly_listeners' => 2500, 'min_tracks' => 8],
            5 => ['min_followers' => 2500, 'min_monthly_listeners' => 5000, 'min_tracks' => 12],
        ];

        return $requirements[$stage];
    }

    /**
     * Create a competition in entry phase (accepting entries, voting not started)
     */
    public function entryPhase(): static
    {
        return $this->state(function (array $attributes) {
            $entryStart = Carbon::now()->subDays(10);
            $entryEnd = Carbon::now()->addDays(20);
            $votingStart = Carbon::now()->addDays(25);
            $votingEnd = Carbon::now()->addDays(55);

            return [
                'entry_start_date' => $entryStart,
                'entry_end_date' => $entryEnd,
                'voting_start_date' => $votingStart,
                'voting_end_date' => $votingEnd,
                'start_date' => $votingStart,
                'end_date' => $votingEnd,
                'status' => true,
            ];
        });
    }

    /**
     * Create a competition in voting phase (voting active)
     */
    public function votingPhase(): static
    {
        return $this->state(function (array $attributes) {
            $entryStart = Carbon::now()->subDays(40);
            $entryEnd = Carbon::now()->subDays(10);
            $votingStart = Carbon::now()->subDays(5);
            $votingEnd = Carbon::now()->addDays(25);

            return [
                'entry_start_date' => $entryStart,
                'entry_end_date' => $entryEnd,
                'voting_start_date' => $votingStart,
                'voting_end_date' => $votingEnd,
                'start_date' => $votingStart,
                'end_date' => $votingEnd,
                'status' => true,
            ];
        });
    }

    /**
     * Create a competition that has ended
     */
    public function ended(): static
    {
        return $this->state(function (array $attributes) {
            $entryStart = Carbon::now()->subDays(90);
            $entryEnd = Carbon::now()->subDays(60);
            $votingStart = Carbon::now()->subDays(55);
            $votingEnd = Carbon::now()->subDays(25);

            return [
                'entry_start_date' => $entryStart,
                'entry_end_date' => $entryEnd,
                'voting_start_date' => $votingStart,
                'voting_end_date' => $votingEnd,
                'start_date' => $votingStart,
                'end_date' => $votingEnd,
                'status' => false,
            ];
        });
    }

    /**
     * Create a competition that hasn't started yet
     */
    public function upcoming(): static
    {
        return $this->state(function (array $attributes) {
            $entryStart = Carbon::now()->addDays(10);
            $entryEnd = Carbon::now()->addDays(40);
            $votingStart = Carbon::now()->addDays(45);
            $votingEnd = Carbon::now()->addDays(75);

            return [
                'entry_start_date' => $entryStart,
                'entry_end_date' => $entryEnd,
                'voting_start_date' => $votingStart,
                'voting_end_date' => $votingEnd,
                'start_date' => $votingStart,
                'end_date' => $votingEnd,
                'status' => true,
            ];
        });
    }

    /**
     * Create a refugees-only competition
     */
    public function refugeesOnly(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'refugees_only',
                'name' => 'On The Move Talents Awards '.Carbon::now()->year,
                'description' => 'A special competition dedicated to refugee and displaced artists, providing a platform to share their powerful stories and musical heritage while building new connections in their host communities.',
            ];
        });
    }

    /**
     * Create a high-tier competition (Stage 4-5)
     */
    public function highTier(): static
    {
        return $this->state(function (array $attributes) {
            $stage = $this->faker->numberBetween(4, 5);

            return [
                'stage' => $stage,
                'vote_price' => $this->faker->randomElement([2.00, 2.50, 3.00]),
                'requirements' => $this->generateRequirements(),
            ];
        });
    }

    /**
     * Create a beginner-friendly competition (Stage 1-2)
     */
    public function beginnerFriendly(): static
    {
        return $this->state(function (array $attributes) {
            $stage = $this->faker->numberBetween(1, 2);

            return [
                'stage' => $stage,
                'vote_price' => $this->faker->randomElement([0.99, 1.00]),
                'auto_approve' => true,
                'requirements' => $this->generateRequirements(),
            ];
        });
    }
}
