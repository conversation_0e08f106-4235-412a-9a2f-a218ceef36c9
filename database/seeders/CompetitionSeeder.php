<?php

namespace Database\Seeders;

use App\Models\Competition;
use Illuminate\Database\Seeder;

class CompetitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing competitions
        Competition::truncate();

        // Create 10 realistic competition samples with different phases and characteristics

        // 1. Current competition in entry phase - Global Music Discovery Awards
        Competition::factory()->entryPhase()->create([
            'name' => 'Global Music Discovery Awards 2024',
            'description' => 'Discover and celebrate emerging musical talent from around the world. This competition showcases artists who are breaking boundaries and creating innovative sounds that resonate with global audiences. Join thousands of artists from over 50 countries competing for recognition and the chance to build their fanbase.',
            'type' => 'all',
            'stage' => 3,
            'vote_price' => 1.50,
            'auto_approve' => true,
            'requirements' => [
                'min_followers' => 500,
                'min_monthly_listeners' => 1000,
                'min_tracks' => 5,
            ],
        ]);

        // 2. Current competition in voting phase - Rising Stars Competition
        Competition::factory()->votingPhase()->create([
            'name' => 'Rising Stars Competition',
            'description' => 'A platform for rising stars to showcase their unique musical style and connect with fans worldwide. This competition focuses on artists who are on the verge of breakthrough success and need that extra push to reach the next level.',
            'type' => 'all',
            'stage' => 2,
            'vote_price' => 1.00,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 250,
                'min_monthly_listeners' => 500,
                'min_tracks' => 3,
            ],
        ]);

        // 3. Refugees-only competition in entry phase
        Competition::factory()->entryPhase()->refugeesOnly()->create([
            'name' => 'On The Move Talents Awards 2024',
            'description' => 'Specifically designed for refugee and displaced artists, this competition provides a platform to share their powerful stories and musical heritage while building new connections in their host communities. We celebrate resilience, cultural diversity, and the universal power of music to heal and unite.',
            'stage' => 2,
            'vote_price' => 0.99,
            'auto_approve' => true,
            'requirements' => [
                'min_followers' => 100,
                'min_monthly_listeners' => 250,
                'min_tracks' => 2,
            ],
        ]);

        // 4. High-tier competition in voting phase
        Competition::factory()->votingPhase()->highTier()->create([
            'name' => 'International Talent Showcase - Platinum Edition',
            'description' => 'Our most prestigious competition for established artists ready to take their career to the next level. This platinum-tier showcase attracts industry professionals, record labels, and music enthusiasts looking for the next big thing in music.',
            'type' => 'all',
            'stage' => 5,
            'vote_price' => 3.00,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 2500,
                'min_monthly_listeners' => 5000,
                'min_tracks' => 12,
            ],
        ]);

        // 5. Beginner-friendly competition in entry phase
        Competition::factory()->entryPhase()->beginnerFriendly()->create([
            'name' => 'New Artist Discovery Challenge',
            'description' => 'Perfect for artists just starting their musical journey! This beginner-friendly competition welcomes new talent with lower entry requirements and automatic approval for qualifying artists. Get your first taste of competitive music showcasing.',
            'type' => 'all',
            'stage' => 1,
            'vote_price' => 0.99,
            'auto_approve' => true,
            'requirements' => [
                'min_followers' => 100,
                'min_monthly_listeners' => 250,
                'min_tracks' => 2,
            ],
        ]);

        // 6. Upcoming competition
        Competition::factory()->upcoming()->create([
            'name' => 'Summer Music Festival Competition 2024',
            'description' => 'Get ready for our biggest summer event! This competition will feature winners performing at our virtual summer music festival, with live streaming to audiences worldwide. Prepare your best summer anthems and get ready to shine.',
            'type' => 'all',
            'stage' => 3,
            'vote_price' => 2.00,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 750,
                'min_monthly_listeners' => 1500,
                'min_tracks' => 6,
            ],
        ]);

        // 7. Ended competition
        Competition::factory()->ended()->create([
            'name' => 'Cultural Fusion Music Awards 2023',
            'description' => 'A celebration of cultural fusion in music, where artists blended traditional sounds with contemporary styles to create something entirely new and exciting. This competition has concluded, but you can still view the amazing results and winner announcements.',
            'type' => 'all',
            'stage' => 4,
            'vote_price' => 2.50,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 1000,
                'min_monthly_listeners' => 2500,
                'min_tracks' => 8,
            ],
        ]);

        // 8. Another voting phase competition - Independent Artist Spotlight
        Competition::factory()->votingPhase()->create([
            'name' => 'Independent Artist Spotlight',
            'description' => 'Focusing on independent artists who are self-producing and self-promoting their music. This competition celebrates the entrepreneurial spirit of modern musicians and their creative independence. Show the world what you can achieve on your own terms.',
            'type' => 'all',
            'stage' => 3,
            'vote_price' => 1.50,
            'auto_approve' => true,
            'requirements' => [
                'min_followers' => 400,
                'min_monthly_listeners' => 800,
                'min_tracks' => 4,
            ],
        ]);

        // 9. Another entry phase competition - Cross-Cultural Music Exchange
        Competition::factory()->entryPhase()->create([
            'name' => 'Cross-Cultural Music Exchange',
            'description' => 'A unique competition that encourages artists to collaborate across cultural boundaries and create music that bridges different traditions and styles. Whether you\'re blending genres or languages, this is your platform to showcase cultural unity through music.',
            'type' => 'all',
            'stage' => 2,
            'vote_price' => 1.25,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 300,
                'min_monthly_listeners' => 600,
                'min_tracks' => 3,
            ],
        ]);

        // 10. High-tier upcoming competition
        Competition::factory()->upcoming()->highTier()->create([
            'name' => 'Elite Musicians Championship 2024',
            'description' => 'Our most exclusive competition reserved for elite musicians who have proven their talent and built substantial followings. This championship offers the highest prizes and most prestigious recognition in our platform. Only the best of the best qualify.',
            'type' => 'all',
            'stage' => 5,
            'vote_price' => 5.00,
            'auto_approve' => false,
            'requirements' => [
                'min_followers' => 5000,
                'min_monthly_listeners' => 10000,
                'min_tracks' => 15,
            ],
        ]);

        $this->command->info('Created 10 realistic competition samples with various phases and characteristics.');
        $this->command->info('Competitions include: entry phase, voting phase, ended, and upcoming competitions.');
        $this->command->info('Mix of general and refugees-only competitions with different tiers and requirements.');
    }
}
