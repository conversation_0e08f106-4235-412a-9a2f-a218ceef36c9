<?php

namespace App\Actions\Fortify;

use App\Events\Auth\EmailVerificationNotificationSent;
use App\Events\Auth\UserProfileInformationUpdated;
use App\Models\User;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     *
     * @param  array<string, string>  $input
     */
    public function update(\Illuminate\Foundation\Auth\User $user, array $input): void
    {
        $validator = Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],

            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],

            'phone' => ['nullable', 'regex:/^([0-9\s\-\+\(\)]*)$/', 'min:10'],
            'country_name' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],

        ]);

        $validator->validateWithBag('updateProfileInformation');

        $validated = $validator->safe()->except('image');

        if (
            $validated['email'] !== $user->email &&
            $user instanceof MustVerifyEmail
        ) {
            $this->updateVerifiedUser($user, $validated);
        } else {
            $user->forceFill($validated)->save();
        }

        UserProfileInformationUpdated::dispatch($user);
    }

    /**
     * Update the given verified user's profile information.
     *
     * @param  array<string, string>  $input
     */
    protected function updateVerifiedUser(User $user, array $input): void
    {
        $user->forceFill([
            ...$input,
            'email_verified_at' => null,
        ])->save();

        $user->sendEmailVerificationNotification();

        EmailVerificationNotificationSent::dispatch($user);
    }
}
