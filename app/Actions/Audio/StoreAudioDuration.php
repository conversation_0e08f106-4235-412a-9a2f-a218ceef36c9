<?php

namespace App\Actions\Audio;

use App\Media\Audio\MP3File;
use App\Media\TmpFile;
use App\Models\File\Audio;
use App\Models\Morph\File;
use Illuminate\Support\Str;

class StoreAudioDuration
{
    /**
     * @param  File  $audio  The audio file
     */
    public function __construct(private readonly File $audio) {}

    /**
     * Execute the action to store audio duration
     */
    public function execute(): void
    {
        if (! $this->isCompatibleAudioType($this->audio->type)) {
            return;
        }

        $duration = $this->calculateAudioDuration();
        $this->storeDuration($duration);
    }

    /**
     * Calculate the duration of the audio file
     */
    private function calculateAudioDuration(): int
    {
        $tmpFile = new TmpFile($this->audio);
        $mp3file = new MP3File($tmpFile->path());

        try {
            return $mp3file->getDurationEstimate();
        } catch (\Throwable $th) {
            return $mp3file->getDuration();
        }
    }

    /**
     * Store the duration in the audio model
     */
    private function storeDuration(int $duration): void
    {
        $this->audio->duration = $duration;
        $this->audio->saveQuietly();
    }

    /**
     * Check if the file type is compatible for processing
     */
    private function isCompatibleAudioType(string $mimeType): bool
    {
        return in_array(Str::lower($mimeType), Audio::AUDIO_MIME_TYPES, true);
    }
}
