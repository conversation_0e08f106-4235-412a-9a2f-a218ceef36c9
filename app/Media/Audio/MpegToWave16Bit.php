<?php

namespace App\Media\Audio;

use Illuminate\Process\Exceptions\ProcessFailedException;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use InvalidArgumentException;
use RuntimeException;

class MpegToWave16Bit
{
    private string $inputFile;

    private ?string $outputFile = null;

    private bool $ownsOutputFile = false;

    private ?int $length = null;

    public function __construct(
        string $input,
        ?string $output = null,
        ?int $length = null,
    ) {
        $this->inputFile = $input;
        $this->outputFile = $output;
        $this->length = $length;
    }

    /**
     * Converts the input MPEG file to a 16-bit WAV file.
     *
     * @return string The path to the generated WAV file.
     *
     * @throws InvalidArgumentException If the input file doesn't exist.
     * @throws RuntimeException If the ffmpeg conversion process fails or another error occurs.
     */
    public function convert(): string
    {
        if (! file_exists($this->inputFile) || ! is_readable($this->inputFile)) {
            throw new InvalidArgumentException("Input file not found or not readable: {$this->inputFile}");
        }

        // If no output file path was provided, create a temporary one.
        if (blank($this->outputFile)) {
            $this->outputFile = Storage::disk('tmp')->path(Str::uuid().'.wav');
            $this->ownsOutputFile = true; // Mark that this class instance created the temp file
        } else {
            // Ensure the output directory exists if a specific path is given
            $outputDir = dirname($this->outputFile);
            if (! is_dir($outputDir)) {
                if (! mkdir($outputDir, 0775, true) && ! is_dir($outputDir)) {
                    throw new RuntimeException("Could not create output directory: {$outputDir}");
                }
            }
            $this->ownsOutputFile = false; // User provided the path, they manage it
        }

        // Build the ffmpeg command arguments as an array for security
        $command = [
            'ffmpeg',               // Just provide the command name as a string
            '-y',                   // Overwrite output files without asking
            '-i',
            $this->inputFile,
            '-acodec',
            'pcm_s16le', // 16-bit PCM Little Endian (standard WAV)
            '-ar',
            '44100',         // Sample rate 44.1 kHz
            '-ac',
            '2',             // Force stereo channels consistently
        ];

        // Add time constraints if length is specified
        if (filled($this->length) && $this->length > 0) {
            $command[] = '-ss';
            $command[] = '0';
            $command[] = '-t';
            $command[] = (string) $this->length;
        }

        // Add the final output file path
        $command[] = $this->outputFile;

        try {
            // Execute the command
            // Process::run will find 'ffmpeg' in the PATH
            $result = Process::run($command);

            // Check for success
            if (! $result->successful()) {
                // If the process failed, throw the specific exception
                throw new ProcessFailedException($result);
            }

        } catch (ProcessFailedException $e) {
            // Clean up temporary file ONLY if we created it and it exists after failure
            $this->clearIfOwned();

            // Re-throw a more specific exception or log details
            // The ProcessFailedException already contains rich details
            throw new RuntimeException(
                'FFmpeg conversion failed. Command: '.$e->result->command().
                ' | Exit Code: '.$e->result->exitCode().
                ' | Error Output: '.$e->result->errorOutput(),
                $e->getCode(), // Preserve original exit code if available
                $e // Chain the original exception
            );
        } catch (\Throwable $th) { // Catch other potential errors (like the one you saw)
            // Attempt cleanup on other errors too
            $this->clearIfOwned();
            // Re-throw a generic runtime exception, adding the original error message
            // This will now correctly report permission issues, command not found (if not in PATH), etc.
            throw new RuntimeException(
                'An unexpected error occurred preparing or starting the FFmpeg process: '.$th->getMessage(),
                $th->getCode(),
                $th
            );
        }

        // Return the path to the successfully created output file
        return $this->outputFile;
    }

    /**
     * Deletes the output file *only if* it was temporarily created by this instance.
     * Call this explicitly when you are finished with the temporary file.
     * This method does NOT automatically get called.
     *
     * @return bool True if the file was owned and successfully deleted (or didn't exist), false otherwise.
     */
    public function clearIfOwned(): bool
    {
        if ($this->ownsOutputFile && filled($this->outputFile) && file_exists($this->outputFile)) {
            try {
                $fileToDelete = $this->outputFile;
                $this->outputFile = null;
                $this->ownsOutputFile = false;

                return unlink($fileToDelete);
            } catch (\Throwable $th) {
                // Log error if needed
                $this->outputFile = null; // Ensure state reset even on failure
                $this->ownsOutputFile = false;

                return false;
            }
        }

        return true;
    }

    /**
     * Gets the path to the output file.
     *
     * @return string|null The output file path, or null if not yet determined/generated or already cleared.
     */
    public function getOutputFile(): ?string
    {
        return $this->outputFile;
    }
}
