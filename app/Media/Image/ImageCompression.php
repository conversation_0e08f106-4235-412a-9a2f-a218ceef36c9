<?php

namespace App\Media\Image;

class ImageCompression extends ImageResource implements OptimizableImage
{
    public function compress_image(string $source_url, ?string $destination_url = null, int $quality = 50, int $widths = 1080): string
    {
        if (is_null($destination_url)) {
            $destination_url = $source_url;
        }

        [$image, $info] = $this->createResource($source_url);

        if (! isset($image) || ! $image) {
            return $source_url;
        }

        $width = explode('"', $info[3]);
        $width = intval($width[1]);

        if (! is_null($widths) && $width > $widths) {
            $width = $widths;
        }

        $imageH = $this->createTheImage(
            file_temp_name: $source_url,
            src: $image,
            newWidth_min: $width
        );

        imagejpeg($imageH, $destination_url, $quality);

        return $destination_url;
    }

    /**
     * @param  mixed  $src
     * @return void
     */
    private function createTheImage(string $file_temp_name, \GdImage $src, int $newWidth_min = 800)
    {
        [$width_min, $height_min] = getimagesize($file_temp_name);
        $newHeight_min = ($height_min / $width_min) * $newWidth_min;
        $tmp_min = imagecreatetruecolor($newWidth_min, $newHeight_min);
        imagecopyresampled($tmp_min, $src, 0, 0, 0, 0, $newWidth_min, $newHeight_min, $width_min, $height_min);

        return $tmp_min;
    }
}
