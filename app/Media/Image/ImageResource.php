<?php

namespace App\Media\Image;

use Illuminate\Support\Str;

class ImageResource
{
    /**
     * @return (resource|\GdImage|false|null|array)[]
     */
    public function createResource(string $source_url): array
    {
        $info = getimagesize($source_url);
        $mineType = Str::lower($info['mime']);

        switch ($mineType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source_url);
                break;
            case 'image/jpg':
                $image = imagecreatefromjpeg($source_url);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($source_url);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_url);
                break;
            default:
                $image = null;
                break;
        }

        return [$image, $info];
    }
}
