<?php

namespace App\Media;

use App\Models\Morph\File;

class TmpFile
{
    private string $tmpFile;

    public function __construct(private File $file)
    {
        $fs = $this->storage($file);

        if (! $fs->exists($file->filename_disk)) {
            throw new \Exception("File not found: {$file->storage} - {$file->filename_disk}");
        }

        $basename = str($file->filename_disk)->basename();
        $tmpFile = \Str::random(16).'-'.$basename;

        $created = $this->tmp()->put($tmpFile, $fs->readStream($file->filename_disk));

        throw_if($created === false, new \Exception('Failed to create tmp file'));

        $this->tmpFile = $tmpFile;
    }

    public function __destruct()
    {
        $this->close();
    }

    private function storage(File $file)
    {
        return \Storage::disk($file->storage);
    }

    private function tmp()
    {
        return \Storage::disk('tmp');
    }

    public function path()
    {
        return $this->tmp()->path($this->tmpFile);
    }

    public function readStream()
    {
        return $this->tmp()->readStream(path: $this->tmpFile);
    }

    public function close()
    {
        return $this->tmp()->delete($this->tmpFile);
    }
}
