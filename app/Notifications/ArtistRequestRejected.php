<?php

namespace App\Notifications;

use App\Models\ArtistRequest;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ArtistRequestRejected extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(private ArtistRequest $artistRequest)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [
            'database',
            'mail',
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line("
We regret to inform you that your request to join our team as an artist has been declined.\n
We appreciate your interest in becoming a part of our creative community; however, after careful consideration,
we have determined that your request does not align with our current requirements.\n\n

Please note that this decision does not reflect any judgment on your artistic abilities.
We encourage you to continue pursuing your passion and wish you the best in your artistic endeavors.
            ")
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return FilamentNotification::make()
            ->title('Artist request Rejected')
            ->body('We regret to inform you that your request to join our team as an artist has been declined.')
            ->warning()
            ->getDatabaseMessage();
    }

    /**
     * Determine which connections should be used for each notification channel.
     *
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }
}
