<?php

namespace App\Notifications;

use App\Models\ArtistRequest;
use App\Models\User;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ArtistRequestCreated extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(private ArtistRequest $artistRequest)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [
            'database',
            'mail',
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $artist = $this->artistRequest->user;
        if (! $artist) {
            return (new MailMessage)
                ->line('An artist request has been created by a user.')
                ->line('However, the user details are not available.')
                ->action('Notification Action', url('/'))
                ->line('Thank you for using our application!');
        }

        return (new MailMessage)
            ->line("
An artist request has been created by a user.
Please review the details and take necessary actions as per your process.

User Email: {$artist->email}
User Name: {$artist->name}
Role request: {$this->artistRequest->artist_type->value}
            ")
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        if (! $notifiable instanceof User) {
            return [];
        }

        $artist = $this->artistRequest->user;

        if (! $artist) {
            return [
                'message' => 'An artist request has been created by a user, but the user details are not available.',
            ];
        }

        return FilamentNotification::make()
            ->title('New artist request')
            ->body("
An artist request has been created by a user.
Please review the details and take necessary actions as per your process.\n
User Email: {$artist->email}\n\n
User Name: {$artist->name}\n
Role request: {$this->artistRequest->artist_type->value}\n
            ")
            ->getDatabaseMessage();

    }
}
