<?php

namespace App\Notifications;

use App\Models\CompetitionEntry;
use App\Models\User;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class CompetitionEntryStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    public string $newStatus;

    public string $competitionName;

    public string $competitionId;

    /**
     * Create a new notification instance.
     *
     * @param  string|null  $statusOverride  Used for 'deleted' event to set status text.
     */
    public function __construct(private CompetitionEntry $competitionEntry, ?string $statusOverride = null)
    {
        // Eager load relationships to prevent N+1 queries
        $this->competitionEntry->load(['competition', 'user']);

        $this->newStatus = $statusOverride ?? $this->competitionEntry->status;

        // Null safety checks for competition relationship
        $competition = $this->competitionEntry->competition;
        $this->competitionName = $competition ? $competition->name : 'Unknown Competition';
        $this->competitionId = $this->competitionEntry->competition_id;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $greeting = 'Hello '.$notifiable->name.',';
        $actionText = 'View Competition Leaderboard';

        // Generate the action URL with null safety
        $actionUrl = $this->getActionUrl();

        // Get subject and message content
        [$subject, $line] = $this->getMailContent();

        return (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($line)
            ->action($actionText, $actionUrl)
            ->line('Thank you for participating!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(User $notifiable): array
    {
        // Get title and body content
        [$title, $body] = $this->getDatabaseContent();

        // Generate the action URL with null safety
        $actionUrl = $this->getActionUrl();

        return FilamentNotification::make()
            ->title($title)
            ->body($body)
            ->actions([
                Action::make('View Leaderboard')
                    ->url($actionUrl),
            ])
            ->getDatabaseMessage();
    }

    /**
     * Get the action URL for the notification.
     */
    private function getActionUrl(): string
    {
        try {
            return route('app.voting.leaderboard', ['competition' => $this->competitionId]);
        } catch (\Exception $e) {
            // Fallback to voting index if route generation fails
            return route('app.voting.index');
        }
    }

    /**
     * Get mail content based on status.
     *
     * @return array{0: string, 1: string} [subject, line]
     */
    private function getMailContent(): array
    {
        switch ($this->newStatus) {
            case 'approved':
                return [
                    "Your Entry for '{$this->competitionName}' has been Approved!",
                    "Congratulations! Your competition entry for the competition '{$this->competitionName}' has been approved.",
                ];
            case 'rejected':
                return [
                    "Update on Your Entry for '{$this->competitionName}'",
                    "We regret to inform you that your competition entry for '{$this->competitionName}' has been rejected.",
                ];
            case 'deleted':
                return [
                    "Your Entry for '{$this->competitionName}' Has Been Removed",
                    "Your competition entry for the competition '{$this->competitionName}' has been removed.",
                ];
            default:
                return [
                    "Status Update for Your Entry in '{$this->competitionName}'",
                    "The status of your entry for the competition '{$this->competitionName}' has been updated to: ".Str::title(str_replace('_', ' ', $this->newStatus)).'.',
                ];
        }
    }

    /**
     * Get database notification content based on status.
     *
     * @return array{0: string, 1: string} [title, body]
     */
    private function getDatabaseContent(): array
    {
        switch ($this->newStatus) {
            case 'approved':
                return [
                    'Entry Approved!',
                    "Your entry for '{$this->competitionName}' has been approved.",
                ];
            case 'rejected':
                return [
                    'Entry Rejected',
                    "Your entry for '{$this->competitionName}' has been rejected.",
                ];
            case 'deleted':
                return [
                    'Entry Removed',
                    "Your entry for '{$this->competitionName}' has been removed.",
                ];
            default:
                return [
                    'Entry Status Updated',
                    "Status of your entry for '{$this->competitionName}' is now ".Str::title(str_replace('_', ' ', $this->newStatus)).'.',
                ];
        }
    }
}
