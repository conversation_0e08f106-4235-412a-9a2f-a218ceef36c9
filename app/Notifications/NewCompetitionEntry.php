<?php

namespace App\Notifications;

use App\Models\CompetitionEntry;
use App\Models\User;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewCompetitionEntry extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(private CompetitionEntry $competitionEntry)
    {
        $this->competitionEntry->load(['competition', 'user']);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [
            'database',
            'mail',
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $competition = $this->competitionEntry->competition;
        $user = $this->competitionEntry->user;

        // Null safety checks
        if (! $competition || ! $user) {
            return (new MailMessage)
                ->subject('New Competition Entry Submitted')
                ->line('A new competition entry has been submitted.')
                ->line('Please check the admin panel for details.');
        }

        $viewUrl = route('filament.app.resources.competitions.view', [
            'record' => $this->competitionEntry->competition_id,
            'activeRelationManager' => '1',
        ]);

        return (new MailMessage)
            ->subject('New Competition Entry Submitted')
            ->line("A new entry has been submitted for the competition: {$competition->name}.")
            ->line("Entry submitted by: {$user->name} ({$user->email})")
            ->action('View Entry', $viewUrl)
            ->line('Please review the new entry.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(User $notifiable): array
    {
        $competition = $this->competitionEntry->competition;
        $user = $this->competitionEntry->user;

        // Null safety checks
        if (! $competition || ! $user) {
            return FilamentNotification::make()
                ->title('New Competition Entry')
                ->body('A new competition entry has been submitted. Please check the admin panel for details.')
                ->getDatabaseMessage();
        }

        $viewUrl = route('filament.app.resources.competitions.view', [
            'record' => $this->competitionEntry->competition_id,
            'activeRelationManager' => '1',
        ]);

        return FilamentNotification::make()
            ->title('New Competition Entry')
            ->body("A new entry has been submitted for the competition '{$competition->name}' by {$user->name}.")
            ->actions([
                Action::make('View')
                    ->url($viewUrl),
            ])
            ->getDatabaseMessage();
    }
}
