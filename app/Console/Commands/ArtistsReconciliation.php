<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\Channel;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ArtistsReconciliation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:artists-reconciliation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'artists reconciliation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userQuery = User::artist()->withTrashed();
        $artistsCount = $userQuery->count();

        $bar = $this->output->createProgressBar($artistsCount);
        $bar->start();

        $userQuery->lazy()
            ->each(function (User $user) use ($bar) {
                $this->reconciliate($user);

                $bar->advance();
            });

        $bar->finish();

        $this->newLine(2);
    }

    public function reconciliate(User $user)
    {
        $validArtistNamePattern = '/[^a-zA-Z0-9 _-]/';
        $names = preg_split("/(Feat\.?\s|;)/i", $user->name);
        if ($names === false || count($names) < 2) {
            return;
        }

        $collection = collect($names)
            ->map(fn (string $name) => artistfy($name))
            ->filter(fn (string $name) => strlen($name) > 1);

        $featuring = $collection
            ->collect()
            ->slice(1)
            ->map(function (string $name) use ($user) {
                $email = emailfy(
                    text: $name,
                    domain: Str::after($user->email, '@')
                );

                $newUser = User::where('email', 'like', $email)->first();

                if (filled($newUser)) {
                    return $newUser;
                }

                return User::create([
                    'name' => $name,
                    'email' => $email,
                    'role' => $user->role,
                    'password' => Str::random(10),
                ]);
            });

        // Get fist name from split user name
        $name = $collection->first();

        if (blank($name)) {
            return;
        }

        $email = emailfy(
            text: $name,
            domain: Str::after($user->email, '@')
        );

        $newUser = User::withTrashed()->where('name', 'like', $name)->first();

        $channels = $user->channels;

        if (filled($newUser) && $user->id !== $newUser->id) {
            $user->channels->each(
                fn (Channel $channel) => $channel->fill(['user_id' => $newUser->id])->save()
            );

            $user->delete();
        } else {
            $existEmail = User::withTrashed()->where('email', 'like', $email)->first();

            if (blank($existEmail)) {
                $user->update([
                    'name' => $name,
                    'email' => $email,
                ]);
            } else {
                $user->update([
                    'name' => $name,
                ]);
            }
        }

        // Update Articles featuring
        if ($featuring->isNotEmpty()) {
            $feats = $featuring
                ->collect()
                ->map(fn (User $user) => ['user_id' => $user->id])
                ->toArray();

            $channels->each(
                fn (Channel $channel) => $channel->articles
                    ->each(fn (Article $a) => $a->featurings()->createMany($feats))
            );
        }
    }
}
