<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ArticleFeaturingExtract extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:article-featuring-extract';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'article featuring extract';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $articleQuery = Article::withTrashed();
        $artistsCount = $articleQuery->count();

        $bar = $this->output->createProgressBar($artistsCount);
        $bar->start();

        $articleQuery->lazy()
            ->each(function (Article $article) use ($bar) {
                $user = $article->user;
                collect(extractFeaturingArtists($article->name))
                    ->filter(fn (string $name) => strlen($name) > 1)
                    ->map(function (string $name) use ($user) {
                        $email = emailfy(
                            text: $name,
                            domain: Str::after($user->email, '@')
                        );

                        $newUser = User::where('email', 'like', $email)->first();

                        if (filled($newUser)) {
                            return $newUser;
                        }

                        return User::create([
                            'name' => $name,
                            'email' => $email,
                            'role' => $user->role,
                            'password' => Str::random(10),
                        ]);
                    })
                    ->each(function (User $user) use ($article) {
                        $article->featurings()->firstOrCreate(['user_id' => $user->id]);
                    });

                $bar->advance();
            });

        $bar->finish();
        $this->newLine(2);
    }
}
