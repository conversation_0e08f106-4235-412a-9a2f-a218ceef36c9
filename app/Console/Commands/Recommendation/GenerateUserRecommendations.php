<?php

namespace App\Console\Commands\Recommendation;

use App\Jobs\Recommendation\GenerateUserRecommendationsJob;
use Illuminate\Console\Command;

class GenerateUserRecommendations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-user-recommendations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate recommendations for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        GenerateUserRecommendationsJob::dispatch();
    }
}
