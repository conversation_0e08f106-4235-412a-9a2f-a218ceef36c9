<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\Channel;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class MergeArtistChannels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:merge-artist-channels';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'merge artist channels';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userQuery = User::artist()->withTrashed();
        $artistsCount = $userQuery->count();

        $bar = $this->output->createProgressBar($artistsCount);
        $bar->start();

        $userQuery->lazy()
            ->each(function (User $user) use ($bar) {
                $this->mergeChannels($user);

                $bar->advance();
            });

        $bar->finish();
        $this->newLine(2);
    }

    public function mergeChannels(User $user)
    {
        $channels = $user->channels;
        $treated = [];

        $channels->each(function (Channel $channel) use (&$treated, $channels) {
            $channelName = Str::lower(trim($channel->name));

            if (in_array($channelName, $treated)) {
                return;
            }

            $channels
                ->filter(
                    fn (Channel $chan) => $chan->id !== $channel->id && Str::lower(trim($chan->name)) === $channelName
                )

                ->each(function (Channel $chan) use ($channel) {
                    $articles = $chan->articles;

                    $articles->each(function (Article $article) use ($channel) {
                        $exists = $channel->articles()->where('name', 'like', $article->name)->exists();

                        if (! $exists) {
                            $article->fill(['channel_id' => $channel->id])->save();
                        } else {
                            $article->delete();
                        }

                    });

                    $chan->delete();
                });

            $treated[] = $channelName;
        });
    }
}
