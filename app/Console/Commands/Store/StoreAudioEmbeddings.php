<?php

namespace App\Console\Commands\Store;

use App\Jobs\Embedding;
use App\Models\Article;
use App\Models\File\Audio;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class StoreAudioEmbeddings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:store-audio-embeddings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'store audio embeddings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $audios = Audio::whereHasMorph('fileable', Article::class)
            ->has('embedding', '<')
            ->withTrashed();

        $chunkSize = 4;
        $batches = [];

        $audioCount = intval($audios->count() / $chunkSize);
        $bar = $this->output->createProgressBar($audioCount);

        $bar->start();

        $audios->chunk($chunkSize, function (Collection $audios) use ($bar, &$batches) {
            $chunk = $audios->map(fn (Audio $audio) => new Embedding\StorePredictedAudioEmbedding($audio))->toArray();

            $batches[] = Bus::batch($chunk)
                ->allowFailures()
                ->name("Store Predicted Audio Embeddings {$bar->getProgress()}/{$bar->getMaxSteps()}");

            $bar->advance();
        });

        if (filled($batches)) {
            Bus::chain($batches)->dispatch();
        }

        $bar->finish();
        $this->newLine(2);
    }
}
