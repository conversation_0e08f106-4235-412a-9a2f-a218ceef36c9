<?php

namespace App\Console\Commands\Store;

use App\Jobs\StoreExtractedAudioMetadata;
use App\Models\Article;
use App\Models\File\Audio;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class StoreAudioMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:store-audio-metadata';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'store audio metadata';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $audios = Audio::whereHasMorph('fileable', Article::class)
            ->has('metadata', '<')
            ->withTrashed();

        $chunkSize = 2;

        $audioCount = intval($audios->count() / $chunkSize);

        $bar = $this->output->createProgressBar($audioCount);

        $bar->start();

        $batches = [];

        $audios->chunk($chunkSize, function (Collection $audios) use ($bar, &$batches) {
            $chunk = $audios->map(fn (Audio $audio) => new StoreExtractedAudioMetadata($audio))->toArray();

            $batches[] = Bus::batch($chunk)
                ->allowFailures()
                ->name("Store Extracted Audio Metadata {$bar->getProgress()}/{$bar->getMaxSteps()}");

            $bar->advance();
        });

        if (filled($batches)) {
            Bus::chain($batches)->dispatch();
        }

        $bar->finish();
        $this->newLine(2);
    }
}
