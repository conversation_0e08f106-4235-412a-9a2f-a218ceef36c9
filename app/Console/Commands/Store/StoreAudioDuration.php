<?php

namespace App\Console\Commands\Store;

use App\Actions\Audio\StoreAudioDuration as StoreAudioDurationAction;
use App\Models\Article;
use App\Models\File\Audio;
use Illuminate\Console\Command;

class StoreAudioDuration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:store-audio-duration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will store the duration of the audio file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $audios = Audio::whereHasMorph('fileable', Article::class)
            ->where('duration', null)
            ->whereIn('type', Audio::AUDIO_MIME_TYPES)
            ->withTrashed();

        $audioCount = $audios->count();
        $bar = $this->output->createProgressBar($audioCount);
        $bar->start();

        $audios->each(function (Audio $audio) use ($bar) {
            try {
                $action = new StoreAudioDurationAction($audio);
                $action->execute();
            } catch (\Throwable $th) {
                // throw $th;
            }

            $bar->advance();
        });

        $bar->finish();
        $this->newLine(2);
    }
}
