<?php

namespace App\Console\Commands\Store;

use App\Jobs\Embedding;
use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class StoreArticleMetadataEmbeddings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:store-article-metadata-embeddings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'store article metadata embeddings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $articles = Article::has('metadataEmbedding', '<')->withTrashed();

        $chunkSize = 5;

        $articleCount = intval($articles->count() / $chunkSize);

        $bar = $this->output->createProgressBar($articleCount);

        $bar->start();

        $batches = [];

        $articles->chunk($chunkSize, function (Collection $articles) use ($bar, &$batches) {
            $chunk = $articles->map(fn (Article $article) => new Embedding\StoreExtractedArticleMetadataEmbedding($article))->toArray();

            $batches[] = Bus::batch($chunk)
                ->allowFailures()
                ->name("Store Extracted Article Metadata Embedding {$bar->getProgress()}/{$bar->getMaxSteps()}");

            $bar->advance();
        });

        if (filled($batches)) {
            Bus::chain($batches)->dispatch();
        }

        $bar->finish();
        $this->newLine(2);
    }
}
