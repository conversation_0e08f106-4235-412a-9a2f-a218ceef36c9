<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\File\Audio;
use App\Models\Genre;
use App\Models\Morph\File;
use App\Repositories\GenreRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AudioGenreReconciliation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:audio-genre-reconciliation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reconciliate audio articles with audio file genre';

    /**
     * Execute the console command.
     */
    public function handle(GenreRepository $genreRepository)
    {
        // Test audio_metadata python
        Process::run('python3 -c "import audio_metadata"')->throw();

        $disk = Storage::disk(Audio::getDisk());
        $files = collect($disk->files());

        // Run
        $this->withProgressBar($files, function ($file) use ($disk, $genreRepository) {
            if (
                ! in_array(
                    $disk->mimeType($file),
                    ['application/octet-stream', ...Audio::AUDIO_MIME_TYPES]
                )
            ) {
                return;
            }

            $filename = $disk->path($file);
            $fileModel = File::where('filename_disk', $file)->first();

            if (blank($fileModel)) {
                return;
            }

            $fileable = $fileModel->fileable;

            if ($fileable instanceof Article) {
                $process = Process::input($filename)->run("python3 scripts/python/audio_genre.py {$filename}");

                if ($process->failed()) {
                    return;
                }

                $genreNameFile = Str::of($process->output())->between('[', ']')->value();

                // If file hasn't meta genre
                if (blank($genreNameFile)) {
                    return;
                }

                // Find or Create genre
                $genre = $genreRepository->findOrCreate($genreNameFile);

                $fileable->fill(['genre_id' => $genre->id]);
                $fileable->save();
            }
        });

        $this->newLine(2);
        $this->info('Audio articles reconciliate process done!');
        $this->newLine();
    }
}
