<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class MergeArtists extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:merge-artists';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'merge artists';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // @TODO Merge duplicated users
        //  User::select(["name"])->groupBy("name")->get();
    }
}
