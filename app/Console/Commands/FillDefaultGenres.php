<?php

namespace App\Console\Commands;

use App\Models\Genre;
use Illuminate\Console\Command;

class FillDefaultGenres extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fill-default-genres';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed table with default data from storage/categorized-genres.json';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $content = json_decode(file_get_contents(storage_path('categorized-genres.json')), true);

        $merged = collect([]);

        collect($content)
            ->each(function ($genres, $key) use ($merged) {
                collect($genres)
                    ->unique()
                    ->each(fn ($value) => $merged->push([
                        'genre' => $value,
                        'category' => $key,
                    ]));
            });

        $this->withProgressBar($merged, function ($value) {
            Genre::firstOrCreate([
                'name' => $value['genre'],
                'category' => $value['category'],
            ]);
        });

        $this->newLine(2);
        $this->info('Genres saved!');
        $this->newLine();
    }
}
