<?php

namespace App\Console\Commands\Convert;

use App\Jobs\HLS;
use App\Models\Article;
use App\Models\File\Audio;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class ConvertAudiosHls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-audios-hls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert audios which are not in hls format to hls files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (! config('audio.hls_enabled')) {
            $this->warn('HLS is not enabled');

            return;
        }

        $audios = Audio::whereIn('type', Audio::AUDIO_MIME_TYPES)
            ->whereHasMorph('fileable', Article::class)
            ->where('hls_directory', null)
            ->withTrashed();

        $chunkSize = 2;
        $batches = [];

        $audioCount = intval($audios->count() / $chunkSize);
        $bar = $this->output->createProgressBar($audioCount);

        $bar->start();

        $audios->chunk($chunkSize, function (Collection $audios) use ($bar, &$batches) {
            $chunk = $audios->map(fn (Audio $audio) => new HLS\HlsConverter($audio))->toArray();

            $batches[] = Bus::batch($chunk)
                ->allowFailures()
                ->name("Convert Audios to HLS {$bar->getProgress()}/{$bar->getMaxSteps()}");

            $bar->advance();
        });

        if (filled($batches)) {
            Bus::chain($batches)->dispatch();
        }

        $bar->finish();
        $this->newLine(2);
    }
}
