<?php

namespace App\Console\Commands;

use App\Jobs\FixAudioFileFormatJob;
use App\Models\File\Audio;
use App\Models\Morph\File;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log; // Import Builder

class FilesFixAudioFormatCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'files:fix-audio-format';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Finds audio files stored as octet-stream with .bin extension and dispatches jobs to fix them.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting scan for problematic audio files...');

        $folderPrefix = rtrim(Audio::FOLDER, '/').'/'; // e.g., 'audios/'
        $expectedMime = 'application/octet-stream';
        $problemExtension = '.bin';

        // Build the base query
        $query = File::query()
            ->where('type', $expectedMime)
            ->where('filename_disk', 'like', $folderPrefix.'%')
            ->where('filename_disk', 'like', '%'.$problemExtension)
            ->withTrashed();

        $audioCount = $query->count();
        $bar = $this->output->createProgressBar($audioCount);
        $bar->start();

        $chunkSize = 4;
        $batches = [];

        $this->info("Querying for files with MIME '$expectedMime', starting with '$folderPrefix', ending with '$problemExtension'.");
        $this->info("Processing in chunks of $chunkSize files.");

        try {
            // Use chunk to efficiently process potentially large numbers of files
            $query->chunk($chunkSize, function ($files) use ($bar, &$batches) {
                $chunk = $files->map(
                    fn (File $file) => new FixAudioFileFormatJob($file->id)
                )->toArray();

                $batches[] = Bus::batch($chunk)
                    ->allowFailures()
                    ->name("Fix Audio File Format {$bar->getProgress()}/{$bar->getMaxSteps()}");

                $bar->advance();
            });

            if (filled($batches)) {
                Bus::chain($batches)->dispatch();
            }

        } catch (\Exception $e) {
            $this->error('An error occurred during the chunking process: '.$e->getMessage());
            Log::error('Error during FilesFixAudioFormatCommand execution', ['exception' => $e]);

            return Command::FAILURE;
        }

        $bar->finish();
        $this->newLine(2);

        return Command::SUCCESS;
    }
}
