<?php

namespace App\Http\Resources\Playlist;

use App\Http\Resources\FileResource;
use App\Http\Resources\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlaylistResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $auth = $request->user();

        $liked = $this->likes()->where('user_id', $auth->id)->exists();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'liked' => $liked,
            'description' => $this->description,
            'is_system_generated' => $this->is_system_generated,
            'generation_strategy' => $this->generation_strategy,
            'generated_at' => $this->generated_at?->toIso8601String(),
            'user' => new UserResource($this->user),
            'image' => $this->image ? new FileResource($this->image) : null,
            'fallback_image' => $this->when(! $this->image, function () {
                // If the image is not available, return first item image
                $item = $this->articles()->first();
                $image = filled($item?->image) ? $item->image : $item?->channel?->image;

                if (filled($image)) {
                    return new FileResource($image);
                }

                return null;
            }),
            'created_at' => $this->created_at,
        ];
    }
}
