<?php

namespace App\Http\Resources\Likeable;

use App\Http\Resources\Article\ArticleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleLikeableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return (new ArticleResource($this->likeable))->toArray($request);
    }
}
