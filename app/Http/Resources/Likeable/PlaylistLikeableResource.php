<?php

namespace App\Http\Resources\Likeable;

use App\Http\Resources\Playlist\PlaylistResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlaylistLikeableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return (new PlaylistResource($this->likeable))->toArray($request);
    }
}
