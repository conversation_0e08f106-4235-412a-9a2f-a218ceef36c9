<?php

namespace App\Http\Resources\Likeable;

use App\Http\Resources\Channel\ChannelResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChannelLikeableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return (new ChannelResource($this->likeable))->toArray($request);
    }
}
