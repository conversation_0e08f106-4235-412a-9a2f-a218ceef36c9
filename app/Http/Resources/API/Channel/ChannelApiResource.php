<?php

namespace App\Http\Resources\API\Channel;

use App\Http\Resources\API\User\ArtistApiResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\Channel $resource
 */
class ChannelApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'slug' => $this->resource->slug,
            'type' => $this->resource->type->value,
            'articles_count' => $this->resource->articles_count ?? 0,
            'likes_count' => $this->resource->likes_count ?? 0,
            'comments_count' => $this->resource->comments_count ?? 0,
            'is_liked' => $this->when($request->user(), function () use ($request) {
                return $this->resource->likes()->where('user_id', $request->user()->id)->exists();
            }),
            'image' => $this->when($this->resource->image, function () {
                return [
                    'id' => $this->resource->image->id,
                    'url' => $this->resource->image->url,
                    'filename' => $this->resource->image->filename_disk,
                ];
            }),
            'artist' => new ArtistApiResource($this->whenLoaded('user')),
            'genre' => $this->when($this->resource->genre, function () {
                return [
                    'id' => $this->resource->genre->id,
                    'name' => $this->resource->genre->name,
                    'slug' => $this->resource->genre->slug,
                ];
            }),
            'latest_articles' => $this->when($this->resource->relationLoaded('latestArticles'), function () {
                return $this->resource->latestArticles->map(function ($article) {
                    return [
                        'id' => $article->id,
                        'name' => $article->name,
                        'slug' => $article->slug,
                        'duration' => $article->duration,
                        'plays_count' => $article->plays_count ?? 0,
                        'created_at' => $article->created_at?->toISOString(),
                    ];
                });
            }),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
