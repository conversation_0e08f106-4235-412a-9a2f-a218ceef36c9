<?php

namespace App\Http\Resources\API\Article;

use App\Http\Resources\API\Channel\ChannelApiResource;
use App\Http\Resources\API\User\ArtistApiResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\Article $resource
 */
class ArticleApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'year' => $this->resource->year,
            'slug' => $this->resource->slug,
            'duration' => $this->resource->duration,
            'plays_count' => $this->resource->plays_count ?? 0,
            'likes_count' => $this->resource->likes_count ?? 0,
            'comments_count' => $this->resource->comments_count ?? 0,
            'is_liked' => $this->when($request->user(), function () use ($request) {
                return $this->resource->likes()->where('user_id', $request->user()->id)->exists();
            }),
            'image' => $this->when($this->resource->image, function () {
                return [
                    'id' => $this->resource->image->id,
                    'url' => $this->resource->image->url,
                    'filename' => $this->resource->image->filename_disk,
                ];
            }),
            'audio' => $this->when($this->resource->audio, function () {
                return [
                    'id' => $this->resource->audio->id,
                    'url' => $this->resource->audio->url,
                    'filename' => $this->resource->audio->filename_disk,
                    'size' => $this->resource->audio->filesize,
                    'mime_type' => $this->resource->audio->type,
                ];
            }),
            'artist' => new ArtistApiResource($this->whenLoaded('user')),
            'channel' => new ChannelApiResource($this->whenLoaded('channel')),
            'genre' => $this->when($this->resource->genre, function () {
                return [
                    'id' => $this->resource->genre->id,
                    'name' => $this->resource->genre->name,
                    'slug' => $this->resource->genre->slug,
                ];
            }),
            'featuring' => $this->when($this->resource->featurings, function () {
                return $this->resource->featurings->map(function ($featuring) {
                    return new ArtistApiResource($featuring->user);
                });
            }),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
