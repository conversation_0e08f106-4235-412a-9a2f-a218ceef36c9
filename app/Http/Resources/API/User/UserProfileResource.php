<?php

namespace App\Http\Resources\API\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\User $resource
 */
class UserProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
            'role' => $this->resource->role->value,
            'is_artist' => $this->resource->isArtist(),
            'image' => $this->when($this->resource->image, function () {
                return [
                    'id' => $this->resource->image->id,
                    'url' => $this->resource->image->url,
                    'filename' => $this->resource->image->filename_disk,
                ];
            }),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
            'stats' => [
                'followers_count' => $this->resource->followers_count ?? 0,
                'following_count' => $this->resource->following_count ?? 0,
                'channels_count' => $this->when($this->resource->isArtist(), function () {
                    return $this->resource->channels_count ?? 0;
                }),
                'articles_count' => $this->when($this->resource->isArtist(), function () {
                    return $this->resource->articles_count ?? 0;
                }),
            ],
        ];
    }
}
