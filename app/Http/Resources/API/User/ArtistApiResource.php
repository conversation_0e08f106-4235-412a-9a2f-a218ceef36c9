<?php

namespace App\Http\Resources\API\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\User $resource
 */
class ArtistApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'slug' => $this->resource->slug,
            'role' => $this->resource->role->value,
            'is_followed' => $this->when($request->user(), function () use ($request) {
                return $this->resource->followers()->where('follower_id', $request->user()->id)->exists();
            }),
            'image' => $this->when($this->resource->image, function () {
                return [
                    'id' => $this->resource->image->id,
                    'url' => $this->resource->image->url,
                    'filename' => $this->resource->image->filename_disk,
                ];
            }),
            'stats' => [
                'followers_count' => $this->resource->followers_count ?? 0,
                'channels_count' => $this->resource->channels_count ?? 0,
                'articles_count' => $this->resource->articles_count ?? 0,
                'total_plays' => $this->resource->total_plays ?? 0,
            ],
            'latest_channels' => $this->when($this->resource->relationLoaded('latestChannels'), function () {
                return $this->resource->latestChannels->map(function ($channel) {
                    return [
                        'id' => $channel->id,
                        'name' => $channel->name,
                        'slug' => $channel->slug,
                        'type' => $channel->type->value,
                        'articles_count' => $channel->articles_count ?? 0,
                        'created_at' => $channel->created_at?->toISOString(),
                    ];
                });
            }),
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
