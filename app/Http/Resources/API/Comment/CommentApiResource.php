<?php

namespace App\Http\Resources\API\Comment;

use App\Http\Resources\API\User\UserProfileResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\Morph\Comment $resource
 */
class CommentApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'comment' => $this->resource->comment,
            'user' => new UserProfileResource($this->whenLoaded('user')),
            'can_edit' => $this->when($request->user(), function () use ($request) {
                return $this->resource->user_id === $request->user()->id;
            }),
            'can_delete' => $this->when($request->user(), function () use ($request) {
                return $this->resource->user_id === $request->user()->id;
            }),
            'commentable' => [
                'type' => $this->resource->commentable_type,
                'id' => $this->resource->commentable_id,
            ],
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
