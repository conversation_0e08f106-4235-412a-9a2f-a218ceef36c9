<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FileResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string|null
     */
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var \App\Models\Morph\File */
        $file = $this;

        return [
            'id' => $file->id,
            'title' => $file->title,
            'filename_disk' => $file->filename_disk,
            'filename_download' => $file->filename_download,
            'type' => $file->type,
            'filesize' => $file->filesize,
            'url' => $file->url,
            'width' => $file->width,
            'height' => $file->height,
            'duration' => $file->duration,
            'tags' => $file->tags,
            'metadata' => $file->metadata,
            'description' => $file->description,
            'hls_directory' => $file->hls_directory,
            'hls_enabled' => config('audio.hls_enabled'),
        ];
    }
}
