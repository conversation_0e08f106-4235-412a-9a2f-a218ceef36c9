<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArtistShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /**
         * @var \App\Models\User
         */
        $user = $this;

        // Get active competitions the artist is participating in
        $activeCompetitions = $user->competitionEntries
            ->filter(function ($entry) {
                return $entry->competition && $entry->status === 'approved';
            })
            ->map(function ($entry) {
                return [
                    'id' => $entry->competition->id,
                    'name' => $entry->competition->name,
                    'type' => $entry->competition->type,
                    'stage' => $entry->competition->stage,
                    'status' => $entry->status,
                    'entry_date' => $entry->entry_date,
                    'phase_status' => $entry->competition->phase_status ?? 'unknown',
                ];
            })
            ->values();

        return array_merge((new ArtistResource($this))->toArray($request), [
            'monthly_plays' => $user->monthly_plays,
            'monthly_listeners' => $user->monthly_listeners,
            'description' => $user->description,
            'active_competitions' => $activeCompetitions,
        ]);
    }
}
