<?php

namespace App\Http\Resources\User;

use App\Enums\UserRole;
use App\Http\Resources\FileResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArtistResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var \App\Models\User */
        $user = $this;

        return [
            'id' => $user->id,
            'name' => ucwords($user->name),
            'role' => $user->isAdmin() ? UserRole::SINGER : $user->role,
            'image' => $user->image ? new FileResource($user->image) : null,
            'followers_count' => $user->isArtist() ? number_format($user->followers->count()) : null,
            'followings_count' => number_format($user->followings->count()),
            'created_at' => $user->created_at,
        ];
    }
}
