<?php

namespace App\Http\Resources\User;

use App\Http\Resources\FileResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * The "data" wrapper that should be applied.
     *
     * @var string|null
     */
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var \App\Models\User */
        $user = $this;

        return [
            'id' => $user->id,
            'name' => ucwords($user->name),
            'email' => $user->email,
            'has_verified_email' => $user->hasVerifiedEmail(),
            'role' => $user->role,
            'phone' => $user->phone,
            'country_name' => $user->country_name,
            'country_code' => $user->country_code,
            'timezone' => $user->timezone,
            'city' => $user->city,
            'region' => $user->region,
            'description' => $user->description,
            'image' => $user->image ? new FileResource($user->image) : null,
            'followers_count' => $user->isArtist() ? number_format($user->followers()->count()) : null,
            'followings_count' => number_format($user->followings()->count()),
            'created_at' => $user->created_at,
        ];
    }
}
