<?php

namespace App\Http\Resources\Channel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class FeaturingChannelCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->collection = $this->collection
            ->filter(fn ($featuring) => filled($featuring->channel_id))
            ->collect();

        return parent::toArray($request);

    }
}
