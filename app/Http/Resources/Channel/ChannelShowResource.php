<?php

namespace App\Http\Resources\Channel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChannelShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return array_merge((new ChannelResource($this))->toArray($request), [
            'description' => $this->description,
            'unique_query' => $this->unique_query,
            'likes_count' => $this->likes->count(),
            'downloads_count' => $this->downloads()->count(),
            'comments_count' => $this->comments()->count(),
        ]);
    }
}
