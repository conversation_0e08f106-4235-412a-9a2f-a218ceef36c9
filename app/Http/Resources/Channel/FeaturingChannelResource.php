<?php

namespace App\Http\Resources\Channel;

use App\Models\Channel;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FeaturingChannelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $channel = Channel::find($this->channel_id);

        return (new ChannelResource($channel))->toArray($request);
    }
}
