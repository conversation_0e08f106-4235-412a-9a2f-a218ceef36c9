<?php

namespace App\Http\Resources\Channel;

use App\Http\Resources\FileResource;
use App\Http\Resources\Genre\GenreResource;
use App\Http\Resources\User\ArtistResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChannelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $auth = $request->user();

        /**
         * @var \App\Models\Channel
         */
        $channel = $this;

        $liked = $channel->likes()->where('user_id', $auth?->id)->exists();

        return [
            'id' => $channel->id,
            'name' => ucwords($channel->name),
            'type' => $channel->type,
            'liked' => $liked,
            'articles_count' => $channel->articles()->count(),
            'user' => new ArtistResource($channel->user),
            'genre' => new GenreResource($channel->genre),
            'image' => $channel->image ? new FileResource($channel->image) : null,
            'created_at' => $channel->created_at,
        ];
    }
}
