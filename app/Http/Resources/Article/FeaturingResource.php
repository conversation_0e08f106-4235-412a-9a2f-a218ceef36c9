<?php

namespace App\Http\Resources\Article;

use App\Http\Resources\User\ArtistResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FeaturingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return (new ArtistResource($this->user))->toArray($request);
    }
}
