<?php

namespace App\Http\Resources\Article;

use App\Http\Resources\Channel\ChannelResource;
use App\Http\Resources\FileResource;
use App\Http\Resources\User\ArtistResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $auth = $request->user();

        /**
         * @var \App\Models\Article
         */
        $article = $this;

        $liked = $article?->likes()->where('user_id', $auth->id)->count();

        return [
            'id' => $article->id,
            'name' => ucwords($article->name),
            'description' => $article->description,
            'likes_count' => $article->likes->count(),
            'liked' => boolval($liked),
            'channel' => new ChannelResource($this->channel),
            'audio' => $this->audio ? new FileResource($this->audio) : null,
            'image' => $this->image ? new FileResource($this->image) : null,
            'user' => new ArtistResource($this->user),
            'featurings' => new FeaturingCollection($this->featurings),
            'rank' => $this->whenPivotLoaded('playlist_items', function () {
                return $this->pivot->rank;
            }),
            'created_at' => $article->created_at,
        ];
    }
}
