<?php

namespace App\Http\Controllers;

use App\Services\PrometheusMetricsService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class MetricsController extends Controller
{
    private PrometheusMetricsService $metricsService;

    public function __construct(PrometheusMetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Generate and return Prometheus metrics
     */
    public function __invoke(Request $request): Response
    {
        try {
            $metrics = $this->metricsService->generateMetrics();

            return response($metrics, 200, [
                'Content-Type' => 'text/plain; version=0.0.4; charset=utf-8',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to generate Prometheus metrics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response('# Error generating metrics', 500, [
                'Content-Type' => 'text/plain; charset=utf-8',
            ]);
        }
    }
}
