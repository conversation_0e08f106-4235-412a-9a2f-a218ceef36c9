<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use App\Models\Article;
use Illuminate\Http\Request;

/**
 * @group Plays
 *
 * APIs for tracking music plays/listens
 */
class PlayController extends Controller
{
    /**
     * Record article play
     *
     * Records a play/listen event for a specific article. This is used for analytics and play count tracking.
     */
    public function recordArticlePlay(Article $article, Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        // Record the play - user as listener, article's user as artist
        $user->playings()->create([
            'user_id' => $article->user_id, // The artist who gets credit for the play
            'article_id' => $article->id,
        ]);

        return SuccessResource::success(
            'Play recorded successfully',
            [
                'article_id' => $article->id,
                'plays_count' => $article->fresh()->plays_count ?? 0,
            ]
        );
    }
}
