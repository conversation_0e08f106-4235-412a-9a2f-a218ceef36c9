<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Morph\Like;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

/**
 * @group Likes
 *
 * APIs for managing likes on articles and channels
 */
class LikeController extends Controller
{
    /**
     * Toggle article like
     *
     * Toggles the like status for a specific article. If already liked, it will unlike; if not liked, it will like.
     */
    public function toggleArticleLike(Article $article, Request $request): SuccessResource
    {
        $result = $this->toggleLike($article, $request);

        return SuccessResource::success(
            $result['action'] === 'liked' ? 'Article liked successfully' : 'Article unliked successfully',
            [
                'is_liked' => $result['is_liked'],
                'likes_count' => $article->fresh()->likes_count ?? 0,
            ]
        );
    }

    /**
     * Toggle channel like
     *
     * Toggles the like status for a specific channel. If already liked, it will unlike; if not liked, it will like.
     */
    public function toggleChannelLike(Channel $channel, Request $request): SuccessResource
    {
        $result = $this->toggleLike($channel, $request);

        return SuccessResource::success(
            $result['action'] === 'liked' ? 'Channel liked successfully' : 'Channel unliked successfully',
            [
                'is_liked' => $result['is_liked'],
                'likes_count' => $channel->fresh()->likes_count ?? 0,
            ]
        );
    }

    /**
     * Toggle like for any likeable model
     */
    private function toggleLike(Model $model, Request $request): array
    {
        $user = $request->user();

        $liked = $model->likes()->where('user_id', $user->id)->exists();

        if ($liked) {
            // Unlike: Remove all likes from this user for this model
            $likes = $model->likes()->where('user_id', $user->id)->get();

            collect($likes)->each(fn (Like $like) => $like->delete());

            return [
                'action' => 'unliked',
                'is_liked' => false,
            ];
        } else {
            // Like: Create new like
            $model->likes()->create(['user_id' => $user->id]);

            return [
                'action' => 'liked',
                'is_liked' => true,
            ];
        }
    }
}
