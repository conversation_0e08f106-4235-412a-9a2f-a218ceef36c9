<?php

namespace App\Http\Controllers\API;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\Settings\CreateArtistRequestRequest;
use App\Http\Requests\API\Settings\UpdateProfileImageRequest;
use App\Http\Resources\API\Common\SuccessResource;
use App\Http\Resources\API\User\UserProfileResource;
use App\Models\ArtistRequest;
use App\Models\File\Image;
use App\Models\User;
use Illuminate\Http\Request;

/**
 * @group Settings
 *
 * APIs for managing user account settings
 */
class SettingsController extends Controller
{
    use HasFileRelation;

    /**
     * Get account settings
     *
     * Returns account settings and permissions for the authenticated user.
     */
    public function account(Request $request): array
    {
        $user = $request->user();
        $canCreateArtistRequest = $user->can('create', ArtistRequest::class);

        return [
            'user' => new UserProfileResource($user),
            'permissions' => [
                'can_create_artist_request' => $canCreateArtistRequest,
            ],
            'artist_roles' => User::getOnlyArtistTypes(),
        ];
    }

    /**
     * Update profile image
     *
     * Updates the profile image for the authenticated user.
     */
    public function updateProfileImage(UpdateProfileImageRequest $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

        // Remove the previous image
        $user->image?->forceDelete();

        // Create the new image
        $user->image()->create(
            $this->fillFileRequiredFields(
                disk: Image::getDisk(),
                data: ['filename_disk' => $path]
            )
        );

        $user->refresh();

        return SuccessResource::success(
            'Profile image updated successfully',
            [
                'user' => new UserProfileResource($user),
            ]
        );
    }

    /**
     * Create artist request
     *
     * Creates a request for the user to become an artist on the platform.
     */
    public function createArtistRequest(CreateArtistRequestRequest $request): SuccessResource
    {
        $this->authorize('create', ArtistRequest::class);

        $validated = $request->validated();

        /** @var User */
        $user = $request->user();

        $artistRequest = $user->artistRequest()->create($validated);

        return SuccessResource::success(
            'Artist request submitted successfully',
            [
                'request_id' => $artistRequest->id,
                'status' => 'pending',
            ]
        );
    }
}
