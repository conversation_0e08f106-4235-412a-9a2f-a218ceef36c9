<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use Illuminate\Http\Request;

/**
 * @group Notifications
 *
 * APIs for managing user notifications
 */
class NotificationController extends Controller
{
    /**
     * Clear all notifications
     *
     * Clears all notifications for the authenticated user.
     */
    public function clear(Request $request): SuccessResource
    {
        $user = $request->user();
        $clearedCount = $user->notifications()->count();

        $user->notifications()->delete();

        return SuccessResource::success(
            'All notifications cleared successfully',
            [
                'cleared_count' => $clearedCount,
            ]
        );
    }

    /**
     * Delete specific notification
     *
     * Deletes a specific notification by its ID.
     */
    public function destroy(string $notificationId, Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->delete();

        return SuccessResource::success('Notification deleted successfully');
    }
}
