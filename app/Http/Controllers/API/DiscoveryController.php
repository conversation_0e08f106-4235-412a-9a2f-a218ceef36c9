<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\Playlist\PlaylistCollection;
use App\Http\Resources\User;
use App\Models\Playlist\Playlist;
use App\Repositories\RecommendationRepository;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

/**
 * APIs for content discovery and recommendations
 */
#[Group('Discovery', weight: 2)]
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     *
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     * The response structure varies based on user authentication status and available data.
     *
     * For authenticated users with personalized playlists:
     * - recommendations.source: "personalized_playlists"
     * - recommendations.items: PlaylistCollection with user's system-generated playlists
     * - recently_played: ChannelCollection with user's recently played albums
     * - top_charts: ArticleCollection with globally popular tracks
     * - top_artists: ArtistCollection with top artists by play count
     *
     * For authenticated users without playlists (fallback):
     * - recommendations.source: "global_popular_fallback"
     * - recommendations.items: Collection with manually structured fallback playlist containing popular tracks
     * - recently_played: ChannelCollection (may be empty)
     * - top_charts: ArticleCollection (may be empty)
     * - top_artists: ArtistCollection (may be empty)
     *
     * For guest users:
     * - recommendations.source: "no_recommendations_available"
     * - recommendations.items: Empty collection
     * - recently_played: Empty array
     * - top_charts: Empty array
     * - top_artists: Empty array
     *
     * @return array{
     *     recommendations: array{playlists: Collection<int, Playlist>|PlaylistCollection, source: string}|array{playlists: Collection<int, Playlist>, source: string}|array{playlists: Collection<int, Playlist>|PlaylistCollection, source: string},
     *     recently_played: \App\Http\Resources\Channel\ChannelResource[]|array,
     *     top_charts: \App\Http\Resources\Article\ArticleResource[]|array,
     *     top_artists: User\ArtistResource[]|array
     * }
     */
    public function index(Request $request): array
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();
        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topCharts = [];
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return [
            'recommendations' => $recommendations,
            'recently_played' => $recentlyPlayed,
            'top_charts' => $topCharts,
            'top_artists' => $topArtists,
        ];
    }
}
