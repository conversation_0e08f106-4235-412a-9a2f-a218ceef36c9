<?php

namespace App\Http\Controllers\API;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\Channels\CreateChannelRequest;
use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelApiResource;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Models\Channel;
use App\Models\File\Image;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;

/**
 * @group Channels
 *
 * APIs for managing music channels/albums
 */
class ChannelController extends Controller
{
    use HasFileRelation;

    public function __construct(
        private ChannelRepository $channelRepository
    ) {}

    /**
     * Get all channels
     *
     * Returns a paginated list of all channels.
     */
    public function index(Request $request): ChannelCollection
    {
        $limit = $request->input('limit', 20);
        $channels = $this->channelRepository->getRandomChannels($limit);

        return new ChannelCollection($channels);
    }

    /**
     * Get channel details
     *
     * Returns detailed information about a specific channel.
     */
    public function show(Channel $channel): ChannelApiResource
    {
        $channel->load(['user', 'genre', 'image', 'latestArticles']);

        return new ChannelApiResource($channel);
    }

    /**
     * Get channel articles
     *
     * Returns all articles/tracks in a specific channel.
     */
    public function articles(Channel $channel, Request $request): ArticleCollection
    {
        $limit = $request->input('limit', 20);
        $articles = $channel->articles()
            ->with(['user', 'image', 'audio', 'genre'])
            ->latest()
            ->paginate($limit);

        return new ArticleCollection($articles);
    }

    /**
     * Create a new channel
     *
     * Creates a new music channel/album with optional cover image.
     */
    public function store(CreateChannelRequest $request): ChannelApiResource
    {
        $data = $request->validated();

        $channel = Channel::create($data);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $channel->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $channel->refresh();
        $channel->load(['user', 'genre', 'image']);

        return new ChannelApiResource($channel);
    }
}
