<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Auth\LoginRequest;
use App\Http\Resources\API\AuthTokenResource;
use App\Models\User;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

/**
 * @group Authentication
 *
 * APIs for user authentication and token management
 */
#[Group('Authentication', weight: 1)]
class IssueAuthTokenController extends Controller
{
    /**
     * Issue authentication token
     *
     * Authenticates a user and returns an API token for subsequent requests.
     */
    public function __invoke(LoginRequest $request): AuthTokenResource
    {
        $validated = $request->validated();

        $user = User::where('email', $validated['email'])->first();

        if (! $user || ! Hash::check($validated['password'], $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $token = $user->createToken($validated['device_name']);

        return new AuthTokenResource($token);
    }
}
