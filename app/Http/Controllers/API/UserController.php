<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\User\UserProfileResource;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for managing user profiles and authentication status
 */
#[Group('User Management', weight: 1)]
class UserController extends Controller
{
    /**
     * Get authenticated user profile
     *
     * Returns the profile information of the currently authenticated user.
     */
    public function profile(Request $request): UserProfileResource
    {
        return new UserProfileResource($request->user());
    }
}
