<?php

namespace App\Http\Controllers\API;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\Users\CreateArtistRequest;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\User\ArtistApiResource;
use App\Http\Resources\API\User\ArtistCollection;
use App\Models\File\Image;
use App\Models\User;
use App\Repositories\ChannelRepository;
use App\Repositories\FeaturingRepository;
use App\Repositories\UserRepository;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;
use Str;

/**
 * APIs for managing artists and their content
 */
#[Group('Artists', weight: 4)]
class ArtistController extends Controller
{
    use HasFileRelation;

    public function __construct(
        private UserRepository $userRepository,
        private ChannelRepository $channelRepository,
        private FeaturingRepository $featuringRepository,
    ) {}

    /**
     * Get all artists
     *
     * Returns a paginated list of all artists.
     */
    public function index(Request $request): ArtistCollection
    {
        $limit = $request->input('limit', 20);
        $artists = $this->userRepository->getRandomArtists($limit);

        return new ArtistCollection($artists);
    }

    /**
     * Get artist details
     *
     * Returns detailed information about a specific artist.
     */
    public function show(User $user, Request $request): ArtistApiResource
    {
        if (! $user->isArtist()) {
            abort(404, 'Artist not found');
        }

        $user->load(['image', 'latestChannels']);

        return new ArtistApiResource($user);
    }

    /**
     * Get artist channels
     *
     * Returns all channels/albums created by a specific artist.
     */
    public function channels(User $user, Request $request): ChannelCollection
    {
        if (! $user->isArtist()) {
            abort(404, 'Artist not found');
        }

        $limit = $request->input('limit', 20);
        $channels = $this->channelRepository->getArtistChannels($user, $limit);

        return new ChannelCollection($channels);
    }

    /**
     * Create a new artist
     *
     * Creates a new artist account with optional profile image.
     */
    public function store(CreateArtistRequest $request): ArtistApiResource
    {
        $data = $request->validated();

        $user = User::create([
            ...$data,
            'password' => $data['password'] ?? Str::random(10),
        ]);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $user->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $user->refresh();
        $user->load(['image']);

        return new ArtistApiResource($user);
    }

    /**
     * Search artists by name
     *
     * Search for artists by their name.
     */
    public function searchByName(Request $request): ArtistApiResource
    {
        $request->validate([
            'q' => 'required|string|min:1',
        ]);

        $name = $request->query('q');
        $user = User::where('name', 'like', trim($name))->first();

        if (! $user) {
            abort(404, 'Artist not found');
        }

        return new ArtistApiResource($user);
    }

    /**
     * Search artists by email
     *
     * Search for artists by their email address.
     */
    public function searchByEmail(Request $request): ArtistApiResource
    {
        $request->validate([
            'q' => 'required|email',
        ]);

        $email = $request->query('q');
        $user = User::where('email', trim($email))->first();

        if (! $user) {
            abort(404, 'Artist not found');
        }

        return new ArtistApiResource($user);
    }
}
