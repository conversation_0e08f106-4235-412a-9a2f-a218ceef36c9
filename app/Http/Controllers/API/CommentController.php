<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Comments\CreateCommentRequest;
use App\Http\Requests\API\Comments\UpdateCommentRequest;
use App\Http\Resources\API\Comment\CommentApiResource;
use App\Http\Resources\API\Comment\CommentCollection;
use App\Http\Resources\API\Common\SuccessResource;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Morph\Comment;
use Illuminate\Http\Request;

/**
 * @group Comments
 *
 * APIs for managing comments on articles and channels
 */
class CommentController extends Controller
{
    /**
     * Get article comments
     *
     * Returns paginated comments for a specific article.
     */
    public function articleComments(Article $article): CommentCollection
    {
        $comments = $article
            ->comments()
            ->with('user')
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Get channel comments
     *
     * Returns paginated comments for a specific channel.
     */
    public function channelComments(Channel $channel): CommentCollection
    {
        $comments = $channel
            ->comments()
            ->with('user')
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Create article comment
     *
     * Creates a new comment on a specific article.
     */
    public function storeArticleComment(Article $article, CreateCommentRequest $request): CommentApiResource
    {
        $data = $request->validated();

        $comment = $article->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Create channel comment
     *
     * Creates a new comment on a specific channel.     */
    public function storeChannelComment(Channel $channel, CreateCommentRequest $request): CommentApiResource
    {
        $data = $request->validated();

        $comment = $channel->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Update comment
     *
     * Updates an existing comment. Only the comment author can update their comment.
     */
    public function update(Comment $comment, UpdateCommentRequest $request): CommentApiResource
    {
        if ($comment->user_id !== $request->user()->id) {
            abort(401, 'Unauthorized');
        }

        $data = $request->validated();
        $comment->fill($data)->save();
        $comment->refresh();
        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Delete comment
     *
     * Deletes an existing comment. Only the comment author can delete their comment.
     */
    public function destroy(Comment $comment, Request $request): SuccessResource
    {
        if ($comment->user_id !== $request->user()->id) {
            abort(401, 'Unauthorized');
        }

        $comment->delete();

        return SuccessResource::success('Comment deleted successfully');
    }
}
