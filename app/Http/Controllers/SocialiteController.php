<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class SocialiteController extends Controller
{
    private array $providers = [
        'google',
    ];

    /**
     * Redirect the user to the provider authentication page.
     */
    public function redirect(string $provider)
    {
        abort_if(! in_array($provider, $this->providers), 400);

        return Socialite::driver($provider)->redirect();
    }

    /**
     * Obtain the user information from the provider.
     */
    public function callback(string $provider)
    {
        abort_if(! in_array($provider, $this->providers), 400);

        $googleUser = Socialite::driver($provider)->user();
        $user = User::where('email', $googleUser->getEmail())->first();

        if ($user && $user->provider !== $provider) {
            return redirect(RouteServiceProvider::HOME)
                ->with('error', 'Email already exists with another provider');
        }

        $user = User::updateOrCreate(
            [
                'email' => $googleUser->getEmail(),
                'provider' => $provider,
            ],
            [
                'name' => $googleUser->getName(),
                'provider_id' => $googleUser->getId(),
                'provider_token' => $googleUser->token,
                'email_verified_at' => $provider === 'google' ? now() : null,
                'password' => \Str::random(10),
            ]
        );

        Auth::login($user, remember: true);

        return redirect(RouteServiceProvider::HOME);
    }
}
