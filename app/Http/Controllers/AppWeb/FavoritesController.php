<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class FavoritesController extends Controller
{
    use HasRetriever;

    public function __construct(
        private ArticleRepository $articleRepository,
        private ChannelRepository $channelRepository,
        private UserRepository $userRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        return inertia('app/favorites', $this->retrieve($request, [
            'articles' => [$this->articleRepository, 'getUserLikesArticles'],
            'channels' => [$this->channelRepository, 'getUserLikesChannels'],
            'followings' => [$this->userRepository, 'getUserFollowings'],
        ]));
    }

    /**
     * Handle the incoming request.
     */
    public function favoritesArticles(Request $request)
    {
        $limit = (int) $request->query('limit', 100);

        return $this->articleRepository->getUserFavoriteArticles(
            $request->user(),
            $limit,
        );
    }
}
