<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;

class PodcastsController extends Controller
{
    use HasRetriever;

    public function __construct(
        private ChannelRepository $channelRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        return inertia('app/podcasts', $this->retrieve($request, [
            'channels' => [$this->channelRepository, 'getChannelTypePodcasts'],
        ]));
    }
}
