<?php

namespace App\Http\Controllers\AppWeb;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Resources\Article\ArticleResource;
use App\Models\Article;
use App\Models\File\Audio;
use App\Models\File\Image;
use App\Repositories\FeaturingRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\File;

class ArticlesController extends Controller
{
    use HasFileRelation;

    public function __construct(
        private FeaturingRepository $featuringRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function view(Article $article, Request $request)
    {
        return new ArticleResource($article);
    }

    public function create(Request $request)
    {
        // Prepare valid MIME types for audio validation
        $mimeTypes = $this->prepareAudioMimeTypes($request);

        // Validate request data
        $data = $this->validateArticleData($request, $mimeTypes);

        // Create the article
        $article = Article::create($data);

        // Handle file uploads
        $this->handleImageUpload($request, $article);
        $this->handleAudioUpload($request, $article);

        // Handle featured artists
        $this->handleFeaturedArtists($request, $data, $article);

        $article->refresh();

        return new ArticleResource($article);
    }

    /**
     * Prepare the list of valid audio MIME types
     */
    private function prepareAudioMimeTypes(Request $request): array
    {
        $mimeTypes = [...Audio::AUDIO_MIME_TYPES];

        $audioFile = $request->file('audio');
        if (! $audioFile) {
            return $mimeTypes;
        }

        $fileMimeType = $audioFile->getMimeType();
        $clientMimeType = $audioFile->getClientMimeType();

        if ($fileMimeType === 'application/octet-stream' && $clientMimeType === Audio::AUDIO_MIME_TYPES[0]) {
            $mimeTypes[] = 'application/octet-stream';
        }

        return $mimeTypes;
    }

    /**
     * Validate article data
     */
    private function validateArticleData(Request $request, array $mimeTypes): array
    {
        return $request->validate([
            'name' => ['required'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'year' => ['nullable', 'numeric', 'digits:4'],
            'audio' => [
                'required',
                File::types($mimeTypes)
                    ->min(Audio::MIN_SIZE)
                    ->max(Audio::MAX_SIZE),
            ],
            'featuring' => [
                'nullable',
                'array',
            ],
            'description' => ['nullable'],
            'channel_id' => ['required', "exists:App\Models\Channel,id"],
            'genre_id' => ['required', "exists:App\Models\Genre,id"],
            'user_id' => ['required', "exists:App\Models\User,id"],
        ]);
    }

    /**
     * Handle image upload
     */
    private function handleImageUpload(Request $request, Article $article): void
    {
        if (! $request->hasFile('image')) {
            return;
        }

        $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

        $article->image()->create(
            $this->fillFileRequiredFields(
                disk: Image::getDisk(),
                data: ['filename_disk' => $path]
            )
        );
    }

    /**
     * Handle audio upload
     */
    private function handleAudioUpload(Request $request, Article $article): void
    {
        if (! $request->hasFile('audio')) {
            return;
        }

        $path = $request->file('audio')->store(Audio::FOLDER, Audio::getDisk());

        $article->audio()->create(
            $this->fillFileRequiredFields(
                disk: Audio::getDisk(),
                data: ['filename_disk' => $path]
            )
        );
    }

    /**
     * Handle featured artists
     */
    private function handleFeaturedArtists(Request $request, array $data, Article $article): void
    {
        if (! $request->has('featuring') || ! is_array($data['featuring'])) {
            return;
        }

        $this->featuringRepository->createManyByNames($article, $data['featuring']);
    }
}
