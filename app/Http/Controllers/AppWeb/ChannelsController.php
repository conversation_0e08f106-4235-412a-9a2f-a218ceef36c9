<?php

namespace App\Http\Controllers\AppWeb;

use App\Enums\ChannelType;
use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Article\ArticleCollection;
use App\Http\Resources\Article\ArticleResource;
use App\Http\Resources\Channel\ChannelCollection;
use App\Http\Resources\Channel\ChannelResource;
use App\Media\TmpFile;
use App\Models\Article;
use App\Models\Channel;
use App\Models\File\Image;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class ChannelsController extends Controller
{
    use HasFileRelation, HasRetriever;

    public function __construct(
        private ChannelRepository $channelRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $resources = $this->retrieve($request, [
            'channels' => [$this->channelRepository, 'getRandomChannels'],
        ]);

        return inertia('app/channels/index', $resources);
    }

    /**
     * Handle the incoming request.
     */
    public function show(Channel $channel, Request $request)
    {
        $resource = $this->retrieve([$channel, $request], [
            'channel' => [$this->channelRepository, 'getChannelDetails'],
            'more_channels' => [$this->channelRepository, 'getMoreChannelsFromArtist'],
            'articles' => [$this, 'articles'],
        ]);

        return inertia('app/channels/show', $resource);
    }

    public function create(Request $request)
    {
        $data = $request->validate([
            'name' => ['required'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'description' => ['nullable'],
            'type' => ['required', Rule::enum(ChannelType::class)],
            'genre_id' => ['required', "exists:App\Models\Genre,id"],
            'user_id' => ['required', "exists:App\Models\User,id"],
        ]);

        $channel = Channel::create([
            ...$data,
            'type' => ChannelType::from($data['type']),
        ]);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $channel->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $channel->refresh();

        return new ChannelResource($channel);
    }

    /**
     * Handle the incoming request.
     */
    public function download(Channel $channel, Request $request)
    {
        $user = $request->user();
        $articles = $channel->articles->filter(fn (Article $article) => $article->audio);

        $zip = new \ZipArchive;
        $fileName = Storage::disk('public')->path(Str::kebab($channel->name).'.zip');

        $tmpFiles = collect();

        if ($zip->open($fileName, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {

            $articles->each(function (Article $article) use (&$zip, &$tmpFiles) {
                $audio = $article->audio;

                if (! Storage::disk($audio->storage)->exists($audio->filename_disk)) {
                    return;
                }

                $tmpFile = new TmpFile($audio);

                $tmpFiles->push($tmpFile);

                $zip->addFile(
                    $tmpFile->path(),
                    $article->name.'.'.pathinfo($audio->filename_disk, PATHINFO_EXTENSION)
                );
            });

            // Add empty file if no audio
            if ($zip->count() === 0) {
                $zip->addFromString('no-audio.txt', 'sorry, no audio');
            }

            $zip->close();
        }

        // Delete tmp files
        $tmpFiles->each(fn (TmpFile $tmpFile) => $tmpFile->close());

        app()->terminating(fn () => unlink($fileName));

        $channel->downloads()->create(['user_id' => $user->id]);

        return response()->download($fileName);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function view(Channel $channel)
    {
        return new ChannelResource($channel);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function articles(Channel $channel)
    {
        $articles = $channel->articles()->with(ArticleRepository::RELATIONS)->get();

        return new ArticleCollection($articles);
    }

    /**
     * Handle the incoming request.
     */
    public function getChannelArticleByName(Channel $channel, Request $request)
    {
        $name = $request->query('q');

        $article = $channel->articles()->where('name', 'like', trim($name))->firstOrFail();

        return new ArticleResource($article);
    }

    public function updateImage(Request $request)
    {
        $data = $request->validate([
            'channel_id' => ['required', "exists:App\Models\Channel,id"],
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        $channel = Channel::find($data['channel_id']);

        if ($request->hasFile('image')) {
            $channel->image()->forceDelete();

            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $channel->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $channel->refresh();

        return new ChannelResource($channel);
    }

    /**
     * Handle the incoming request.
     */
    public function getChannelsWithoutImage()
    {
        $channels = Channel::whereHas('image', null, '!=')->get();

        return new ChannelCollection($channels);
    }
}
