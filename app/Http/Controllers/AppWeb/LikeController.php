<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Morph\Like;
use App\Models\Playlist\Playlist;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class LikeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function channel(Channel $channel, Request $request)
    {
        $this->like($channel, $request);

        return back();
    }

    /**
     * Handle the incoming request.
     */
    public function article(Article $article, Request $request)
    {
        $this->like($article, $request);

        return back();
    }

    /**
     * Handle the incoming request.
     */
    public function playlist(Playlist $playlist, Request $request)
    {
        $this->like($playlist, $request);

        return back();
    }

    /**
     *  Add or like from item
     */
    private function like(Model $model, Request $request)
    {
        $user = $request->user();

        $liked = $model->likes()->where('user_id', $user->id)
            ->exists();

        if ($liked) {
            $likes = $model->likes()->where('user_id', $user->id)->get();

            collect($likes)
                ->each(fn (Like $like) => $like->delete());

        } else {

            $model->likes()->create(['user_id' => $user->id]);
        }
    }
}
