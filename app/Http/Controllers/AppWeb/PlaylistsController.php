<?php

namespace App\Http\Controllers\AppWeb;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Article\ArticleResource;
use App\Http\Resources\Playlist\PlaylistResource;
use App\Models\File\Image;
use App\Models\Playlist\Playlist;
use App\Repositories\ArticleRepository;
use App\Repositories\PlaylistRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\File;

class PlaylistsController extends Controller
{
    use HasFileRelation, HasRetriever;

    public function __construct(
        private PlaylistRepository $playlistRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function show(string $playlist, Request $request)
    {
        $playlist = Playlist::find($playlist);
        if ($playlist === null) {
            return redirect()->route('app.discover');
        }

        $resource = $this->retrieve([$playlist, $request], [
            'playlist' => [$this->playlistRepository, 'getPlaylistDetails'],
            'articles' => [$this, 'articles'],
        ]);

        return inertia('app/playlists/show', $resource);
    }

    public function create(Request $request)
    {
        $data = $request->validate([
            'name' => ['required'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'description' => ['nullable'],
        ]);

        $playlist = Playlist::create($data);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $playlist->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $playlist->refresh();

        return new PlaylistResource($playlist);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function view(Playlist $playlist)
    {
        return new PlaylistResource($playlist);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function articles(Playlist $playlist)
    {
        $articles = $playlist->articles()->with(ArticleRepository::RELATIONS)->get();

        return ArticleResource::collection($articles);
    }

    public function updateImage(Request $request)
    {
        $data = $request->validate([
            'playlist_id' => ['required', 'exists:playlists,id'],
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        $playlist = Playlist::find($data['playlist_id']);

        if ($request->hasFile('image')) {
            $playlist->image()->forceDelete();

            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $playlist->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $playlist->refresh();

        return new PlaylistResource($playlist);
    }
}
