<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\Resources\Comment\CommentCollection;
use App\Http\Resources\Comment\CommentResource;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Morph\Comment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CommentsController extends Controller
{
    /**
     * Update comment request
     */
    public function update(Comment $comment, Request $request): CommentResource
    {
        $data = $request->validate([
            'comment' => ['required', 'string', 'min:2', 'max:1000'],
        ]);

        abort_if($comment->user->id !== $request->user()->id, 401);

        $comment->fill($data)->save();

        $comment->refresh();

        return new CommentResource($comment);
    }

    /**
     * Delete comment request
     */
    public function delete(Comment $comment, Request $request): JsonResponse
    {
        abort_if($comment->user->id !== $request->user()->id, 401);

        $comment->delete();

        return response()->json(['status' => true]);
    }

    /**
     * Get article comments (Handle the incoming request).
     */
    public function article(Article $article): CommentCollection
    {
        $comments = $article
            ->comments()
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Get channel comments (Handle the incoming request).
     */
    public function channel(Channel $channel): CommentCollection
    {
        $comments = $channel
            ->comments()
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Create article comment request
     */
    public function articleCommentStore(Article $article, Request $request): CommentResource
    {
        $data = $request->validate([
            'comment' => ['required', 'string', 'min:2', 'max:1000'],
        ]);

        $comment = $article->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        return new CommentResource($comment);
    }

    /**
     * Create channel comment request
     */
    public function channelCommentStore(Channel $channel, Request $request): CommentResource
    {
        $data = $request->validate([
            'comment' => ['required', 'string', 'min:2', 'max:1000'],
        ]);

        $comment = $channel->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        return new CommentResource($comment);
    }
}
