<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\LegalDocument;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use League\CommonMark\CommonMarkConverter;

class LegalDocumentController extends Controller
{
    /**
     * Get active legal documents for competition entry.
     */
    public function getActiveDocuments(): JsonResponse
    {
        $termsOfService = LegalDocument::getActiveTermsOfService();
        $privacyPolicy = LegalDocument::getActivePrivacyPolicy();
        $competitionRules = LegalDocument::getActiveCompetitionRules();

        return response()->json([
            'termsOfService' => $termsOfService ? [
                'id' => $termsOfService->id,
                'type' => $termsOfService->type,
                'title' => $termsOfService->title,
                'content' => $this->parseMarkdown($termsOfService->content),
                'version' => $termsOfService->version,
                'effective_date' => $termsOfService->effective_date,
            ] : null,
            'privacyPolicy' => $privacyPolicy ? [
                'id' => $privacyPolicy->id,
                'type' => $privacyPolicy->type,
                'title' => $privacyPolicy->title,
                'content' => $this->parseMarkdown($privacyPolicy->content),
                'version' => $privacyPolicy->version,
                'effective_date' => $privacyPolicy->effective_date,
            ] : null,
            'competitionRules' => $competitionRules ? [
                'id' => $competitionRules->id,
                'type' => $competitionRules->type,
                'title' => $competitionRules->title,
                'content' => $this->parseMarkdown($competitionRules->content),
                'version' => $competitionRules->version,
                'effective_date' => $competitionRules->effective_date,
            ] : null,
        ]);
    }

    /**
     * Get a specific legal document by type.
     */
    public function getDocument(string $type): JsonResponse
    {
        $document = LegalDocument::getActiveDocument($type);

        if (! $document) {
            return response()->json([
                'error' => 'Document not found',
            ], 404);
        }

        return response()->json([
            'id' => $document->id,
            'type' => $document->type,
            'title' => $document->title,
            'content' => $this->parseMarkdown($document->content),
            'version' => $document->version,
            'effective_date' => $document->effective_date,
        ]);
    }

    /**
     * Display a legal document page for public viewing.
     */
    public function showDocument(string $type, Request $request)
    {
        $document = LegalDocument::getActiveDocument($type);

        if (! $document) {
            abort(404, 'Legal document not found');
        }

        // Check if user can view this document
        if (! $request->user()?->can('viewPublic', $document) && ! $document->is_active) {
            abort(403, 'This document is not publicly available');
        }

        return inertia('app/legal/document', [
            'document' => [
                'id' => $document->id,
                'type' => $document->type,
                'title' => $document->title,
                'content' => $this->parseMarkdown($document->content),
                'version' => $document->version,
                'effective_date' => $document->effective_date,
            ],
        ]);
    }

    /**
     * Parse markdown content to HTML using CommonMark.
     */
    private function parseMarkdown(string $content): string
    {
        $converter = new CommonMarkConverter([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
        ]);

        return $converter->convert($content)->getContent();
    }
}
