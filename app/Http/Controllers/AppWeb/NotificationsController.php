<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class NotificationsController extends Controller
{
    public function clear(Request $request)
    {
        $user = $request->user();

        $user->notifications()->delete();

        return back();
    }

    public function destroy(string $notificationId, Request $request)
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $user->notifications()->findOrFail($notificationId)
            ->delete();

        return back();
    }
}
