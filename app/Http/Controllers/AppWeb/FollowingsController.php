<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class FollowingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Handle the incoming request.
     */
    public function toggle(User $user, Request $request): RedirectResponse
    {
        $artist = $user;
        $auth = $request->user();

        $followed = $artist->followers()->where('follower_id', $auth->id)->exists();

        if ($followed) {
            $artist->followers()->where('follower_id', $auth->id)->delete();
        } else {
            $artist->followers()->create(['follower_id' => $auth->id]);
        }

        return back();
    }
}
