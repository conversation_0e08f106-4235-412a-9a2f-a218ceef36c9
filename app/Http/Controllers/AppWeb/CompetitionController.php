<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\User\ArtistShowResource;
use App\Models\Competition;
use App\Models\CompetitionEntry;
use App\Models\LegalDocument;
use App\Models\User;
use App\Models\UserAgreement;
use App\Repositories\CompetitionRepository;
use Illuminate\Http\Request;

class CompetitionController extends Controller
{
    use HasRetriever;

    public function __construct(
        private CompetitionRepository $competitionRepository
    ) {}

    /**
     * Display the voting page with active competitions.
     */
    public function index(Request $request)
    {
        return inertia('app/voting/index', $this->retrieve($request, [
            'competitions' => [$this->competitionRepository, 'getActiveCompetitions'],
            'similar_votes' => [$this->competitionRepository, 'getSimilarToUserVotes'],
            'recently_voted' => [$this->competitionRepository, 'getRecentlyVotedArtists'],
        ]));
    }

    /**
     * Display the leaderboard for a specific competition.
     */
    public function leaderboard(Competition $competition, Request $request)
    {
        return inertia('app/voting/leaderboard', $this->retrieve([$competition, $request], [
            'competition' => [$this->competitionRepository, 'getCompetitionDetails'],
            'leaderboard' => [$this->competitionRepository, 'getLeaderboard'],
        ]));
    }

    /**
     * Display the artist entry request form.
     */
    public function entryForm(Competition $competition, Request $request)
    {
        $user = $request->user();

        // Check if the competition is accepting entries
        if (! $competition->isAcceptingEntries()) {
            return redirect()->route('app.voting.index')
                ->with('error', 'This competition is no longer accepting entries.');
        }

        // Check if the user is an artist
        if (! $user->isArtist()) {
            return redirect()->route('app.voting.index')->with('error', 'Only artists can enter competitions.');
        }

        // Check if the artist has already entered this competition
        $existingEntry = CompetitionEntry::where('competition_id', $competition->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingEntry) {
            return redirect()->route('app.voting.leaderboard', $competition->id)
                ->with('info', 'You have already entered this competition.');
        }

        return inertia('app/voting/entry-form', [
            'competition' => $competition,
            'meetsRequirements' => $user->meetsCompetitionRequirements($competition->requirements ?? []),
        ]);
    }

    /**
     * Submit an entry request for a competition.
     */
    public function submitEntry(Competition $competition, Request $request)
    {
        $user = $request->user();

        // Check if the competition is accepting entries
        if (! $competition->isAcceptingEntries()) {
            return redirect()->route('app.voting.index')
                ->with('error', 'This competition is no longer accepting entries.');
        }

        // Validate legal agreements
        $request->validate([
            'terms_agreed' => 'required|accepted',
            'privacy_agreed' => 'required|accepted',
            'rules_agreed' => 'required|accepted',
        ], [
            'terms_agreed.accepted' => 'You must agree to the Terms of Service.',
            'privacy_agreed.accepted' => 'You must agree to the Privacy Policy.',
            'rules_agreed.accepted' => 'You must agree to the Competition Rules.',
        ]);

        // Check if the user is an artist
        if (! $user->isArtist()) {
            return redirect()->route('app.voting.index')->with('error', 'Only artists can enter competitions.');
        }

        // Check if the artist has already entered this competition
        $existingEntry = CompetitionEntry::where('competition_id', $competition->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingEntry) {
            return redirect()->route('app.voting.leaderboard', $competition->id)
                ->with('info', 'You have already entered this competition.');
        }

        // Check if the artist meets the requirements
        $meetsRequirements = $user->meetsCompetitionRequirements($competition->requirements ?? []);

        // Determine entry status based on auto-approval settings
        $entryStatus = 'pending';
        if ($competition->auto_approve && $meetsRequirements) {
            $entryStatus = 'approved';
        }

        // Get the active legal documents
        $termsOfService = LegalDocument::getActiveTermsOfService();
        $privacyPolicy = LegalDocument::getActivePrivacyPolicy();
        $competitionRules = LegalDocument::getActiveCompetitionRules();

        // Validate that all required documents exist
        if (! $termsOfService || ! $privacyPolicy || ! $competitionRules) {
            return redirect()->back()
                ->withErrors(['legal_documents' => 'Required legal documents are not available. Please contact support.']);
        }

        // Create the entry
        $entry = CompetitionEntry::create([
            'competition_id' => $competition->id,
            'user_id' => $user->id,
            'status' => $entryStatus,
            'entry_date' => now(),
            'requirements_met' => $meetsRequirements,
            'terms_agreed_at' => now(),
            'privacy_agreed_at' => now(),
            'rules_agreed_at' => now(),
            'agreement_ip_address' => $request->ip(),
        ]);

        // Record individual user agreements for each legal document
        UserAgreement::recordAgreement(
            $user->id,
            $termsOfService->id,
            $competition->id,
            $request->ip(),
            $request->userAgent()
        );

        UserAgreement::recordAgreement(
            $user->id,
            $privacyPolicy->id,
            $competition->id,
            $request->ip(),
            $request->userAgent()
        );

        UserAgreement::recordAgreement(
            $user->id,
            $competitionRules->id,
            $competition->id,
            $request->ip(),
            $request->userAgent()
        );

        // Determine success message based on entry status
        $successMessage = $entryStatus === 'approved'
            ? 'Your entry has been submitted and automatically approved! You can now participate in the competition.'
            : 'Your entry request has been submitted and is pending approval.';

        return redirect()->route('app.voting.leaderboard', $competition->id)
            ->with('success', $successMessage);
    }

    /**
     * Display the artist profile page with voting options.
     */
    public function artistProfile(Competition $competition, User $artist, Request $request)
    {
        // Check if the competition is in voting phase
        if (! $competition->isInVotingPhase()) {
            return redirect()->route('app.voting.leaderboard', $competition->id)
                ->with('error', 'Voting is not currently available for this competition.');
        }

        // Check if the artist is entered and approved in the competition
        $entry = CompetitionEntry::where('competition_id', $competition->id)
            ->where('user_id', $artist->id)
            ->where('status', 'approved')
            ->first();

        if (! $entry) {
            return redirect()->route('app.voting.leaderboard', $competition->id)
                ->with('error', 'This artist is not participating in this competition.');
        }

        // Get the artist's vote count in this competition (sum of vote_count field)
        $voteCount = $artist->votesReceived()
            ->where('competition_id', $competition->id)
            ->sum('vote_count');

        // Check if the current user has already voted for this artist
        $hasVoted = false;
        if ($request->user()) {
            $hasVoted = $request->user()->votesGiven()
                ->where('competition_id', $competition->id)
                ->where('artist_id', $artist->id)
                ->exists();
        }

        return inertia('app/voting/artist-profile', [
            'competition' => $competition->load('image'),
            'artist' => new ArtistShowResource($artist),
            'voteCount' => $voteCount,
            'hasVoted' => $hasVoted,
            'entry' => $entry,
        ]);
    }
}
