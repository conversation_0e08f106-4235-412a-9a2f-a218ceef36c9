<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Repositories\RecommendationRepository;
use Illuminate\Http\Request;

class DiscoverController extends Controller
{
    use HasRetriever;

    public function __construct(private RecommendationRepository $recommendationRepository) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $topCharts = [];

        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return inertia(
            'app/discover',
            [
                ...$recommendations,
                'recently_played' => $recentlyPlayed,
                'top_charts' => $topCharts,
                'top_artists' => $topArtists,
            ]
        );
    }
}
