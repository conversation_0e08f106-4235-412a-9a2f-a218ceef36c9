<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;

class PlaysController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Handle the incoming request.
     */
    public function storeArticlePlay(Article $article, Request $request)
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $user->playings()->create([
            // user as artist
            'user_id' => $article->user_id,
            'article_id' => $article->id,
        ]);

        return response()->json(['store' => true]);
    }
}
