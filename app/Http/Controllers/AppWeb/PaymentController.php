<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\Competition;
use App\Models\CompetitionEntry;
use App\Models\User;
use App\Models\Vote;
use App\Services\FlutterwaveService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * The Flutterwave service instance.
     *
     * @var FlutterwaveService
     */
    protected $flutterwaveService;

    /**
     * Create a new controller instance.
     */
    public function __construct(FlutterwaveService $flutterwaveService)
    {
        $this->flutterwaveService = $flutterwaveService;
    }

    /**
     * Initialize a payment for voting.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function initializePayment(Competition $competition, User $artist, Request $request)
    {
        $voter = $request->user();

        // Check if the user can vote for this artist
        if (! $voter->can('voteFor', $artist)) {
            return back()->with('error', 'You cannot vote for yourself.');
        }

        // Validate the vote count
        $request->validate([
            'vote_count' => 'required|integer|min:1|max:100',
        ]);

        $voteCount = $request->input('vote_count', 1);

        // Check if the competition is in voting phase
        if (! $competition->isInVotingPhase()) {
            return back()->with('error', 'Voting is not currently available for this competition.');
        }

        // Check if the artist is entered in the competition
        $entry = CompetitionEntry::where('competition_id', $competition->id)
            ->where('user_id', $artist->id)
            ->where('status', 'approved')
            ->first();

        if (! $entry) {
            return back()->with('error', 'This artist is not participating in this competition.');
        }

        // Calculate the total amount
        $totalAmount = $competition->vote_price * $voteCount;

        // Set the redirect URL after payment
        $redirectUrl = route('app.voting.payment.callback');

        // Initialize the vote payment
        $paymentResponse = $this->flutterwaveService->initializeVotePayment(
            $voter,
            $artist,
            $competition,
            $voteCount,
            $totalAmount,
            $redirectUrl
        );

        // Check if payment initialization was successful
        if ($paymentResponse['status'] === 'success') {
            // Redirect to the payment page
            return redirect($paymentResponse['data']['link']);
        }

        // If payment initialization failed
        return back()->with('error', 'Failed to initialize payment. Please try again.');

        // The code below is for future implementation when subscriptions are required
        /*
        if ($hasActiveSubscription) {
            // User has an active subscription, allow voting for free
            $vote = Vote::create([
                'competition_id' => $competition->id,
                'user_id' => $voter->id,
                'artist_id' => $artist->id,
                'paid' => false, // Free with subscription
            ]);

            return redirect()->route('app.voting.artist', [
                'competition' => $competition->id,
                'artist' => $artist->id,
            ])->with('success', 'Your vote has been recorded.');
        } else {
            // User doesn't have an active subscription, redirect to subscription page
            return redirect()->route('app.subscription.checkout')
                ->with('info', 'A subscription is required to vote. Please subscribe to continue.');
        }
        */
    }

    /**
     * Display the subscription checkout page.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function subscriptionCheckout(Request $request)
    {
        $user = $request->user();

        // Check if the user already has an active subscription
        if ($user->hasActiveSubscription()) {
            return redirect()->route('app.voting.index')
                ->with('info', 'You already have an active subscription.');
        }

        // Set the subscription amount
        $amount = 29.99; // $29.99 USD for annual subscription

        // Set the redirect URL after payment
        $redirectUrl = route('app.voting.payment.callback');

        // Initialize the subscription payment
        $paymentResponse = $this->flutterwaveService->initializeSubscriptionPayment(
            $user,
            'annual',
            $amount,
            $redirectUrl
        );

        // Check if payment initialization was successful
        if ($paymentResponse['status'] === 'success') {
            // Redirect to the payment page
            return redirect($paymentResponse['data']['link']);
        }

        // If payment initialization failed
        return back()->with('error', 'Failed to initialize subscription. Please try again.');
    }

    /**
     * Display the mock payment page.
     *
     * @return \Illuminate\View\View
     */
    public function mockPaymentPage(Request $request)
    {
        // Get the payment data from the request
        $txRef = $request->input('tx_ref');
        $voterId = $request->input('voter_id');
        $artistId = $request->input('artist_id');
        $competitionId = $request->input('competition_id');
        $voteCount = $request->input('vote_count', 1);
        $amount = $request->input('amount');
        $redirectUrl = $request->input('redirect_url');

        // Get the voter and artist details
        $voter = User::find($voterId);
        $artist = User::find($artistId);
        $competition = Competition::find($competitionId);

        if (! $voter || ! $artist || ! $competition) {
            abort(404, 'Invalid payment data');
        }

        // Return the mock payment page
        return view('app.payment.mock', [
            'tx_ref' => $txRef,
            'voter' => $voter,
            'artist' => $artist,
            'competition' => $competition,
            'vote_count' => $voteCount,
            'amount' => $amount,
            'redirect_url' => $redirectUrl,
        ]);
    }

    /**
     * Process the mock payment.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processMockPayment(Request $request)
    {
        // Get the payment data from the request
        $txRef = $request->input('tx_ref');
        $voterId = $request->input('voter_id');
        $artistId = $request->input('artist_id');
        $competitionId = $request->input('competition_id');
        $voteCount = $request->input('vote_count', 1);
        $amount = $request->input('amount');

        // Log the mock payment
        Log::info('Mock payment processed', [
            'tx_ref' => $txRef,
            'voter_id' => $voterId,
            'artist_id' => $artistId,
            'competition_id' => $competitionId,
            'vote_count' => $voteCount,
            'amount' => $amount,
        ]);

        // Process the payment
        $paymentData = [
            'voter_id' => $voterId,
            'artist_id' => $artistId,
            'competition_id' => $competitionId,
            'vote_count' => $voteCount,
            'amount' => $amount,
            'transaction_id' => $txRef,
        ];

        $this->flutterwaveService->processVotePayment($paymentData);

        // Redirect to the artist page
        return redirect()->route('app.voting.artist', [
            'competition' => $competitionId,
            'artist' => $artistId,
        ])->with('success', 'Your vote has been recorded.');
    }

    /**
     * Handle the payment callback from Flutterwave.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handlePaymentCallback(Request $request)
    {
        // Get the transaction ID from the request
        $transactionId = $request->input('transaction_id');
        $status = $request->input('status');

        // Log the callback
        Log::info('Payment callback received', [
            'transaction_id' => $transactionId,
            'status' => $status,
            'data' => $request->all(),
        ]);

        // Verify the payment
        $verificationResponse = $this->flutterwaveService->verifyPayment($transactionId);

        // Check if payment verification was successful
        if ($verificationResponse['status'] === 'success' && $verificationResponse['data']['status'] === 'successful') {
            // Extract the payment data
            // In a real implementation, we would store this data in the session or a temporary table
            // For now, we'll just redirect to a success page
            return redirect()->route('app.voting.index')
                ->with('success', 'Payment successful. Your vote has been recorded.');
        }

        // If payment verification failed
        return redirect()->route('app.voting.index')
            ->with('error', 'Payment verification failed. Please try again.');
    }
}
