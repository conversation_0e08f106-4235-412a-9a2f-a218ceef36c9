<?php

namespace App\Http\Controllers\AppWeb;

use App\Enums\UserRole;
use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Channel\ChannelResource;
use App\Http\Resources\User\ArtistCollection;
use App\Http\Resources\User\UserResource;
use App\Models\File\Image;
use App\Models\User;
use App\Repositories\ChannelRepository;
use App\Repositories\FeaturingRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Str;

class ArtistesController extends Controller
{
    use HasFileRelation, HasRetriever;

    public function __construct(
        private UserRepository $userRepository,
        private ChannelRepository $channelRepository,
        private FeaturingRepository $featuringRepository,
    ) {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        return inertia('app/artists/index', $this->retrieve($request, [
            'artists' => [$this->userRepository, 'getRandomArtists'],
        ]));
    }

    /**
     * Handle the incoming request.
     */
    public function show(User $user, Request $request)
    {
        if (! $user->isArtist()) {
            abort(404);
        }

        return inertia(
            'app/artists/show',
            $this->retrieve(
                [$request, 'artist' => $user],
                [
                    'artist' => [$this->userRepository, 'getArtistDetails'],
                    'topchart_articles' => [$this->userRepository, 'getArtistTopChartArticles'],
                    'channels' => [$this->channelRepository, 'getArtistChannels'],
                    'featurings' => [$this->featuringRepository, 'getArtistFeaturings'],
                    'followed' => [$this->userRepository, 'isArtistFollowedByUser'],
                ]
            )
        );
    }

    /**
     * Handle the incoming request.
     */
    public function create(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class),
            ],
            'role' => [
                'required',
                Rule::enum(UserRole::class),
                Rule::in(User::getOnlyArtistTypes()),
            ],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'password' => ['nullable'],
        ]);

        $user = User::create([
            ...$data,
            'role' => UserRole::from($data['role']),
            'password' => Str::random(10),
        ]);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $user->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $user->refresh();

        return new UserResource($user);
    }

    public function updateImage(Request $request)
    {
        $data = $request->validate([
            'user_id' => ['required', "exists:App\Models\User,id"],
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        $user = User::find($data['user_id']);

        if ($request->hasFile('image')) {
            $user->image()->forceDelete();

            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $user->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $user->refresh();

        return new UserResource($user);
    }

    /**
     * Handle the incoming request.
     */
    public function getPopularArticles(User $user)
    {
        return $this->userRepository->getArtistTopChartArticles($user, 100);
    }

    /**
     * Handle the incoming request.
     */
    public function getByName(Request $request)
    {
        $name = $request->query('q');
        $user = User::where('name', 'like', trim($name))->first();

        abort_if($user === null, 404, 'Not found');

        return new UserResource($user);
    }

    /**
     * Handle the incoming request.
     */
    public function getArtistsWithoutImage()
    {
        $artists = User::artist()->whereHas('image', null, '!=')->get();

        return new ArtistCollection($artists);
    }

    /**
     * Handle the incoming request.
     */
    public function getArtistChannelByName(User $user, Request $request)
    {
        $name = $request->query('q');
        $channel = $user->channels()->where('name', 'like', trim($name))->first();

        abort_if($channel === null, 404, 'Not found');

        return new ChannelResource($channel);
    }

    /**
     * Handle the incoming request.
     */
    public function getByEmail(Request $request)
    {
        $email = $request->query('q');
        $user = User::where('email', trim($email))->first();

        abort_if($user === null, 404, 'Not found');

        return new UserResource($user);
    }
}
