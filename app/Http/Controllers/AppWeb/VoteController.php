<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Models\Competition;
use App\Models\User;

class VoteController extends Controller
{
    /**
     * Share a voting link for an artist.
     */
    public function share(Competition $competition, User $artist)
    {
        // Generate a sharing URL
        $shareUrl = route('app.voting.artist', [
            'competition' => $competition->id,
            'artist' => $artist->id,
        ]);

        // Return the sharing URL
        return response()->json([
            'share_url' => $shareUrl,
        ]);
    }
}
