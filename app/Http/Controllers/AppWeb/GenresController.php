<?php

namespace App\Http\Controllers\AppWeb;

use App\Http\Controllers\Controller;
use App\Http\HasRetriever;
use App\Http\Resources\Genre\GenreResource;
use App\Models\Genre;
use App\Repositories\GenreRepository;
use Illuminate\Http\Request;

class GenresController extends Controller
{
    use HasRetriever;

    public function __construct(
        private GenreRepository $genreRepository
    ) {}

    /**
     * Handle the incoming request.
     */
    public function show(Genre $genre, Request $request)
    {
        return inertia('app/genres/show', $this->retrieve([$request, $genre], [
            'genre' => [$this->genreRepository, 'getGenreDetails'],
            'channels' => [$this->genreRepository, 'getGenreChannels'],
            'articles' => [$this->genreRepository, 'getGenreArticles'],
        ]));
    }

    /**
     * Handle the XHR incoming request.
     */
    public function random(Request $request)
    {
        $name = $request->query('q');

        try {
            throw_if(blank($name));

            $genre = $this->genreRepository->findOrCreate($name);
        } catch (\Throwable $th) {
            $genre = Genre::inRandomOrder()->first();
        }

        abort_if($genre === null, 404);

        return new GenreResource($genre);
    }

    /**
     * Handle the XHR incoming request.
     */
    public function genreArticles(Genre $genre, Request $request)
    {
        return $this->genreRepository->getGenreRandomArticles($genre, $request->user());
    }
}
