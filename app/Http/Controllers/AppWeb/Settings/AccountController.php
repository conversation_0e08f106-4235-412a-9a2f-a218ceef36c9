<?php

namespace App\Http\Controllers\AppWeb\Settings;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Models\ArtistRequest;
use App\Models\File\Image;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\File;

class AccountController extends Controller
{
    use HasFileRelation;

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $create_artist_request = $request->user()->can('create', ArtistRequest::class);

        return inertia('app/settings/account', [
            'can' => [
                'create_artist_request' => $create_artist_request,
            ],
            'artist_roles' => User::getOnlyArtistTypes(),
        ]);
    }

    /**
     * Handle the income post request (update user image profile)
     */
    public function updateImageProfile(Request $request): RedirectResponse
    {
        $request->validate([
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ]);

        /** @var \App\Models\User */
        $user = $request->user();

        $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

        // Remove the previous image
        $user->image?->forceDelete();

        // Create the new image
        $user->image()->create(
            attributes: $this->fillFileRequiredFields(
                disk: Image::getDisk(),
                data: ['filename_disk' => $path]
            )
        );

        return back();
    }
}
