<?php

namespace App\Http\Controllers\AppWeb\Settings;

use App\Http\Controllers\Controller;
use App\Models\ArtistRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ArtistRequestController extends Controller
{
    /**
     * Store a new artist request.
     */
    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create', ArtistRequest::class);

        $validated = $request->validate([
            'artist_type' => [
                'required',
                'string',
                Rule::in(User::getOnlyArtistTypes()),
                'max:10',
            ],
            'description' => [
                'required',
                'string',
                'min:20',
                'max:5000',
            ],
            'youtube_link' => [
                'nullable',
                'string',
                'url',
                'max:255',
                'regex:/^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.*$/',
            ],
        ]);

        /** @var User */
        $user = $request->user();

        $user->artistRequest()->create($validated);

        return back();
    }
}
