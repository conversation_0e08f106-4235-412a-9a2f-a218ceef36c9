<?php

namespace App\Http\Controllers\Streaming;

use App\Http\Controllers\Controller;
use App\Models\Morph\File;
use Illuminate\Support\Facades\Storage;

class AudioHlsStreamController extends Controller
{
    /**
     * Serve the master files
     */
    public function __invoke(File $file, string $filename)
    {
        $hlsDirectory = rtrim($file->hls_directory, '/');
        $path = $hlsDirectory.'/'.$filename;

        $fs = Storage::disk($file->storage);

        if (! $fs->exists($path)) {
            return response()->json(['error' => 'File not found'], 404);
        }

        return response($fs->get($path))
            ->header('Content-Type', $this->getContentType($filename))
            ->header('Access-Control-Allow-Origin', '*') // For CORS
            ->header('Cache-Control', 'max-age=86400'); // Cache for 24 hours
    }

    /**
     * Determine the correct content type for the file
     */
    private function getContentType($filename)
    {
        if (str_ends_with($filename, '.m3u8')) {
            return 'application/vnd.apple.mpegurl';
        } elseif (str_ends_with($filename, '.ts')) {
            return 'video/mp2t';
        } else {
            return 'application/octet-stream';
        }
    }
}
