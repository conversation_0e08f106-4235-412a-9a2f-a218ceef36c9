<?php

namespace App\Http\Controllers\Streaming;

use App\Http\Controllers\Controller;
use App\Models\Morph\File;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class AudioRangeStreamController extends Controller
{
    /**
     * Stream an audio file with support for range requests.
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function __invoke(Request $request, File $file)
    {
        try {
            $storage = Storage::disk($file->storage);
            $filenameDisk = $file->filename_disk;

            if (! $storage->exists($filenameDisk)) {
                return response()->json(['error' => 'File not found'], 404);
            }

            $mimeType = $storage->mimeType($filenameDisk);
            if (! $this->isAudioMimeType($mimeType)) {
                Log::warning("Non-audio file requested: {$filenameDisk} ({$mimeType})");

                return response()->json(['error' => 'Invalid file type'], 415);
            }

            $fileSize = $storage->size($filenameDisk);

            return $this->stream($request, $storage, $file, $mimeType, $fileSize);
        } catch (\Exception $e) {
            Log::error('Error streaming audio: '.$e->getMessage(), [
                'file_id' => $file->id,
                'exception' => $e,
            ]);

            return response()->json(['error' => 'Failed to stream audio'], 500);
        }
    }

    /**
     * Create a streamed response with proper support for range requests.
     *
     * @return StreamedResponse|\Illuminate\Http\JsonResponse
     */
    protected function stream(Request $request, Filesystem $storage, File $file, string $mimeType, int $fileSize)
    {
        // Default headers
        $filename = str($file->filename_download)->basename();
        $headers = [
            'Content-Type' => $mimeType,
            'Accept-Ranges' => 'bytes',
            'Content-Length' => $fileSize,
            'Cache-Control' => 'public, max-age=86400',
            'Content-Disposition' => 'inline; filename="'.addslashes($filename).'"',
            'X-Accel-Buffering' => 'no', // Disable nginx buffering for better streaming
        ];

        $status = 200;
        $start = 0;
        $end = $fileSize - 1;

        // Process range request if present
        if ($request->hasHeader('Range')) {
            $rangeHeader = $request->header('Range');

            if (strpos($rangeHeader, 'bytes=') !== 0) {
                return response()->json(['error' => 'Invalid range header'], 416);
            }

            $rangePart = substr($rangeHeader, 6);
            $ranges = explode(',', $rangePart, 2);
            if (count($ranges) > 1) {
                return response()->json(['error' => 'Multiple ranges not supported'], 416);
            }

            $range = $ranges[0];
            $rangeParts = explode('-', $range, 2);
            if (count($rangeParts) != 2) {
                return response()->json(['error' => 'Invalid range header'], 416);
            }

            [$startStr, $endStr] = $rangeParts;

            if ($startStr === '' && $endStr !== '') {
                // Suffix case: -suffix
                $suffix = (int) $endStr;
                if ($suffix <= 0) {
                    return response()->json(['error' => 'Invalid suffix range'], 416);
                }
                $start = max(0, $fileSize - $suffix);
                $end = $fileSize - 1;
            } elseif ($endStr !== '') {
                // Start-end case: 0-499
                $start = (int) $startStr;
                $end = (int) $endStr;
            } elseif ($startStr !== '') {
                // Start to end of file: 500-
                $start = (int) $startStr;
                $end = $fileSize - 1;
            } else {
                return response()->json(['error' => 'Invalid range header'], 416);
            }

            // Validate range
            $maxChunkSize = 1024 * 1024 * 8; // 8MB
            if ($end - $start + 1 > $maxChunkSize) {
                $end = $start + $maxChunkSize - 1;
            }

            if ($start >= $fileSize) {
                return response()->json(
                    ['error' => 'Requested range not satisfiable'],
                    416,
                    ['Content-Range' => 'bytes */'.$fileSize]
                );
            }

            $end = min($end, $fileSize - 1);

            if ($start > $end) {
                return response()->json(
                    ['error' => 'Invalid range: start exceeds end'],
                    416,
                    ['Content-Range' => 'bytes */'.$fileSize]
                );
            }

            $status = 206;
            $headers['Content-Range'] = "bytes $start-$end/$fileSize";
            $headers['Content-Length'] = $end - $start + 1;
        }

        $response = new StreamedResponse(function () use ($storage, $file, $start, $end, $fileSize) {
            $stream = $storage->readStream($file->filename_disk);
            if (! $stream) {
                Log::error("Failed to open stream for file: {$file->filename_disk}");

                return;
            }

            $meta = stream_get_meta_data($stream);
            $seekable = $meta['seekable'];
            $remaining = $end - $start + 1;

            try {
                if (function_exists('stream_set_read_buffer')) {
                    stream_set_read_buffer($stream, 0);
                }

                // Handle seeking or skipping bytes for non-seekable streams
                if ($start > 0) {
                    if ($seekable) {
                        if (fseek($stream, $start, SEEK_SET) === -1) {
                            Log::error("Failed to seek in stream for file: {$file->filename_disk}");
                            fclose($stream);

                            return;
                        }
                    } else {
                        $bytesToSkip = $start;
                        $chunkSize = 8192; // 8KB chunks
                        while ($bytesToSkip > 0 && ! feof($stream)) {
                            $data = fread($stream, min($chunkSize, $bytesToSkip));
                            if ($data === false) {
                                Log::error("Error reading from stream for file: {$file->filename_disk}");
                                fclose($stream);

                                return;
                            }
                            $bytesRead = strlen($data);
                            $bytesToSkip -= $bytesRead;
                        }
                        if ($bytesToSkip > 0) {
                            Log::error("Reached EOF before seeking to start position for file: {$file->filename_disk}");
                            fclose($stream);

                            return;
                        }
                    }
                }

                if (ob_get_level()) {
                    ob_end_flush();
                }

                $bufferSize = $fileSize > 1024 * 1024 * 100 ? 65536 : 32768; // 64KB or 32KB

                while (! feof($stream) && $remaining > 0) {
                    $bytesToRead = min($bufferSize, $remaining);
                    $data = fread($stream, $bytesToRead);
                    if ($data === false) {
                        Log::error("Error reading from stream for file: {$file->filename_disk}");
                        break;
                    }

                    $dataLen = strlen($data);
                    if ($dataLen === 0) {
                        break;
                    }

                    echo $data;
                    flush();

                    $remaining -= $dataLen;
                }
            } finally {
                if (is_resource($stream)) {
                    fclose($stream);
                }
            }
        }, $status, $headers);

        return $response;
    }

    /**
     * Check if the given MIME type is an audio type.
     */
    protected function isAudioMimeType(string $mimeType): bool
    {
        return str_starts_with($mimeType, 'audio/');
    }
}
