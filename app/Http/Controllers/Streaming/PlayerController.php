<?php

namespace App\Http\Controllers\Streaming;

use App\Http\Controllers\Controller;
use App\Http\Resources\Article\ArticleResource;
use App\Services\PlayerQueueService;
use Illuminate\Http\Request;

class PlayerController extends Controller
{
    protected PlayerQueueService $playerQueueService;

    public function __construct(PlayerQueueService $playerQueueService)
    {
        $this->playerQueueService = $playerQueueService;
    }

    /**
     * Get the next suggested track for the Auto DJ Mix.
     */
    public function getNextDjTrack(Request $request)
    {
        $data = $request->validate([
            // Expect an array of IDs played in this session
            'played_article_ids' => 'required|array|min:1',
            'played_article_ids.*' => 'required|string|distinct', // Ensure IDs are numeric (or ulid if needed)
        ]);

        // Convert IDs to a collection
        $sessionPlayedIds = collect($data['played_article_ids']);
        $user = $request->user();

        try {
            $nextTrack = $this->playerQueueService->getNextDjTrack($sessionPlayedIds, $user);

            if ($nextTrack) {
                return new ArticleResource($nextTrack);
            } else {
                return response()->json([
                    'message' => 'Could not find a suitable next track.',
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred while suggesting the next track.'], 500);
        }
    }
}
