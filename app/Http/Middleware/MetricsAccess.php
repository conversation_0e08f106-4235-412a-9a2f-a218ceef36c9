<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class MetricsAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        // Check if metrics access is restricted
        if (config('metrics.access_restricted', false)) {
            // Option 1: Check for specific IP addresses
            $allowedIps = config('metrics.allowed_ips', []);
            if (! empty($allowedIps) && ! in_array($request->ip(), $allowedIps)) {
                abort(403, 'Access denied');
            }

            // Option 2: Check for authentication
            if (config('metrics.require_auth', false) && ! $request->user()) {
                abort(401, 'Authentication required');
            }

            // Option 3: Check for admin role
            if (config('metrics.require_admin', false)) {
                if (! $request->user() || ! $request->user()->isAdmin()) {
                    abort(403, 'Admin access required');
                }
            }

            // Option 4: Check for API token
            $requiredToken = config('metrics.api_token');
            if ($requiredToken) {
                $providedToken = $request->header('Authorization')
                    ?? $request->query('token')
                    ?? $request->header('X-Metrics-Token');

                if ($providedToken !== $requiredToken && $providedToken !== "Bearer {$requiredToken}") {
                    abort(401, 'Invalid token');
                }
            }
        }

        return $next($request);
    }
}
