<?php

namespace App\Http\Middleware;

use App\Http\Resources\User\UserResource;
use App\Repositories\CompetitionRepository;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Unique identifier for the Inertia component.
     */
    private function identifier(): string
    {
        return Cache::store('database')->rememberForever('inertia-identifier', function () {
            return \Str::random(16);
        });
    }

    /**
     * Defines the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     */
    public function share(Request $request): array
    {
        /**
         * @var \App\Models\User
         */
        $user = $request->user();

        $notifications = $user ?
            $user->unreadNotifications
                ->map(function ($notif) {
                    return array_merge(Notification::fromDatabase($notif)->toArray(), [
                        'created_at' => $notif->created_at,
                        'readable_date' => $notif->created_at->diffForHumans(),
                    ]);
                })
                ->toArray() :
            [];

        $manager = app('impersonate');
        $competitionRepository = app(CompetitionRepository::class);

        return array_merge(parent::share($request), [
            'appName' => config('app.name'),

            'authUser' => $user ? new UserResource($user) : null,

            'notifications' => $notifications,

            'identifier' => $this->identifier(),

            'hlsEnabled' => config('audio.hls_enabled'),

            'isImpersonating' => $manager->isImpersonating(),

            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'error' => fn () => $request->session()->get('error'),
                'success' => fn () => $request->session()->get('success'),
            ],

            'sso' => [
                'google' => ['enabled' => filled((config('services.google.client_id')))],
            ],

            'votingActive' => $competitionRepository->hasActiveCompetitions(),
        ]);
    }
}
