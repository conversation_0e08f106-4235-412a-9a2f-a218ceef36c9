<?php

namespace App\Http;

use App\Models\User;
use Illuminate\Http\Request;

trait HasRetriever
{
    /**
     * @param  Request  $request
     * @param  array<string, \Closure|array>  $calls
     */
    protected function retrieve(array|Request $params, array $calls): array
    {
        $args = [];
        if (is_array($params)) {
            foreach ($params as $key => $param) {
                if ($param instanceof Request) {
                    $args[Request::class] = $param;

                    continue;
                }

                if (gettype($key) === 'integer' && is_object($param)) {
                    $args[get_class($param)] = $param;
                }

                if (gettype($key) === 'string') {
                    $args[$key] = $param;
                }
            }
        }

        $request = isset($args[Request::class]) ? $args[Request::class] : $params;

        if (! ($request instanceof Request)) {
            throw new \Exception('Cannot found request object in passed params', 1);
        }

        $user = $request->user();
        if ($user === null) {
            return [];
        }

        $called = [];
        $args = array_merge([
            User::class => $user,
            Request::class => $request,
        ], $args);

        foreach ($calls as $key => $closure) {
            $reflector = is_array($closure) ?
                new \ReflectionMethod($closure[0], $closure[1]) :
                new \ReflectionFunction($closure);

            $closureArgs = [];
            foreach ($reflector->getParameters() as $param) {
                // Skill variadic params
                if ($param->isVariadic()) {
                    continue;
                }

                $typeName = $param->getType()?->getName();
                $arg = ($args[$param->getName()] ?? null) ??
                    (isset($args[$typeName]) ? $args[$typeName] : null);
                // comment: if the param is not passed, we will use the default value
                if ($arg === null && $param->isDefaultValueAvailable()) {
                    $arg = $param->getDefaultValue();
                }
                array_push($closureArgs, $arg);
            }

            $called[$key] = call_user_func_array($closure, $closureArgs);
        }

        return $called;
    }
}
