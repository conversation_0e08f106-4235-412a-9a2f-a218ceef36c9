<?php

namespace App\Http\Requests\API\Comments;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @bodyParam comment string required The updated comment text. Must be between 2 and 1000 characters. Example: This is an updated comment!
 */
class UpdateCommentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'comment' => ['required', 'string', 'min:2', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'comment.required' => 'Comment text is required',
            'comment.min' => 'Comment must be at least 2 characters long',
            'comment.max' => 'Comment cannot exceed 1000 characters',
        ];
    }
}
