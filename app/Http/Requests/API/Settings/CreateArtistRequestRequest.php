<?php

namespace App\Http\Requests\API\Settings;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * @bodyParam artist_type string required The type of artist role being requested. Example: singer
 * @bodyParam description string required Description of why the user wants to become an artist (20-5000 characters). Example: I have been making music for 5 years and would like to share my work on this platform.
 * @bodyParam youtube_link string optional YouTube channel or video link for verification purposes. Example: https://youtube.com/@yourusername
 * @bodyParam social_links object optional Social media links as key-value pairs. Example: {"instagram": "https://instagram.com/artist", "twitter": "https://twitter.com/artist"}
 */
class CreateArtistRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'artist_type' => [
                'required',
                'string',
                Rule::in(User::getOnlyArtistTypes()),
                'max:10',
            ],
            'description' => [
                'required',
                'string',
                'min:20',
                'max:5000',
            ],
            'youtube_link' => [
                'nullable',
                'string',
                'url',
                'max:255',
                'regex:/^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.*$/',
            ],
            'social_links' => [
                'nullable',
                'array',
                'max:10', // Limit to 10 social links
            ],
            'social_links.*' => [
                'required',
                'string',
                'url',
                'max:255',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'artist_type.required' => 'Artist type is required',
            'artist_type.in' => 'Selected artist type is not valid',
            'description.required' => 'Description is required',
            'description.min' => 'Description must be at least 20 characters',
            'description.max' => 'Description cannot exceed 5000 characters',
            'youtube_link.url' => 'Please provide a valid YouTube URL',
            'youtube_link.regex' => 'Please provide a valid YouTube channel or video URL',
            'youtube_link.max' => 'YouTube link cannot exceed 255 characters',
            'social_links.array' => 'Social links must be an array',
            'social_links.max' => 'You can add a maximum of 10 social links',
            'social_links.*.required' => 'Social link URL is required',
            'social_links.*.url' => 'Please provide a valid URL for social links',
            'social_links.*.max' => 'Social link URL cannot exceed 255 characters',
        ];
    }

    /**
     * Get the available artist types for documentation
     */
    public function getArtistTypes(): array
    {
        return User::getOnlyArtistTypes();
    }
}
