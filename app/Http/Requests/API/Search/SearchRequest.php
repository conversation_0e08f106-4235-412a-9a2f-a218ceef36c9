<?php

namespace App\Http\Requests\API\Search;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @queryParam q string required The search query. Example: rock music
 * @queryParam limit integer optional The maximum number of results per category (default: 12). Example: 20
 */
class SearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'q' => ['required', 'string', 'min:1', 'max:255'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'q.required' => 'Search query is required',
            'q.min' => 'Search query must be at least 1 character',
            'q.max' => 'Search query cannot exceed 255 characters',
            'limit.integer' => 'Limit must be a number',
            'limit.min' => 'Limit must be at least 1',
            'limit.max' => 'Limit cannot exceed 100',
        ];
    }

    /**
     * Get the search query
     */
    public function getQuery(): string
    {
        return $this->input('q', '');
    }

    /**
     * Get the search limit
     */
    public function getLimit(): int
    {
        return $this->input('limit', 12);
    }
}
