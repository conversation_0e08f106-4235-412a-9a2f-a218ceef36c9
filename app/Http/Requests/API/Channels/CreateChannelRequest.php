<?php

namespace App\Http\Requests\API\Channels;

use App\Enums\ChannelType;
use App\Models\File\Image;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

/**
 * @bodyParam name string required The name of the channel. Example: My Music Channel
 * @bodyParam description string optional The description of the channel. Example: This channel contains my latest music
 * @bodyParam type string required The type of the channel (music, podcast, etc.). Example: music
 * @bodyParam genre_id integer required The ID of the genre for this channel. Example: 1
 * @bodyParam user_id integer required The ID of the user creating this channel. Example: 1
 * @bodyParam image file optional The cover image for the channel (JPG, PNG, max 10MB)
 */
class CreateChannelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:5000'],
            'type' => ['required', Rule::enum(ChannelType::class)],
            'genre_id' => ['required', 'integer', 'exists:App\Models\Genre,id'],
            'user_id' => ['required', 'integer', 'exists:App\Models\User,id'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Channel name is required',
            'name.max' => 'Channel name cannot exceed 255 characters',
            'type.required' => 'Channel type is required',
            'genre_id.required' => 'Genre is required',
            'genre_id.exists' => 'Selected genre does not exist',
            'user_id.required' => 'User is required',
            'user_id.exists' => 'Selected user does not exist',
            'image.max' => 'Image file size cannot exceed '.(Image::MAX_SIZE / 1024).'KB',
        ];
    }

    /**
     * Get the channel type enum values for documentation
     */
    public function getChannelTypes(): array
    {
        return array_map(fn ($case) => $case->value, ChannelType::cases());
    }
}
