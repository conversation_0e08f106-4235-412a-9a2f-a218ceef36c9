<?php

if (! function_exists('emailfy')) {
    function emailfy(string $text, string $domain): string
    {
        $text = Str::lower($text);
        // Remove any invalid email character from the text input
        $validEmailChars = '/[^a-zA-Z0-9]/';
        $emailUsername = preg_replace($validEmailChars, '', trim($text));

        // Combine the cleaned-up text with the domain to form an email address
        $emailAddress = "{$emailUsername}@{$domain}";

        return Str::lower($emailAddress);
    }

}

if (! function_exists('artistfy')) {
    function artistfy(string $name): string
    {
        $validArtistNamePattern = "/[^\p{L}\p{N} _-]/u";

        return preg_replace($validArtistNamePattern, '', trim($name));
    }
}

if (! function_exists('logActivity')) {
    function logActivity(
        array $attributes = [],
        bool $deferred = true
    ) {
        if ($deferred) {
            defer(function () use ($attributes) {
                App\Models\Activity::create(attributes: $attributes);
            });
        } else {
            App\Models\Activity::create($attributes);
        }
    }
}

if (! function_exists('extractFeaturingArtists')) {
    /**
     * @return array<string>
     */
    function extractFeaturingArtists(string $songName): array
    {
        $pattern = '/(?:feat(?:uring)?\.|ft\.)\s*(?:\(|\[)?([^)\]]+)(?:\)|\])?/i';
        if (preg_match($pattern, $songName, $matches)) {
            $names = explode(',', trim($matches[1], ' ,'));

            if ($names === false) {
                return [];
            }

            foreach ($names as $key => $name) {
                $names[$key] = artistfy($name);
            }

            // Clean up the match by trimming whitespace and extra commas
            return $names;
        }

        return [];
    }
}
