<?php

namespace App\Observers;

use App\Enums\UserRole;
use App\Models\ArtistRequest;
use App\Models\User;
use App\Notifications;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class ArtistRequestObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the ArtistRequest "created" event.
     */
    public function created(ArtistRequest $artistRequest): void
    {
        User::where('role', UserRole::ADMIN->value)->cursor()
            ->each(function (User $user) use ($artistRequest) {
                $user->notify(
                    new Notifications\ArtistRequestCreated($artistRequest)
                );
            });
    }

    /**
     * Handle the ArtistRequest "updated" event.
     */
    public function updated(ArtistRequest $artistRequest): void
    {
        if ($artistRequest->approved) {
            $artistRequest->user->role = $artistRequest->artist_type;
            $artistRequest->user->save();

            $artistRequest->user->notify(
                new Notifications\ArtistRequestApproved($artistRequest)
            );
        }
    }

    /**
     * Handle the ArtistRequest "deleted" event.
     */
    public function deleted(ArtistRequest $artistRequest): void
    {
        $artistRequest->user->notify(
            new Notifications\ArtistRequestRejected($artistRequest)
        );
    }

    /**
     * Handle the ArtistRequest "force deleted" event.
     */
    public function forceDeleted(ArtistRequest $artistRequest): void
    {
        $artistRequest->user->notify(
            new Notifications\ArtistRequestRejected($artistRequest)
        );
    }

    /**
     * Handle the ArtistRequest "restored" event.
     */
    public function restored(ArtistRequest $artistRequest): void
    {
        //
    }
}
