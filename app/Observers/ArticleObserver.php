<?php

namespace App\Observers;

use App\Models\Article;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class ArticleObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Article "created" event.
     */
    public function created(Article $article): void
    {
        $this->updateArticleChannelGenre($article);
    }

    /**
     * Handle the Article "updated" event.
     */
    public function updated(Article $article): void
    {
        $this->updateArticleChannelGenre($article);
    }

    public function updateArticleChannelGenre(Article $article)
    {
        $channel = $article->channel;
        $channelArticle = $channel->articles()->first();

        if (filled($channelArticle) && $channelArticle->genre) {
            $channel->fill([
                'genre_id' => $channelArticle->genre_id,
            ]);

            $channel->saveQuietly();
        }

        // Refresh the channel to ensure the genre is updated
        $channel->refresh();
        $channel->load('genre');

        // If the current article does not have a genre, we use the channel's genre
        if (blank($article->genre_id) && $channel->genre) {
            $article->fill([
                'genre_id' => $channel->genre_id,
            ]);

            $article->saveQuietly();
        }
    }

    /**
     * Handle the Article "deleted" event.
     */
    public function deleted(Article $article): void
    {
        //
    }

    /**
     * Handle the Article "restored" event.
     */
    public function restored(Article $article): void
    {
        //
    }

    /**
     * Handle the Article "force deleted" event.
     */
    public function forceDeleted(Article $article): void
    {
        $article->likes()->delete();
        $article->downloads()->delete();
    }
}
