<?php

namespace App\Observers;

use App\Models\CompetitionEntry;
use App\Models\User;
use App\Notifications\CompetitionEntryStatusUpdated;
use App\Notifications\NewCompetitionEntry as NewCompetitionEntryNotification;
use Illuminate\Support\Facades\Log;

class CompetitionEntryObserver
{
    /**
     * Handle the CompetitionEntry "created" event.
     */
    public function created(CompetitionEntry $competitionEntry): void
    {

        User::admins()
            ->cursor()
            ->each(function (User $user) use ($competitionEntry) {
                $user->notify(
                    new NewCompetitionEntryNotification($competitionEntry)
                );
            });
    }

    /**
     * Handle the CompetitionEntry "updated" event.
     */
    public function updated(CompetitionEntry $competitionEntry): void
    {
        // Check if the status was actually changed to avoid sending notifications on other updates.
        if ($competitionEntry->isDirty('status')) {
            $artist = $competitionEntry->user;
            if ($artist) {
                try {
                    $artist->notify(new CompetitionEntryStatusUpdated($competitionEntry));
                } catch (\Exception $e) {
                    Log::error("Failed to send CompetitionEntryStatusUpdated notification to user {$artist->id} for entry {$competitionEntry->id} on update: ".$e->getMessage());
                }
            }
        }
    }

    /**
     * Handle the CompetitionEntry "deleted" event.
     * This is for soft deletes. For hard deletes, use forceDeleted.
     */
    public function deleted(CompetitionEntry $competitionEntry): void
    {
        $artist = $competitionEntry->user;
        if ($artist) {
            try {
                // Pass 'deleted' as status override so the notification can tailor the message
                $artist->notify(new CompetitionEntryStatusUpdated($competitionEntry, 'deleted'));
            } catch (\Exception $e) {
                Log::error("Failed to send CompetitionEntryStatusUpdated notification to user {$artist->id} for entry {$competitionEntry->id} on delete: ".$e->getMessage());
            }
        }
    }

    /**
     * Handle the CompetitionEntry "restored" event.
     */
    public function restored(CompetitionEntry $competitionEntry): void
    {
        //
    }

    /**
     * Handle the CompetitionEntry "force deleted" event.
     */
    public function forceDeleted(CompetitionEntry $competitionEntry): void
    {
        //
    }
}
