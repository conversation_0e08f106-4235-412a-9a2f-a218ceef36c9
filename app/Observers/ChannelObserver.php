<?php

namespace App\Observers;

use App\Enums\ChannelType;
use App\Models\Article;
use App\Models\Channel;

class ChannelObserver
{
    /**
     * Handle the Channel "created" event.
     */
    public function created(Channel $channel): void
    {
        //
    }

    /**
     * Handle the Channel "updated" event.
     */
    public function updated(Channel $channel): void
    {
        $channel->articles()->lazy()
            ->each(function (Article $article) use ($channel) {
                $article->fill(['user_id' => $channel->user_id]);
                $article->saveQuietly();
            });

        $this->updateChannelType($channel);
    }

    public function updateChannelType(Channel $channel)
    {
        // If channel is type SINGLE and has more one article
        if ($channel->type !== ChannelType::SINGLE) {
            return;
        }

        if ($channel->articles()->count() > 1) {
            $channel->fill([
                'type' => ChannelType::ALBUM,
            ]);

            $channel->saveQuietly();
        }
    }

    /**
     * Handle the Channel "deleted" event.
     */
    public function deleted(Channel $channel): void
    {
        //
    }

    /**
     * Handle the Channel "restored" event.
     */
    public function restored(Channel $channel): void
    {
        //
    }

    /**
     * Handle the Channel "force deleted" event.
     */
    public function forceDeleted(Channel $channel): void
    {
        $channel->articles()->delete();
        $channel->likes()->delete();
        $channel->downloads()->delete();
    }
}
