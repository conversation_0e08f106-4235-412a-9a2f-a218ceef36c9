<?php

namespace App\Observers;

use App\Models\Playlist\Playlist;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class PlaylistObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Playlist "created" event.
     */
    public function created(Playlist $Playlist): void
    {
        //
    }

    /**
     * Handle the Playlist "updated" event.
     */
    public function updated(Playlist $Playlist): void
    {
        //
    }

    /**
     * Handle the Playlist "deleted" event.
     */
    public function deleted(Playlist $Playlist): void
    {
        //
    }

    /**
     * Handle the Playlist "restored" event.
     */
    public function restored(Playlist $Playlist): void
    {
        //
    }

    /**
     * Handle the Playlist "force deleted" event.
     */
    public function forceDeleted(Playlist $Playlist): void
    {
        $Playlist->items()->delete();

        if ($Playlist->image) {
            $Playlist->image->delete();
        }
    }
}
