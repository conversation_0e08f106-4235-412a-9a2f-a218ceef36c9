<?php

namespace App\Observers;

use App\Actions\Audio\StoreAudioDuration;
use App\Jobs\FixAudioFileFormatJob;
use App\Media\Image\OptimizableImage;
use App\Media\TmpFile;
use App\Models\File\Audio;
use App\Models\File\Image;
use App\Models\Morph\File;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class FileObserver implements ShouldHandleEventsAfterCommit
{
    public function __construct(private OptimizableImage $compressor) {}

    /**
     * Handle the File "creating" event.
     */
    public function creating(File $file): void
    {
        $authId = auth()->id();
        if ($authId !== null) {
            $file->created_by = $authId;
        }
    }

    /**
     * Handle the File "created" event.
     */
    public function created(File $file): void
    {
        defer(function () use ($file) {
            $fs = $this->storage($file);

            $exists = $fs->exists($file->filename_disk);
            if ($exists === false) {
                return;
            }

            $this->optimizeImage(file: $file);

            $this->storeMp3Duration(file: $file);

            $this->checkAndDispatchFixJob(file: $file);
        });
    }

    /**
     * Handle the File "updating" event.
     */
    public function updating(File $file): void
    {
        $authId = auth()->id();
        if ($authId !== null) {
            $file->updated_by = $authId;
        }
    }

    /**
     * Handle the File "updated" event.
     */
    public function updated(File $file): void
    {
        defer(function () use ($file) {
            $fs = $this->storage($file);

            $exists = $fs->exists($file->filename_disk);
            if ($exists === false) {
                return;
            }

            $this->optimizeImage(file: $file);

            $this->storeMp3Duration(file: $file);
        });
    }

    /**
     * Handle the File "deleted" event.
     */
    public function deleted(File $file): void
    {
        //
    }

    /**
     * Handle the File "restored" event.
     */
    public function restored(File $file): void
    {
        //
    }

    /**
     * Handle the File "force deleted" event.
     */
    public function forceDeleted(File $file): void
    {
        $fs = $this->storage($file);

        // doing some cleanup
        $fs->delete($file->filename_disk);
        if ($file->hls_directory) {
            $fs->deleteDirectory($file->hls_directory);
        }
    }

    /**
     * Store mp3 file duration
     *
     * @return void
     */
    private function storeMp3Duration(File $file)
    {
        if ($this->isMp3($file->type) === false) {
            return;
        }

        $action = new StoreAudioDuration($file);
        $action->execute();
    }

    /**
     * Optimize image by compression
     */
    private function optimizeImage(File $file): void
    {
        if ($this->isImage($file->type) === false) {
            return;
        }

        $tmpFile = new TmpFile($file);

        $this->compressor->compress_image(
            source_url: $tmpFile->path(),
            destination_url: null,
            widths: 650,
            quality: 50
        );

        // Update file system with compressed image
        $this->storage($file)->put(
            path: $file->filename_disk,
            contents: $tmpFile->readStream()
        );
    }

    /**
     * Checks if the file needs fixing and dispatches the job.
     */
    protected function checkAndDispatchFixJob(File $file): void
    {
        $folderPrefix = rtrim(Audio::FOLDER, '/').'/'; // Ensure trailing slash e.g., 'audios/'
        $expectedMime = 'application/octet-stream';
        $problemExtension = '.bin';

        // Condition checks
        $isOctetStream = $file->type === $expectedMime;
        $isInAudioFolder = Str::startsWith($file->filename_disk, $folderPrefix);
        $hasBinExtension = Str::endsWith($file->filename_disk, $problemExtension);

        if ($isOctetStream && $isInAudioFolder && $hasBinExtension) {
            Log::info("Dispatching FixAudioFileFormatJob for File ID: {$file->id}");
            // Dispatch the job, passing the file ID
            FixAudioFileFormatJob::dispatch($file->id);
        }
    }

    private function storage(File $file)
    {
        return \Storage::disk($file->storage);
    }

    /**
     * @param  string|null  $mimeType
     */
    private function isMp3(string $mimeType): bool
    {
        $mimeType = Str::lower($mimeType);
        if (in_array($mimeType, Audio::AUDIO_MIME_TYPES)) {
            return true;
        }

        return false;
    }

    /**
     * @param  string|null  $mimeType
     */
    private function isImage(string $mimeType): bool
    {
        $mimeType = Str::lower($mimeType);
        if (in_array($mimeType, Image::IMAGE_MIME_TYPES)) {
            return true;
        }

        return false;
    }
}
