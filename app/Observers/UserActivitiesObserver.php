<?php

namespace App\Observers;

use Illuminate\Database\Eloquent\Model;

class UserActivitiesObserver
{
    /**
     * Handle the Model "created" event.
     */
    public function created(Model $model): void
    {
        $this->storeActivity($model, 'ModelCreate');
    }

    /**
     * Handle the Model "updated" event.
     */
    public function updated(Model $model): void
    {
        $this->storeActivity($model, 'ModelUpdate');
    }

    /**
     * Handle the Model "deleted" event.
     */
    public function deleted(Model $model): void
    {
        $this->storeActivity($model, 'ModelDelete');
    }

    /**
     * Handle the Model "restored" event.
     */
    public function restored(Model $model): void
    {
        $this->storeActivity($model, 'ModelRestore');
    }

    /**
     * Handle the Model "force deleted" event.
     */
    public function forceDeleted(Model $model): void
    {
        $this->storeActivity($model, 'ModelForceDelete');
    }

    private function storeActivity(Model $model, string $description)
    {
        $user = auth()->user();

        if ($user === null) {
            return;
        }

        logActivity([
            'user_id' => $user->id,
            'model' => $model->getTable(),
            'json' => $model->toArray(),
            'description' => $description,
        ]);
    }
}
