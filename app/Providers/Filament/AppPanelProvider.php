<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Auth\EditProfile;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\UserMenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('app')
            ->path('dash')
            ->emailVerification()
            ->profile(
                EditProfile::class,
                isSimple: false,
            )
            ->favicon(asset('logo.png'))
            ->colors([
                'primary' => Color::Green,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])

            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
            ])
            ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
            ->databaseNotifications()
            ->userMenuItems([
                'application' => UserMenuItem::make()
                    ->label('Application')
                    ->icon('heroicon-o-squares-2x2')
                    ->url(function () {
                        return route('app.discover');
                    }),

                'telescope' => UserMenuItem::make()
                    ->label('Telescope')
                    ->icon('heroicon-o-code-bracket-square')
                    ->url(fn () => route('telescope'))
                    ->visible(fn () => auth()->user()->isAdmin() ?? false),

                'pulse' => UserMenuItem::make()
                    ->label('Pulse')
                    ->icon('heroicon-o-computer-desktop')
                    ->url(fn () => route('pulse'))
                    ->visible(fn () => auth()->user()->isAdmin() ?? false),

                'horizon' => UserMenuItem::make()
                    ->label('Horizon')
                    ->icon('heroicon-o-queue-list')
                    ->url(fn () => route('horizon.index'))
                    ->visible(fn () => auth()->user()->isAdmin() ?? false),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
