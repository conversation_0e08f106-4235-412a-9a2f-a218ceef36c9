<?php

namespace App\Providers;

use App\Media\Image\ImageCompression;
use App\Media\Image\OptimizableImage;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Pulse\Facades\Pulse;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(OptimizableImage::class, ImageCompression::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (app()->isProduction()) {
            \URL::forceScheme('https');
        }

        Pulse::user(fn (User $user) => [
            'name' => $user->name,
            'extra' => $user->email,
            'avatar' => $user->image?->url,
        ]);

        Gate::define('viewApiDocs', function () {
            return true;
        });

        Gate::define('viewPulse', function (User $user) {
            return $user->isAdmin();
        });

        Model::automaticallyEagerLoadRelationships();

        JsonResource::withoutWrapping();
    }
}
