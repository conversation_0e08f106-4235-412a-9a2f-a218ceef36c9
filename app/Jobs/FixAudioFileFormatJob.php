<?php

namespace App\Jobs;

use App\Media\TmpFile;
use App\Models\Morph\File;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Throwable;

class FixAudioFileFormatJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $fileId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $fileId)
    {
        $this->fileId = $fileId;
        // $this->onQueue('audio-processing');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->batch()?->cancelled()) {
            return;
        }

        Log::info("Starting FixAudioFileFormatJob for File ID: {$this->fileId}");

        $file = File::find($this->fileId);

        if (! $file) {
            Log::warning("FixAudioFileFormatJob: File not found for ID: {$this->fileId}");

            return;
        }

        // Double-check conditions in case the file was fixed elsewhere or state changed
        $problemExtension = '.bin';
        if (! Str::endsWith($file->filename_disk, $problemExtension) || $file->type !== 'application/octet-stream') {
            Log::warning("FixAudioFileFormatJob: File ID {$this->fileId} no longer meets fixing conditions. Skipping.");

            return;
        }

        $tempOriginal = new TmpFile($file);
        $originalPath = $file->filename_disk; // e.g., 'audios/xyz.bin'

        $newExtension = '.mp3'; // Target extension
        $newPath = Str::replaceLast($problemExtension, $newExtension, $originalPath); // e.g., 'audios/xyz.mp3'
        $newMimeType = 'audio/mpeg';

        try {
            // We need a local temporary path to run ffmpeg
            $tempConverted = tempnam(sys_get_temp_dir(), 'audio_converted_').$newExtension;

            Log::info("FixAudioFileFormatJob [{$this->fileId}]: Running FFmpeg to convert {$originalPath} to {$tempConverted}");

            // Basic FFmpeg command: Tries to copy audio stream if possible (faster), else re-encodes.
            $process = new Process([
                'ffmpeg',
                '-i',
                $tempOriginal->path(),        // Input file
                '-vn',                        // No video
                // Attempt codec copy first for speed if it's already a compatible format
                '-acodec',
                'copy',
                $tempConverted,               // Output file
            ]);
            $process->setTimeout(360);
            $process->run();

            if (! $process->isSuccessful()) {
                // Maybe codec copy failed? Try forcing re-encoding as a fallback?
                Log::warning("FixAudioFileFormatJob [{$this->fileId}]: FFmpeg codec copy failed. Trying forced re-encoding.");
                $processReEncode = new Process([
                    'ffmpeg',
                    '-i',
                    $tempOriginal->path(),
                    '-vn',
                    '-acodec',
                    'libmp3lame', // Force MP3 encoding
                    '-ar',
                    '44100',         // Audio sample rate
                    '-ab',
                    '192k',          // Audio bitrate
                    $tempConverted,
                ]);
                $processReEncode->setTimeout(360);
                $processReEncode->run();

                if (! $processReEncode->isSuccessful()) {
                    throw new ProcessFailedException($processReEncode); // Throw error if re-encoding also fails
                }
            }

            // Check if the converted file has content
            if (! file_exists($tempConverted) || filesize($tempConverted) === 0) {
                throw new \Exception("FFmpeg conversion resulted in an empty file for File ID: {$this->fileId}");
            }

            Log::info("FixAudioFileFormatJob [{$this->fileId}]: FFmpeg successful. Uploading {$newPath} to disk '{$file->storage}'");

            // Upload the converted file (overwrite if necessary)
            $disk = Storage::disk($file->storage);
            $disk->put($newPath, fopen($tempConverted, 'r+'));

            Log::info("FixAudioFileFormatJob [{$this->fileId}]: Deleting original file {$originalPath} from disk '{$file->storage}'");
            $disk->delete($originalPath);

            // Update database record
            $file->update([
                'filename_disk' => $newPath,
                'filename_download' => $newPath,
                'type' => $newMimeType,
                'filesize' => $disk->size($newPath),
                'url' => $disk->url($newPath),
                'location' => $disk->path($newPath),
            ]);

            Log::info("FixAudioFileFormatJob [{$this->fileId}]: Successfully processed and updated File record.");

        } catch (ProcessFailedException $exception) {
            Log::error("FixAudioFileFormatJob [{$this->fileId}]: FFmpeg process failed.");
            Log::error('Command: '.$exception->getProcess()->getCommandLine());
            Log::error('Output: '.$exception->getProcess()->getErrorOutput());
            // Optionally rethrow or fail the job
            $this->fail($exception); // Mark job as failed
        } catch (Throwable $e) { // Catch any other exception
            Log::error("FixAudioFileFormatJob [{$this->fileId}]: An error occurred: ".$e->getMessage(), [
                'exception' => $e,
            ]);
            // Optionally rethrow or fail the job
            $this->fail($e); // Mark job as failed
        } finally {
            // Clean up temporary files
            if (isset($tempConverted) && file_exists($tempConverted)) {
                unlink($tempConverted);
            }
        }
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public function backoff(): array
    {
        return [60, 180];
    }

    /**
     * Determine number of times the job may be attempted.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        // Send notification, log extensively, etc.
        Log::critical("FixAudioFileFormatJob for File ID {$this->fileId} has failed permanently.", [
            'exception' => $exception,
        ]);
    }
}
