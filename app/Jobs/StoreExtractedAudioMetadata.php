<?php

namespace App\Jobs;

use App\Events\ArticleUpdated;
use App\Models\Article;
use App\Models\ArticleAudioMetadata;
use App\Models\File\Audio;
use App\Services\AudioMetadataExtractorService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class StoreExtractedAudioMetadata implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Audio $file) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->batch()?->cancelled()) {
            return;
        }

        $article = $this->file->fileable;

        if (
            in_array($this->file->type, $this->file::AUDIO_MIME_TYPES) === false ||
            ! ($article instanceof Article)
        ) {
            return;
        }

        $service = new AudioMetadataExtractorService(
            audio: $this->file,
            filesystem: Storage::disk($this->file->storage)
        );

        $dataObject = (object) $service->handle();

        $year = $dataObject->tags['year'] ? explode('-', $dataObject->tags['year'])[0] ?? null : null;

        ArticleAudioMetadata::firstOrCreate(
            [
                'article_id' => $article->id,
                'file_id' => $this->file->id,
            ],
            [
                'tempo' => $dataObject->tempo,
                'bitrate' => $dataObject->tags['bitrate'] ?? 0,
                'sample_rate' => $dataObject->tags['samplerate'] ?? 0,
                'channels' => $dataObject->tags['channels'] ?? 2,
                'duration' => $dataObject->tags['duration'] ?? 0,
                'layer' => 3,
                'year' => filled($year) ? trim($year) : null,
                'json' => (array) $dataObject,
            ]
        );

        event(new ArticleUpdated($article));
    }
}
