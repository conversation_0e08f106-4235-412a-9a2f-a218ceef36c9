<?php

namespace App\Jobs\Embedding;

use App\Media\Audio\MpegToWave16Bit;
use App\Media\TmpFile;
use App\Models\Article;
use App\Models\Embedding\ArticleAudioEmbedding;
use App\Models\File\Audio;
use App\Services\AudioEmbeddingGeneratorService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class StorePredictedAudioEmbedding implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600;

    /**
     * The number of seconds of audio to process
     */
    private int $audioSeconds;

    /**
     * The embedding dimension, read from config.
     */
    private int $embeddingDimension;

    /**
     * Create a new job instance.
     */
    public function __construct(public readonly Audio $file)
    {
        if ($this->batch()?->cancelled()) {
            return;
        }

        // Read the dimension from the configuration file on job instantiation
        $this->embeddingDimension = (int) config('embeddings.audio_dimension', 128); // Added default for safety
        $this->audioSeconds = (int) config('embeddings.max_audio_length', 120); // Added default for safety

        // Log a warning if the config is missing or invalid, falling back to default
        if (config('embeddings.audio_dimension') === null) {
            Log::warning('Configuration embeddings.audio_dimension not found, defaulting to 128.');
        } elseif ($this->embeddingDimension <= 0) {
            Log::error('Invalid configuration value for embeddings.audio_dimension. Must be a positive integer.');
            $this->fail('Invalid configuration value for embeddings.audio_dimension. Must be a positive integer.');
        }
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->batch()?->cancelled()) {
            return;
        }

        if (! $this->isValidFile()) {
            return;
        }

        $this->processAudioEmbedding($this->file->fileable);
    }

    /**
     * Check if file is valid for processing
     */
    private function isValidFile(): bool
    {
        return in_array($this->file->type, Audio::AUDIO_MIME_TYPES, true) &&
            $this->file->fileable instanceof Article;
    }

    /**
     * Process audio and store embedding
     */
    private function processAudioEmbedding(Article $article): void
    {
        $tmpFile = new TmpFile($this->file);

        $audioSeconds = $this->audioSeconds === 0 ? null : $this->audioSeconds;
        $waveConverter = new MpegToWave16Bit(
            input: $tmpFile->path(),
            length: $audioSeconds, // Convert only the first N seconds
        );

        $finalEmbedding = [];

        try {
            $finalEmbedding = $this->generateEmbedding($waveConverter->convert());
        } catch (\Exception $e) {
            Log::error("Failed to generate embedding for audio file ID: {$this->file->id}. Error: {$e->getMessage()}");
            $this->fail($e);
        } finally {
            $tmpFile->close();
            $waveConverter->clearIfOwned();
        }

        if (empty($finalEmbedding)) {
            Log::error("Empty or invalid embedding generated after processing for audio file ID: {$this->file->id}");

            return;
        }

        // Use the dimension read from config for validation
        if (count($finalEmbedding) !== $this->embeddingDimension) {
            Log::critical("Final embedding dimension is incorrect before saving! File ID: {$this->file->id}. Expected: ".$this->embeddingDimension.', Got: '.count($finalEmbedding));

            return;
        }

        $this->storeEmbedding($article, $finalEmbedding);
    }

    /**
     * Generate a fixed-size embedding by averaging per-chunk embeddings.
     * Dimension is read from config via $this->embeddingDimension.
     */
    private function generateEmbedding(string $waveFile): array
    {
        $service = new AudioEmbeddingGeneratorService($waveFile);
        $serviceResponse = (object) $service->handle();

        if (! isset($serviceResponse->embedding) || ! is_array($serviceResponse->embedding) || empty($serviceResponse->embedding)) {
            Log::warning("AudioEmbeddingGeneratorService returned empty or invalid embedding array for {$waveFile}.");

            return [];
        }

        $embeddingsList = $serviceResponse->embedding;

        if (! is_array($embeddingsList[0])) {
            Log::error("AudioEmbeddingGeneratorService returned unexpected format. Expected array of arrays for {$waveFile}.");
            if (count($embeddingsList) === $this->embeddingDimension) {
                return $embeddingsList;
            }

            return [];
        }

        $firstVectorDim = count($embeddingsList[0]);

        // Use the dimension read from config for validation
        if ($firstVectorDim !== $this->embeddingDimension) {
            Log::error("Embedding dimension mismatch in service response for {$waveFile}. Expected: ".$this->embeddingDimension.', Got: '.$firstVectorDim);

            return [];
        }

        $averagedEmbedding = array_fill(0, $this->embeddingDimension, 0.0);
        $validVectorsCount = 0; // Count only vectors with correct dimension

        foreach ($embeddingsList as $vector) {
            if (count($vector) !== $this->embeddingDimension) {
                Log::warning("Skipping vector with incorrect dimension during averaging for {$waveFile}.");

                continue;
            }
            for ($i = 0; $i < $this->embeddingDimension; $i++) {
                $averagedEmbedding[$i] += (float) ($vector[$i] ?? 0.0);
            }
            $validVectorsCount++; // Increment count for valid vectors
        }

        if ($validVectorsCount > 0) {
            for ($i = 0; $i < $this->embeddingDimension; $i++) {
                $averagedEmbedding[$i] /= $validVectorsCount; // Divide by the count of VALID vectors
            }
        } else {
            Log::error("Averaging failed: No valid vectors found for {$waveFile}.");

            return [];
        }

        return $averagedEmbedding;
    }

    /**
     * Store embedding in database
     */
    private function storeEmbedding(Article $article, array $embedding): void
    {
        ArticleAudioEmbedding::updateOrCreate(
            ['article_id' => $article->id, 'file_id' => $this->file->id],
            ['embedding' => $embedding]
        );
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        $articleName = $this->file->fileable?->name ?? 'unknown';

        return ['audio-embedding', "article:{$articleName}"];
    }
}
