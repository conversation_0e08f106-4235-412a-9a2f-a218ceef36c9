<?php

namespace App\Jobs\Embedding;

use App\Embedding\ArticlePrepareForEmbedding;
use App\Models;
use App\Services\TextEmbedderService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StoreExtractedArticleMetadataEmbedding implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Models\Article $article) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->batch()?->cancelled()) {
            return;
        }

        $preparation = new ArticlePrepareForEmbedding($this->article);
        $summaryText = $preparation->getCategoricalMetadata();

        $service = new TextEmbedderService($summaryText);
        $serviceResponse = (object) $service->handle();

        Models\Embedding\ArticleMetadataEmbedding::updateOrCreate(
            attributes: ['article_id' => $this->article->id],
            values: [
                'embedding' => $serviceResponse->embedding,
                'text' => $summaryText,
                'with_metadata' => $preparation->hasAudioMetadata(),
            ]
        );
    }
}
