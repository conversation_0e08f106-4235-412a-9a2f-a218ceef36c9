<?php

namespace App\Jobs\Recommendation;

use App\Models\User;
use App\Services\RecommendationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateSingleUserRecommendationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // Increase timeouts if recommendation generation is slow
    public $timeout = 600; // 10 minutes

    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(public User $user)
    {
        // Optional: Set a specific queue
        $this->onQueue('recommender');
    }

    /**
     * Execute the job.
     */
    public function handle(RecommendationService $recommendationService): void
    {
        try {
            $recommendationService->generateForUser($this->user);
        } catch (\Throwable $e) {
            Log::error("Failed to generate recommendations for User ID: {$this->user->id}. Error: {$e->getMessage()}", [
                'exception' => $e,
            ]);
            // Optionally re-throw or handle failure (e.g., notify admin)
            $this->fail($e);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['recommendation', 'user:'.$this->user->id];
    }
}
