<?php

namespace App\Jobs\Recommendation;

use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateUserRecommendationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // Increase timeouts if processing many users takes time
    public $timeout = 3600; // 1 hour

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        // Optional: Set a specific queue
        $this->onQueue('recommender');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting batch recommendation generation job.');

        // Option 2: Only users active recently (e.g., played something in last 30 days)
        User::whereHas('playings', function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })
            ->orWhereHas('likes', function ($query) {
                $query->where('created_at', '>=', now()->subDays(30));
            })
            ->orWhereHas('followings', function ($query) {
                $query->where('created_at', '>=', now()->subDays(30));
            })
            ->chunk(200, function ($users) { // chunkById is generally safer for long running jobs
                Log::info('Dispatching recommendation jobs for chunk of '.$users->count().' users.');
                foreach ($users as $user) {
                    GenerateSingleUserRecommendationJob::dispatch($user);
                }
            });

        Log::info('Finished dispatching all user recommendation jobs.');
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['recommendation', 'batch-scheduler'];
    }
}
