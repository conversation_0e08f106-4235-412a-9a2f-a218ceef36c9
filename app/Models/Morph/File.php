<?php

namespace App\Models\Morph;

use App\Models\User;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperFile
 */
#[ObservedBy([
    Observers\FileObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class File extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'storage',
        'filename_disk',
        'filename_download',
        'type',
        'filesize',
        'url',
        'width',
        'height',
        'duration',
        'description',
        'location',
        'tags',
        'metadata',
        'hls_directory',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'tags' => '[]',
        'metadata' => '{}',
    ];

    /**
     * Get the file url.
     */
    protected function url(): Attribute
    {
        return Attribute::make(
            get: fn () => \Storage::disk($this->storage)->url($this->filename_disk),
        );
    }

    /**
     * Get the parent fileable model (user or post).
     */
    public function fileable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user that owns the file.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user that modify the file.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
