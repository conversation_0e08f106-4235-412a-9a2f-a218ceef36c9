<?php

namespace App\Models\Morph;

use App\Models\User;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @mixin IdeHelperDownload
 */
#[ObservedBy([
    Observers\UserActivitiesObserver::class,
])]
class Download extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
    ];

    /**
     * Get the parent downloadable model.
     */
    public function downloadable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the download's owner.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
