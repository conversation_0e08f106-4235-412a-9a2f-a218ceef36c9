<?php

namespace App\Models;

use App\Models\File\Audio;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperArticleAudioMetadata
 */
class ArticleAudioMetadata extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'article_id',
        'file_id',
        'bitrate',
        'sample_rate',
        'channels',
        'tempo',
        'duration',
        'layer',
        'score',
        'encoder_info',
        'encoder_settings',
        'date',
        'year',
        'comment',
        'json',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'json' => 'array',
        'date' => 'immutable_date',
    ];

    /**
     * Get the audio.
     */
    public function audio(): BelongsTo
    {
        return $this->belongsTo(Audio::class, 'file_id');
    }

    /**
     * Get the article.
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(Article::class);
    }
}
