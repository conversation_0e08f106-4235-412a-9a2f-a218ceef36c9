<?php

namespace App\Models\Scopes;

use App\Models\File\Audio;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class AudioFileScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $builder->whereIn('type', Audio::AUDIO_MIME_TYPES);
    }
}
