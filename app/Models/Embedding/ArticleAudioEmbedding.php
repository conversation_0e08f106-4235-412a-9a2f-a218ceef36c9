<?php

namespace App\Models\Embedding;

use App\Models\Article;
use App\Models\File\Audio;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Pgvector\Laravel\HasNeighbors;
use Pgvector\Laravel\Vector;

/**
 * @mixin IdeHelperArticleAudioEmbedding
 */
class ArticleAudioEmbedding extends Model
{
    use HasFactory, HasNeighbors, HasUlids;

    protected $casts = [
        'embedding' => Vector::class,
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'article_id',
        'file_id',
        'embedding',
    ];

    /**
     * Get the audio.
     */
    public function audio(): BelongsTo
    {
        return $this->belongsTo(Audio::class, 'file_id');
    }

    /**
     * Get the article.
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(Article::class);
    }
}
