<?php

namespace App\Models;

use App\Models\File\Image;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Competition model for artist voting competitions
 *
 * @mixin IdeHelperCompetition
 */
#[ObservedBy([
    Observers\UserActivitiesObserver::class,
])]
class Competition extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'status',
        'type',
        'stage',
        'requirements',
        'vote_price',
        'auto_approve',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'requirements' => 'array',
        'status' => 'boolean',
        'vote_price' => 'decimal:2',
        'auto_approve' => 'boolean',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'is_in_voting_phase',
        'is_accepting_entries',
        'is_in_entry_phase',
        'phase_status',
    ];

    /**
     * Scope a query to only include active competitions (voting phase).
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());
    }

    /**
     * Scope a query to include competitions that allow entry submission.
     * This includes competitions that haven't started yet but are still accepting entries.
     */
    public function scopeAcceptingEntries(Builder $query): void
    {
        $query->where('status', true)
            ->where('end_date', '>=', now());
    }

    /**
     * Check if the competition is currently in the voting phase.
     */
    public function isInVotingPhase(): bool
    {
        return $this->status
            && now() >= $this->start_date
            && now() <= $this->end_date;
    }

    /**
     * Check if the competition is accepting entries.
     */
    public function isAcceptingEntries(): bool
    {
        return $this->status && now() <= $this->end_date;
    }

    /**
     * Check if the competition hasn't started yet but is accepting entries.
     */
    public function isInEntryPhase(): bool
    {
        return $this->status
            && now() < $this->start_date
            && now() <= $this->end_date;
    }

    /**
     * Get the is_in_voting_phase attribute for API responses.
     */
    public function getIsInVotingPhaseAttribute(): bool
    {
        return $this->isInVotingPhase();
    }

    /**
     * Get the is_accepting_entries attribute for API responses.
     */
    public function getIsAcceptingEntriesAttribute(): bool
    {
        return $this->isAcceptingEntries();
    }

    /**
     * Get the is_in_entry_phase attribute for API responses.
     */
    public function getIsInEntryPhaseAttribute(): bool
    {
        return $this->isInEntryPhase();
    }

    /**
     * Get the phase_status attribute for API responses.
     */
    public function getPhaseStatusAttribute(): string
    {
        if (! $this->status) {
            return 'inactive';
        }

        if (now() > $this->end_date) {
            return 'ended';
        }

        if (now() < $this->start_date) {
            return 'entry_phase';
        }

        return 'voting_phase';
    }

    /**
     * Get the competition's image.
     */
    public function image(): MorphOne
    {
        return $this->morphOne(Image::class, 'fileable');
    }

    /**
     * Get the competition entries.
     */
    public function entries(): HasMany
    {
        return $this->hasMany(CompetitionEntry::class);
    }

    /**
     * Get the competition votes.
     */
    public function votes(): HasMany
    {
        return $this->hasMany(Vote::class);
    }
}
