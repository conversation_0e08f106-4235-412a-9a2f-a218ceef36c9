<?php

namespace App\Models;

use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * CompetitionEntry model for tracking artist entries in competitions
 *
 * @mixin IdeHelperCompetitionEntry
 */
#[ObservedBy([
    Observers\UserActivitiesObserver::class,
    Observers\CompetitionEntryObserver::class,
])]
class CompetitionEntry extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'competition_id',
        'user_id',
        'status',
        'entry_date',
        'requirements_met',
        'terms_agreed_at',
        'privacy_agreed_at',
        'rules_agreed_at',
        'agreement_ip_address',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'entry_date' => 'datetime',
        'requirements_met' => 'boolean',
        'terms_agreed_at' => 'datetime',
        'privacy_agreed_at' => 'datetime',
        'rules_agreed_at' => 'datetime',
    ];

    /**
     * Scope a query to only include approved entries.
     */
    public function scopeApproved(Builder $query): void
    {
        $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include pending entries.
     */
    public function scopePending(Builder $query): void
    {
        $query->where('status', 'pending');
    }

    /**
     * Get the competition that the entry belongs to.
     */
    public function competition(): BelongsTo
    {
        return $this->belongsTo(Competition::class);
    }

    /**
     * Get the user (artist) that the entry belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if all legal agreements have been completed.
     */
    public function hasCompletedLegalAgreements(): bool
    {
        return $this->terms_agreed_at !== null
            && $this->privacy_agreed_at !== null
            && $this->rules_agreed_at !== null;
    }

    /**
     * Record legal agreements for this entry.
     */
    public function recordLegalAgreements(?string $ipAddress = null): void
    {
        $now = now();
        $this->update([
            'terms_agreed_at' => $now,
            'privacy_agreed_at' => $now,
            'rules_agreed_at' => $now,
            'agreement_ip_address' => $ipAddress,
        ]);
    }

    /**
     * Scope to entries with completed legal agreements.
     */
    public function scopeWithLegalAgreements(Builder $query): void
    {
        $query->whereNotNull('terms_agreed_at')
            ->whereNotNull('privacy_agreed_at')
            ->whereNotNull('rules_agreed_at');
    }
}
