<?php

namespace App\Models;

use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * VoteTransaction model for tracking payment transactions for votes
 *
 * @mixin IdeHelperVoteTransaction
 */
#[ObservedBy([
    Observers\UserActivitiesObserver::class,
])]
class VoteTransaction extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'vote_id',
        'amount',
        'transaction_id',
        'status',
        'provider',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Get the vote that the transaction belongs to.
     */
    public function vote(): BelongsTo
    {
        return $this->belongsTo(Vote::class);
    }
}
