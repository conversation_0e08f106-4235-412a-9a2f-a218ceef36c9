<?php

namespace App\Models;

use App\Enums\ChannelType;
use App\Models\File\Image;
use App\Models\Morph\Comment;
use App\Models\Morph\Download;
use App\Models\Morph\Like;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;

/**
 * @mixin IdeHelperChannel
 */
#[ObservedBy([
    Observers\ChannelObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class Channel extends Model
{
    use HasFactory, HasUlids, Searchable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'genre_id',
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'type' => ChannelType::class,
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'user' => $this->user->name,
        ];
    }

    /**
     * Scope a query to only channel type podcast popular users.
     */
    public function scopePodcast(Builder $query): void
    {
        $query->where('type', ChannelType::CHANNEL->value);
    }

    /**
     * Interact with the channel's first name.
     */
    protected function likesCount(): Attribute
    {
        return Attribute::make(get: fn (?int $value): ?int => $value ?? null);
    }

    /**
     * Interact with the query's unique_query.
     */
    protected function uniqueQuery(): Attribute
    {
        return Attribute::make(get: fn (?float $value): ?float => $value ?? null);
    }

    /**
     * Get the channel's owner.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the channel's genre.
     */
    public function genre(): BelongsTo
    {
        return $this->belongsTo(Genre::class);
    }

    /**
     * Get the channel's image.
     */
    public function image(): MorphOne
    {
        return $this->morphOne(Image::class, 'fileable');
    }

    /**
     * Get the channel's likes.
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Get the channel's downloads.
     */
    public function downloads(): MorphMany
    {
        return $this->morphMany(Download::class, 'downloadable');
    }

    /**
     * Get the channel's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get the channels's articles.
     */
    public function articles(): HasMany
    {
        return $this->hasMany(Article::class);
    }
}
