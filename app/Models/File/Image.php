<?php

namespace App\Models\File;

use App\Models\Morph\File;
use App\Models\Scopes\ImageFileScope;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;

/**
 * @mixin IdeHelperImage
 */
#[ObservedBy([
    Observers\FileObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class Image extends File
{
    public const MAX_SIZE = 1024 * 20;

    public const MIN_SIZE = 10;

    public const VISIBILITY = 'public';

    public const FOLDER = 'images';

    public const IMAGE_MIME_TYPES = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
    ];

    public static function getDisk(): string
    {
        $default = config('filesystems.default');
        if ($default === 'local') {
            return 'public';
        }

        return $default;
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::addGlobalScope(new ImageFileScope);
    }
}
