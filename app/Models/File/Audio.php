<?php

namespace App\Models\File;

use App\Events\FileAudioCreated;
use App\Models\ArticleAudioMetadata;
use App\Models\Embedding\ArticleAudioEmbedding;
use App\Models\Morph\File;
use App\Models\Scopes\AudioFileScope;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @mixin IdeHelperAudio
 */
#[ObservedBy([
    Observers\FileObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class Audio extends File
{
    public const MAX_SIZE = 1024 * 50;

    public const MIN_SIZE = 100;

    public const VISIBILITY = 'private';

    public const FOLDER = 'audios';

    public const AUDIO_MIME_TYPES = [
        'audio/mpeg',
    ];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'created' => FileAudioCreated::class,
    ];

    /**
     * Get the file url.
     */
    protected function url(): Attribute
    {
        return Attribute::make(
            get: fn () => route('audio', ['file' => $this->id])
        );
    }

    public static function getDisk(): string
    {
        return config('filesystems.default');
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::addGlobalScope(new AudioFileScope);
    }

    /**
     * Get audio metadata relation
     */
    public function metadata(): HasOne
    {
        return $this->hasOne(ArticleAudioMetadata::class, 'file_id');
    }

    /**
     * Get audio embedding relation
     */
    public function embedding(): HasOne
    {
        return $this->hasOne(ArticleAudioEmbedding::class, 'file_id');
    }

    /**
     * Save hls directory
     */
    public function saveHlsDirectory(string $value): void
    {
        $this->hls_directory = $value;
        $this->saveQuietly(); // no need to trigger events
    }
}
