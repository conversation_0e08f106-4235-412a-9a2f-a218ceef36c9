<?php

namespace App\Models;

use App\Enums\UserRole;
use App\Models\File\Image;
use App\Models\Morph\Comment;
use App\Models\Morph\Like;
use App\Models\Playlist\Playlist;
use App\Notifications\Auth\VerifyEmailQueued;
use App\Observers;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Lab404\Impersonate\Models\Impersonate;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Scout\Searchable;

/**
 * @mixin IdeHelperUser
 */
#[ObservedBy(Observers\UserObserver::class)]
class User extends Authenticatable implements FilamentUser, HasAvatar, MustVerifyEmail
{
    use HasApiTokens, HasFactory, HasUlids, Impersonate, Notifiable, Searchable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'description',
        'verified',

        'country_name',
        'country_code',
        'timezone',
        'city',
        'region',

        'provider',
        'provider_id',
        'provider_token',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'role' => UserRole::class,
    ];

    /**
     * Send the queued email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new VerifyEmailQueued);
    }

    /**
     * Scope a query to only include popular users.
     */
    public function scopeArtist(Builder $query): void
    {
        $query->whereIn('role', $this->getAllArtistTypes());
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->id,
            'name' => $this->name,
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->isArtist();
    }

    public function isAdmin(): bool
    {
        return $this->role === UserRole::ADMIN;
    }

    public function isArtist(): bool
    {
        return in_array($this->role, [
            UserRole::ADMIN,
            UserRole::SINGER,
            UserRole::PODCASTER,
        ]);
    }

    /**
     * Scope a query to only include admin users.
     */
    public function scopeAdmins(Builder $query): void
    {
        $query->where('role', UserRole::ADMIN);
    }

    /**
     * Check if this user is the first registered admin user.
     * The first registered user automatically becomes admin via UserObserver.
     */
    public function isFirstAdmin(): bool
    {
        if (! $this->isAdmin()) {
            return false;
        }

        // Get the first user ever created (oldest by created_at)
        $firstUser = static::withTrashed()
            ->orderBy('created_at', 'asc')
            ->where('role', UserRole::ADMIN)
            ->first();

        return $firstUser && $firstUser->id === $this->id;
    }

    /**
     * @return array<string>
     */
    public static function getOnlyArtistTypes(): array
    {
        return [
            UserRole::SINGER->value,
            UserRole::PODCASTER->value,
        ];
    }

    /**
     * @return array<string>
     */
    public static function getAllArtistTypes(): array
    {
        return [
            UserRole::SINGER->value,
            UserRole::PODCASTER->value,
            UserRole::ADMIN->value,
        ];
    }

    public function isOnlyArtist(): bool
    {
        return in_array($this->role, [
            UserRole::SINGER,
            UserRole::PODCASTER,
        ]);
    }

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->image ? $this->image->url : $this->avatar_url;
    }

    public function getMonthlyPlays(): int
    {
        $monthlyPlay = $this->plays()->select(DB::raw("COUNT(id) as plays_count, TO_CHAR(created_at, 'Month') AS monthname"))
            ->groupByRaw("TO_CHAR(created_at, 'Month')")
            ->orderBy('plays_count', 'desc')
            ->limit(1)
            ->first();

        return $monthlyPlay?->plays_count ?? 0;
    }

    public function getMonthlyListeners(): int
    {
        $result = $this->plays()
            ->select(DB::raw("TO_CHAR(created_at, 'Month') as month"), DB::raw('COUNT(DISTINCT player_id) as unique_listeners'))
            ->whereNotNull('player_id')
            ->groupByRaw("TO_CHAR(created_at, 'Month')")
            ->orderBy('unique_listeners', 'desc')
            ->first();

        return $result?->unique_listeners ?? 0;
    }

    /**
     * get use monthly plays count.
     */
    protected function monthlyPlays(): Attribute
    {
        return Attribute::make(
            get: fn (): int => $this->getMonthlyPlays(),
        );
    }

    /**
     * get use monthly plays count.
     */
    protected function monthlyListeners(): Attribute
    {
        return Attribute::make(
            get: fn (): int => $this->getMonthlyListeners(),
        );
    }

    /**
     * Admins can impersonate other users
     */
    public function canImpersonate(): bool
    {
        return $this->isAdmin();
    }

    /**
     * No admin users can be impersonated
     */
    public function canBeImpersonated(): bool
    {
        return ! $this->isAdmin();
    }

    /**
     * Get the user's image.
     */
    public function image(): MorphOne
    {
        return $this->morphOne(Image::class, 'fileable');
    }

    /**
     * Get the channels.
     */
    public function channels(): HasMany
    {
        return $this->hasMany(Channel::class);
    }

    /**
     * Get the articles.
     */
    public function articles(): HasMany
    {
        return $this->hasMany(Article::class);
    }

    /**
     * Get the plays.
     */
    public function plays(): HasMany
    {
        return $this->hasMany(Play::class);
    }

    /**
     * Get the user playing.
     */
    public function playings(): HasMany
    {
        return $this->hasMany(Play::class, 'player_id');
    }

    /**
     * Get the comments.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get the user's artist featurings.
     */
    public function featurings(): HasMany
    {
        return $this->hasMany(Featuring::class);
    }

    /**
     * Get user artist followers
     */
    public function followers(): HasMany
    {
        return $this->hasMany(Following::class);
    }

    /**
     * Get user artist likes
     */
    public function likes(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    /**
     * Get user followings
     */
    public function followings(): HasMany
    {
        return $this->hasMany(Following::class, 'follower_id');
    }

    /**
     * Get user artist request
     */
    public function artistRequest(): HasOne
    {
        return $this->hasOne(ArtistRequest::class);
    }

    /**
     * Get the user's playlists.
     */
    public function playlists(): HasMany
    {
        return $this->hasMany(Playlist::class);
    }

    /**
     * Get the user's playlists. generated by the system for recommendation.
     */
    public function recommendedPlaylists(): HasMany
    {
        return $this->hasMany(Playlist::class)->where('is_system_generated', true);
    }

    /**
     * Get the user's playlists. created by the user.
     */
    public function userCreatedPlaylists(): HasMany
    {
        return $this->hasMany(Playlist::class)->where('is_system_generated', false);
    }

    /**
     * Get the competition entries for the user (as an artist).
     */
    public function competitionEntries(): HasMany
    {
        return $this->hasMany(CompetitionEntry::class);
    }

    /**
     * Get the votes cast by the user.
     */
    public function votesGiven(): HasMany
    {
        return $this->hasMany(Vote::class, 'user_id');
    }

    /**
     * Get the votes received by the user (as an artist).
     */
    public function votesReceived(): HasMany
    {
        return $this->hasMany(Vote::class, 'artist_id');
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription()
    {
        return $this->subscriptions()->active()->latest()->first();
    }

    /**
     * Check if the user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->subscriptions()->active()->exists();
    }

    /**
     * Check if the user meets the requirements for a competition stage.
     */
    public function meetsCompetitionRequirements(array $requirements): bool
    {
        $followersCount = $this->followers()->count();
        $monthlyListeners = $this->getMonthlyListeners();
        $publishedTracks = $this->articles()->count();

        $meetsFollowers = $followersCount >= ($requirements['min_followers'] ?? 0);
        $meetsListeners = $monthlyListeners >= ($requirements['min_monthly_listeners'] ?? 0);
        $meetsTracks = $publishedTracks >= ($requirements['min_tracks'] ?? 0);

        return $meetsFollowers && $meetsListeners && $meetsTracks;
    }
}
