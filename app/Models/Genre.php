<?php

namespace App\Models;

use App\Models\File\Image;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;

/**
 * @mixin IdeHelperGenre
 */
class Genre extends Model
{
    use HasFactory, HasUlids, Searchable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['name', 'category'];

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'category' => $this->category,
        ];
    }

    /**
     * Get the genre's image.
     */
    public function image(): MorphOne
    {
        return $this->morphOne(Image::class, 'fileable');
    }

    /**
     * Get the channels.
     */
    public function channels(): HasMany
    {
        return $this->hasMany(Channel::class);
    }

    /**
     * Get the articles.
     */
    public function articles(): HasMany
    {
        return $this->hasMany(Article::class);
    }
}
