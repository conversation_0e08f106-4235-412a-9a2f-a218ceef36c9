<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperUserAgreement
 */
class UserAgreement extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'user_id',
        'legal_document_id',
        'competition_id',
        'agreed_at',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'agreed_at' => 'datetime',
    ];

    /**
     * Get the user who agreed.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the legal document that was agreed to.
     */
    public function legalDocument(): BelongsTo
    {
        return $this->belongsTo(LegalDocument::class);
    }

    /**
     * Get the competition this agreement is for.
     */
    public function competition(): BelongsTo
    {
        return $this->belongsTo(Competition::class);
    }

    /**
     * Check if a user has agreed to a specific document for a competition.
     */
    public static function hasUserAgreed(string $userId, string $documentId, ?string $competitionId = null): bool
    {
        $query = static::where('user_id', $userId)
            ->where('legal_document_id', $documentId);

        if ($competitionId) {
            $query->where('competition_id', $competitionId);
        }

        return $query->exists();
    }

    /**
     * Record a user's agreement to a legal document.
     */
    public static function recordAgreement(
        string $userId,
        string $documentId,
        ?string $competitionId = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return static::create([
            'user_id' => $userId,
            'legal_document_id' => $documentId,
            'competition_id' => $competitionId,
            'agreed_at' => now(),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }
}
