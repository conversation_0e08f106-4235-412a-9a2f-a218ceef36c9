<?php

namespace App\Models;

use App\Enums\UserRole;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperArtistRequest
 */
#[ObservedBy([
    Observers\ArtistRequestObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class ArtistRequest extends Model
{
    use HasFactory, HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'description',
        'approved',
        'artist_type',
        'youtube_link',
        'social_links',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'artist_type' => UserRole::class,
        'social_links' => 'array',
    ];

    /**
     * Get the artist's user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
