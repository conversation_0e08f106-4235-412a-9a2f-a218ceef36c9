<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperLegalDocument
 */
class LegalDocument extends Model
{
    use HasFactory, HasUlids, SoftDeletes;

    protected $fillable = [
        'type',
        'title',
        'content',
        'version',
        'is_active',
        'effective_date',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'effective_date' => 'datetime',
        'version' => 'decimal:1',
    ];

    // Document types
    const TYPE_TERMS_OF_SERVICE = 'terms_of_service';

    const TYPE_PRIVACY_POLICY = 'privacy_policy';

    const TYPE_COMPETITION_RULES = 'competition_rules';

    public static function getTypes(): array
    {
        return [
            self::TYPE_TERMS_OF_SERVICE => 'Terms of Service',
            self::TYPE_PRIVACY_POLICY => 'Privacy Policy',
            self::TYPE_COMPETITION_RULES => 'Competition Rules',
        ];
    }

    /**
     * Get the active version of a document type.
     */
    public static function getActiveDocument(string $type): ?self
    {
        return static::where('type', $type)
            ->where('is_active', true)
            ->latest('version')
            ->first();
    }

    /**
     * Get the latest terms of service.
     */
    public static function getActiveTermsOfService(): ?self
    {
        return static::getActiveDocument(self::TYPE_TERMS_OF_SERVICE);
    }

    /**
     * Get the latest privacy policy.
     */
    public static function getActivePrivacyPolicy(): ?self
    {
        return static::getActiveDocument(self::TYPE_PRIVACY_POLICY);
    }

    /**
     * Get the latest competition rules.
     */
    public static function getActiveCompetitionRules(): ?self
    {
        return static::getActiveDocument(self::TYPE_COMPETITION_RULES);
    }

    /**
     * Scope to get only active documents.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get documents by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
