<?php

namespace App\Models;

use App\Events;
use App\Observers;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;

/**
 * @mixin IdeHelperArticle
 */
#[ObservedBy([
    Observers\ArticleObserver::class,
    Observers\UserActivitiesObserver::class,
])]
class Article extends Model
{
    use HasFactory, HasUlids, Searchable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'year',
        'channel_id',
        'genre_id',
        'user_id',
        'track_number',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'created' => Events\ArticleCreated::class,
        'updated' => Events\ArticleUpdated::class,
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'user' => $this->user->name,
            'channel' => $this->channel->name,
        ];
    }

    /**
     * Interact with the article's first name.
     */
    protected function likesCount(): Attribute
    {
        return Attribute::make(
            get: fn (): ?int => $this->likes_count ?? null
        );
    }

    /**
     * Get the article's owner.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the article's genre.
     */
    public function genre(): BelongsTo
    {
        return $this->belongsTo(Genre::class);
    }

    /**
     * Get the article's channel.
     */
    public function channel(): BelongsTo
    {
        return $this->belongsTo(Channel::class);
    }

    /**
     * Get the article's image.
     */
    public function image(): MorphOne
    {
        return $this->morphOne(File\Image::class, 'fileable');
    }

    /**
     * Get the article's audio.
     */
    public function audio(): MorphOne
    {
        return $this->morphOne(File\Audio::class, 'fileable');
    }

    /**
     * Get the article's audio metadata.
     */
    public function audioMetadata(): HasOne
    {
        return $this->hasOne(ArticleAudioMetadata::class);
    }

    /**
     * Get the article's audio embedding.
     */
    public function audioEmbedding(): HasOne
    {
        return $this->hasOne(Embedding\ArticleAudioEmbedding::class);
    }

    /**
     * Get the article's metadata embedding.
     */
    public function metadataEmbedding(): HasOne
    {
        return $this->hasOne(Embedding\ArticleMetadataEmbedding::class);
    }

    /**
     * Get the channel's comments.
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Morph\Comment::class, 'commentable');
    }

    /**
     * Get the article's likes.
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Morph\Like::class, 'likeable');
    }

    /**
     * Get the article's downloads.
     */
    public function downloads(): MorphMany
    {
        return $this->morphMany(Morph\Download::class, 'downloadable');
    }

    /**
     * Get the article's featurings.
     */
    public function featurings(): HasMany
    {
        return $this->hasMany(Featuring::class);
    }

    /**
     * Get the article's featurings.
     */
    public function plays(): HasMany
    {
        return $this->hasMany(Play::class);
    }
}
