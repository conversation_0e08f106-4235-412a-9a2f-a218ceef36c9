<?php

namespace App\Filament;

use App\Models\File\Image;
use Illuminate\Support\Facades\Storage;

trait HasFileRelation
{
    /**
     * Fill missing fields for Filament resource with file relation manager
     *
     * @param  array<string, mixed>  $data
     * @return array<string, mixed>
     */
    public static function fillFileRequiredFields(array $data, string $disk): array
    {
        $storage = Storage::disk($disk);
        $filename_disk = $data['filename_disk'];

        $mimeType = $storage->mimeType($filename_disk);
        $width = null;
        $height = null;

        if (in_array($mimeType, Image::IMAGE_MIME_TYPES)) {
            $fileContent = $storage->get($filename_disk);
            $imageSize = getimagesizefromstring($fileContent);

            if ($imageSize !== false) {
                $width = $imageSize[0];
                $height = $imageSize[1];
            }
        }

        $filename = pathinfo($filename_disk, PATHINFO_FILENAME);

        return array_merge($data, [
            'title' => $filename,
            'storage' => $disk,
            'filename_download' => $filename_disk,
            'type' => $mimeType,
            'filesize' => $storage->size($filename_disk),
            'width' => $width,
            'height' => $height,
            'url' => $storage->url($filename_disk),
            'location' => $storage->path($filename_disk),
        ]);
    }
}
