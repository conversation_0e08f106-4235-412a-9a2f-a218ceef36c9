<?php

namespace App\Filament\Pages\Auth;

use App\Filament\Resources\UserResource;
use App\Models\File\Image;
use Filament\Forms\Components;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Pages\Auth\EditProfile as BaseEditProfile;

class EditProfile extends BaseEditProfile
{
    public function form(Form $form): Form
    {
        $userFormSchema = UserResource::formSchema(disableEmailField: true);

        return $form
            ->schema([
                Section::make('General')
                    ->model(auth()->user())
                    ->schema([
                        Components\Card::make()
                            ->schema([
                                $this->getNameFormComponent(),

                                $this->getEmailFormComponent()->disabled(),

                                Components\TextInput::make('phone')
                                    ->tel()
                                    ->telRegex('/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\.\/0-9]*$/')
                                    ->maxLength(13),

                                Components\FileUpload::make('image.filename_disk')
                                    ->hiddenOn('edit')
                                    ->label('Image')
                                    ->image()
                                    ->disk(name: Image::getDisk())
                                    ->directory(Image::FOLDER)
                                    ->visibility(Image::VISIBILITY)
                                    ->minSize(Image::MIN_SIZE)
                                    ->maxSize(Image::MAX_SIZE)
                                    ->acceptedFileTypes(Image::IMAGE_MIME_TYPES),

                                Components\TextInput::make('country_name')
                                    ->minLength(2)
                                    ->maxLength(255),

                                Components\TextInput::make('country_code')
                                    ->minLength(2)
                                    ->maxLength(255),

                                Components\TextInput::make('timezone')
                                    ->minLength(2)
                                    ->maxLength(255),

                                Components\TextInput::make('city')
                                    ->minLength(2)
                                    ->maxLength(255),

                                Components\TextInput::make('region')
                                    ->minLength(2)
                                    ->maxLength(255),

                                Components\Textarea::make('description')
                                    ->minLength(2)
                                    ->maxLength(65535),

                            ])
                            ->columnSpan([
                                'lg' => 2,
                            ])
                            ->columns([
                                'lg' => 1,
                            ]),

                        $userFormSchema[1],
                    ])->columns([
                        'sm' => 3,
                        'lg' => null,
                    ]),

                Section::make('Update Password')
                    ->columns(1)
                    ->schema([
                        $this->getPasswordFormComponent(),
                        $this->getPasswordConfirmationFormComponent(),
                    ]),

            ]);
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }
}
