<?php

namespace App\Filament\Widgets;

use App\Models\Article;
use App\Models\Channel;
use App\Models\Genre;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class StatsOverviewWidget extends BaseWidget
{
    protected static ?int $sort = 0;

    protected function getCards(): array
    {
        /**
         * @var User
         */
        $auth = auth()->user();

        $follows = [
            Card::make('Followers', number_format($auth->followers()->count())),

            Card::make('Followings', number_format($auth->followings()->count())),
        ];

        if ($auth->isOnlyArtist()) {
            return [
                Card::make('Channels', number_format($auth->channels()->count())),

                Card::make('Audios', number_format($auth->articles()->count())),

                Card::make('Monthly plays', number_format($auth->getMonthlyPlays())),

                ...$follows,
            ];
        }

        return [
            Card::make('Total Users', number_format(User::count())),

            Card::make('Total Channels', number_format(Channel::count())),

            Card::make('Total Audios', number_format(Article::count())),

            Card::make('Total Genres', number_format(Genre::count())),

            ...$follows,
        ];
    }
}
