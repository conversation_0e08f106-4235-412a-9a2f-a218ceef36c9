<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\UserResource;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class LatestUsers extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 2;

    public function getDefaultTableRecordsPerPageSelectOption(): int
    {
        return 5;
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'id';
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }

    protected function getTableQuery(): Builder
    {
        return UserResource::getEloquentQuery();
    }

    protected function getTableColumns(): array
    {
        return UserResource::tableColumns();
    }

    protected function getTableRecordsPerPageSelectOptions(): ?array
    {
        return [5, 10, 50, 100];
    }

    protected function getTableActions(): array
    {
        return [];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user->isAdmin();
    }
}
