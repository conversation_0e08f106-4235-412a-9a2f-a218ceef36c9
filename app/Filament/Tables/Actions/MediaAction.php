<?php

namespace App\Filament\Tables\Actions;

use Hugomyb\FilamentMediaAction\Tables\Actions\MediaAction as HugomybMediaAction;

class MediaAction extends HugomybMediaAction
{
    private ?string $customType = null;

    public ?bool $preloadAuto = false;

    public function setCustomMediaType(string $mime): static
    {
        $type = explode('/', strtolower($mime));

        $this->customType = $type[0];
        $this->mime = $mime;

        return $this;
    }

    protected function detectMediaType(): string
    {
        return $this->customType ?? parent::detectMediaType();
    }
}
