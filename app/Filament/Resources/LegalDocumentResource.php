<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LegalDocumentResource\Pages;
use App\Models\LegalDocument;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LegalDocumentResource extends Resource
{
    protected static ?string $model = LegalDocument::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Legal';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Legal Documents';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Document Details')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Document Type')
                            ->options(LegalDocument::getTypes())
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $titles = [
                                    'terms_of_service' => 'Terms of Service',
                                    'privacy_policy' => 'Privacy Policy',
                                    'competition_rules' => 'Competition Rules and Guidelines',
                                ];
                                $set('title', $titles[$state] ?? '');
                            }),
                        Forms\Components\TextInput::make('title')
                            ->label('Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('version')
                            ->label('Version')
                            ->numeric()
                            ->default(1.0)
                            ->step(0.1)
                            ->required()
                            ->helperText('Increment version when making significant changes'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(false)
                            ->helperText('Only one version per document type can be active'),
                        Forms\Components\DateTimePicker::make('effective_date')
                            ->label('Effective Date')
                            ->default(now())
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\MarkdownEditor::make('content')
                            ->label('Document Content')
                            ->required()
                            ->columnSpanFull()
                            ->helperText('Use Markdown formatting for better presentation'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'terms_of_service' => 'info',
                        'privacy_policy' => 'warning',
                        'competition_rules' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => LegalDocument::getTypes()[$state] ?? $state),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('version')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('effective_date')
                    ->label('Effective Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('type')
                    ->options(LegalDocument::getTypes()),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('activate')
                    ->label('Activate')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (LegalDocument $record): bool => ! $record->is_active)
                    ->requiresConfirmation()
                    ->modalHeading('Activate Document')
                    ->modalDescription('This will deactivate the current active version and activate this version.')
                    ->action(function (LegalDocument $record) {
                        // Deactivate other versions of the same type
                        LegalDocument::where('type', $record->type)
                            ->where('id', '!=', $record->id)
                            ->update(['is_active' => false]);

                        // Activate this version
                        $record->update(['is_active' => true]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('type')
            ->defaultSort('version', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLegalDocuments::route('/'),
            'create' => Pages\CreateLegalDocument::route('/create'),
            'view' => Pages\ViewLegalDocument::route('/{record}'),
            'edit' => Pages\EditLegalDocument::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
