<?php

namespace App\Filament\Resources\ChannelResource\Widgets;

use App\Models\Channel;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class ChannelStats extends BaseWidget
{
    protected function getCards(): array
    {
        return [
            Card::make('Total Channels', Channel::count()),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
