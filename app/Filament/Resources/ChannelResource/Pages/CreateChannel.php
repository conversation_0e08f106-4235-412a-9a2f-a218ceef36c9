<?php

namespace App\Filament\Resources\ChannelResource\Pages;

use App\Filament\HasFileRelation;
use App\Filament\Resources\ChannelResource;
use App\Models\File\Image;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateChannel extends CreateRecord
{
    use HasFileRelation;

    protected static string $resource = ChannelResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $record = static::getModel()::create($data);

        if ($data['image'] && $data['image']['filename_disk']) {
            $record->image()->create(
                $this->fillFileRequiredFields(
                    data: $data['image'],
                    disk: Image::getDisk()
                )
            );
        }

        return $record;
    }
}
