<?php

namespace App\Filament\Resources\ChannelResource\RelationManagers;

use App\Filament\HasFileRelation;
use App\Filament\Resources\ArticleResource;
use App\Filament\Tables\Actions\MediaAction;
use App\Models\Article;
use App\Models\Channel;
use App\Models\File;
use App\Repositories\ArticleRepository;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ArticlesRelationManager extends RelationManager
{
    protected static string $relationship = 'articles';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = 'Audios';

    protected static ?string $label = 'Audio';

    protected static ?Channel $owner = null;

    public function form(Form $form): Form
    {
        $schema = ArticleResource::form(
            form: $form,
            reusedFromChannelView: true,
            owner: $this->getOwnerRecord()
        );

        return $form
            ->schema($schema->getComponents());
    }

    public function table(Table $table): Table
    {
        $columns = ArticleResource::table(table: $table)
            ->modifyQueryUsing(fn (Builder $query) => $query->orderBy('track_number'))
            ->getColumns();

        array_unshift($columns, Tables\Columns\TextColumn::make('track'));

        $ownerModel = $this->getOwnerRecord();

        return $table
            ->columns($columns)
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->using(function (array $data, string $model) use ($ownerModel): Model {
                        $record = $ownerModel->articles()->create([
                            ...$data,
                            'user_id' => $ownerModel->user_id,
                        ]);

                        if ($data['image'] && $data['image']['filename_disk']) {
                            $record->image()->create(
                                HasFileRelation::fillFileRequiredFields(
                                    data: $data['image'],
                                    disk: File\Image::getDisk()
                                )
                            );
                        }

                        if ($data['audio'] && $data['audio']['filename_disk']) {
                            $record->audio()->create(
                                HasFileRelation::fillFileRequiredFields(
                                    data: $data['audio'],
                                    disk: File\Audio::getDisk()
                                )
                            );
                        }

                        return $record;
                    }),
            ])
            ->modifyQueryUsing(
                fn (Builder $query) => $query->with(ArticleRepository::DEFAULT_RELATIONS)
            )
            ->actions([
                MediaAction::make('Audio')
                    ->iconButton()
                    ->setCustomMediaType('audio/mpeg')
                    ->media(fn (Article $record) => $record->audio?->url)
                    ->disabled(fn (Article $record) => ! $record->audio)
                    ->icon('heroicon-o-play'),

                Tables\Actions\EditAction::make('edit')
                    ->url(function (Tables\Actions\EditAction $action) {
                        $record = $action->getRecord();

                        return ArticleResource::getUrl('edit', ['record' => $record->id]);
                    }),

                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): ?Builder
    {
        if (parent::getTableQuery()) {
            return parent::getTableQuery()
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }

        return null;
    }
}
