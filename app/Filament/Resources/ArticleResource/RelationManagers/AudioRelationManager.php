<?php

namespace App\Filament\Resources\ArticleResource\RelationManagers;

use App\Filament\HasFileRelation;
use App\Filament\Tables\Actions\MediaAction;
use App\Models\File\Audio;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AudioRelationManager extends RelationManager
{
    use HasFileRelation;

    protected static string $relationship = 'audio';

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?string $title = 'Audio File';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('filename_disk')
                    ->disk(Audio::getDisk())
                    ->minSize(Audio::MIN_SIZE)
                    ->directory(Audio::FOLDER)
                    ->maxSize(Audio::MAX_SIZE)
                    ->required()
                    ->acceptedFileTypes(Audio::AUDIO_MIME_TYPES),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id'),

                Tables\Columns\TextColumn::make('duration')
                    ->state(function (Audio $record) {
                        return gmdate('H:i:s', (int) $record->duration ?? 0);
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->sortable()
                    ->since(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(
                        fn (array $data) => self::fillFileRequiredFields(
                            data: $data,
                            disk: Audio::getDisk()
                        )
                    ),
            ])
            ->actions([
                MediaAction::make('Audio')
                    ->iconButton()
                    ->setCustomMediaType('audio/mpeg')
                    ->media(fn (Audio $record) => $record->url)
                    ->icon('heroicon-o-play'),

                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): ?Builder
    {
        if (parent::getTableQuery()) {
            return parent::getTableQuery()
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }

        return null;
    }
}
