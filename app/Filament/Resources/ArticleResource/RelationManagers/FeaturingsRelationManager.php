<?php

namespace App\Filament\Resources\ArticleResource\RelationManagers;

use App\Enums\UserRole;
use App\Models\Article;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FeaturingsRelationManager extends RelationManager
{
    protected static string $relationship = 'featurings';

    protected static ?string $recordTitleAttribute = 'user.name';

    protected static ?Article $owner = null;

    public function booted()
    {
        self::$owner = $this->getOwnerRecord();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Artist')
                    ->relationship(
                        'user',
                        'name',
                        function (Builder $query) {
                            $query->whereNot('id', self::$owner->user_id)
                                ->where(function (Builder $query) {
                                    $query->where('role', UserRole::ADMIN->value)
                                        ->orWhere('role', UserRole::SINGER->value)
                                        ->orWhere('role', UserRole::PODCASTER->value);
                                });
                        }
                    )
                    ->lazy()
                    ->searchable()
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name'),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }
}
