<?php

namespace App\Filament\Resources\ArticleResource\Widgets;

use App\Models\Article;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class ArticleStats extends BaseWidget
{
    protected function getCards(): array
    {
        return [
            Card::make('Total Audios', Article::count()),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
