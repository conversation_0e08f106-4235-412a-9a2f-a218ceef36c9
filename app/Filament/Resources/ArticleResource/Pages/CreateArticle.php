<?php

namespace App\Filament\Resources\ArticleResource\Pages;

use App\Filament\HasFileRelation;
use App\Filament\Resources\ArticleResource;
use App\Models\Channel;
use App\Models\File;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateArticle extends CreateRecord
{
    use HasFileRelation;

    protected static string $resource = ArticleResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $channel = Channel::findOrFail($data['channel_id']);

        $data['user_id'] = $channel->user_id;

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $record = static::getModel()::create($data);

        if ($data['image'] && $data['image']['filename_disk']) {
            $record->image()->create(
                $this->fillFileRequiredFields(
                    data: $data['image'],
                    disk: File\Image::getDisk()
                )
            );
        }

        if ($data['audio'] && $data['audio']['filename_disk']) {
            $record->audio()->create(
                $this->fillFileRequiredFields(
                    data: $data['audio'],
                    disk: File\Audio::getDisk()
                )
            );
        }

        return $record;
    }
}
