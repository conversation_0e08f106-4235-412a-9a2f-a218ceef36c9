<?php

namespace App\Filament\Resources\VoteTransactionResource\Pages;

use App\Filament\Resources\VoteTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewVoteTransaction extends ViewRecord
{
    protected static string $resource = VoteTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }
}
