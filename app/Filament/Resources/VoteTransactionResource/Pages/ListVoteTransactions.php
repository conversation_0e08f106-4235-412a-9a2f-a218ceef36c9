<?php

namespace App\Filament\Resources\VoteTransactionResource\Pages;

use App\Filament\Resources\VoteTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVoteTransactions extends ListRecords
{
    protected static string $resource = VoteTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
