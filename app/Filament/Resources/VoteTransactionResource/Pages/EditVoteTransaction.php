<?php

namespace App\Filament\Resources\VoteTransactionResource\Pages;

use App\Filament\Resources\VoteTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditVoteTransaction extends EditRecord
{
    protected static string $resource = VoteTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
