<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArtistRequestResource\Pages;
use App\Models\ArtistRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ArtistRequestResource extends Resource
{
    protected static ?string $model = ArtistRequest::class;

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationGroup = 'Users';

    protected static ?string $navigationIcon = 'heroicon-o-cube-transparent';

    protected static ?string $recordTitleAttribute = 'user.name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(36),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->maxLength(65535),
                Forms\Components\TextInput::make('youtube_link')
                    ->label('YouTube Link')
                    ->url()
                    ->maxLength(255),
                Forms\Components\KeyValue::make('social_links')
                    ->label('Social Media Links')
                    ->keyLabel('Platform')
                    ->valueLabel('URL'),
                Forms\Components\TextInput::make('artist_type')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('approved')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('artist_type')
                    ->sortable(),
                Tables\Columns\TextColumn::make('youtube_link')
                    ->label('YouTube Link')
                    ->limit(30)
                    ->url(fn ($record) => $record->youtube_link)
                    ->openUrlInNewTab(),
                Tables\Columns\IconColumn::make('approved')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('Approve')
                    ->action(function (Tables\Actions\Action $action) {
                        $action->getRecord()?->fill(['approved' => true])->save();

                        Notification::make()
                            ->title('Artist request approved.')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArtistRequests::route('/'),
            'view' => Pages\ViewArtistRequest::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return self::$model::where('approved', false)->count();
    }
}
