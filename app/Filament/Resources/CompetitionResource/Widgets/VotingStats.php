<?php

namespace App\Filament\Resources\CompetitionResource\Widgets;

use App\Models\Competition;
use App\Models\Vote;
use App\Models\VoteTransaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;

class VotingStats extends BaseWidget
{
    public ?Model $record = null;

    protected function getStats(): array
    {
        if (! $this->record instanceof Competition) {
            return [];
        }

        $competition = $this->record;

        // Get total votes count (sum of vote_count)
        $totalVotes = Vote::where('competition_id', $competition->id)
            ->sum('vote_count');

        // Get total unique voters
        $uniqueVoters = Vote::where('competition_id', $competition->id)
            ->distinct('user_id')
            ->count('user_id');

        // Get total revenue from votes
        $totalRevenue = VoteTransaction::whereHas('vote', function ($query) use ($competition) {
            $query->where('competition_id', $competition->id);
        })
            ->where('status', 'completed')
            ->sum('amount');

        // Get pending transactions
        $pendingTransactions = VoteTransaction::whereHas('vote', function ($query) use ($competition) {
            $query->where('competition_id', $competition->id);
        })
            ->where('status', 'pending')
            ->count();

        // Get average votes per voter
        $avgVotesPerVoter = $uniqueVoters > 0 ? round($totalVotes / $uniqueVoters, 1) : 0;

        return [
            Stat::make('Total Votes', number_format($totalVotes))
                ->description('Sum of all votes cast')
                ->descriptionIcon('heroicon-m-hand-thumb-up')
                ->color('success'),

            Stat::make('Unique Voters', number_format($uniqueVoters))
                ->description('Number of people who voted')
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),

            Stat::make('Total Revenue', '$'.number_format($totalRevenue, 2))
                ->description('From completed transactions')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('Pending Payments', number_format($pendingTransactions))
                ->description('Transactions awaiting completion')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingTransactions > 0 ? 'warning' : 'success'),

            Stat::make('Avg Votes/Voter', $avgVotesPerVoter)
                ->description('Average votes per person')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('info'),

            Stat::make('Vote Price', '$'.number_format($competition->vote_price, 2))
                ->description('Price per vote')
                ->descriptionIcon('heroicon-m-tag')
                ->color('gray'),
        ];
    }
}
