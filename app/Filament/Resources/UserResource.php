<?php

namespace App\Filament\Resources;

use App\Enums\UserRole;
use App\Filament\Resources\RelationManagers\ImageRelationManager;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Filament\Resources\UserResource\Widgets;
use App\Models\File\Image;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $modelLabel = 'User';

    protected static ?string $pluralModelLabel = 'Users';

    protected static ?string $navigationGroup = 'Users';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'name';

    public static function formSchema(?array $userRoles = null, bool $disableEmailField = false): array
    {
        $email = Forms\Components\TextInput::make('email')
            ->email()
            ->required()
            ->minLength(2)
            ->maxLength(255);

        return [
            Forms\Components\Group::make()
                ->schema([
                    Forms\Components\Card::make()
                        ->columns(['lg' => 2])
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->required()
                                ->minLength(2)
                                ->maxLength(255),

                            $disableEmailField ? $email->disabled(true) : $email
                                ->disabledOn('edit'),

                            $userRoles ?
                            Forms\Components\Select::make('role')
                                ->options($userRoles)
                                ->required()
                                ->disabled(function (?User $record) {
                                    // Disable role field for first admin when they're editing themselves
                                    if (! $record) {
                                        return false;
                                    } // Allow on create

                                    $currentUser = auth()->user();

                                    return $record->isFirstAdmin() && $currentUser->id === $record->id;
                                })
                                ->helperText(function (?User $record) {
                                    if (! $record) {
                                        return null;
                                    }

                                    $currentUser = auth()->user();
                                    if ($record->isFirstAdmin() && $currentUser->id === $record->id) {
                                        return 'As the first admin, you cannot change your role to ensure system stability.';
                                    }

                                    return null;
                                }) : Forms\Components\Hidden::make(''),

                            Forms\Components\TextInput::make('password')
                                ->password()
                                ->required()
                                ->default(Str::random(10))
                                ->visibleOn('create')
                                ->maxLength(255),

                            Forms\Components\TextInput::make('phone')
                                ->tel()
                                ->telRegex('/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\.\/0-9]*$/')
                                ->columnSpan(['lg' => 2])
                                ->maxLength(13),

                            Forms\Components\FileUpload::make('image.filename_disk')
                                ->hiddenOn('edit')
                                ->label('Image')
                                ->image()
                                ->disk(Image::getDisk())
                                ->visibility(Image::VISIBILITY)
                                ->directory(Image::FOLDER)
                                ->minSize(Image::MIN_SIZE)
                                ->maxSize(Image::MAX_SIZE)
                                ->columnSpan(['lg' => 2])
                                ->acceptedFileTypes(Image::IMAGE_MIME_TYPES),

                            Forms\Components\TextInput::make('country_name')
                                ->minLength(2)
                                ->maxLength(255),

                            Forms\Components\TextInput::make('country_code')
                                ->minLength(2)
                                ->maxLength(255),

                            Forms\Components\TextInput::make('timezone')
                                ->minLength(2)
                                ->maxLength(255),

                            Forms\Components\TextInput::make('city')
                                ->minLength(2)
                                ->maxLength(255),

                            Forms\Components\TextInput::make('region')
                                ->minLength(2)
                                ->maxLength(255)
                                ->columnSpan(['lg' => 2]),

                            Forms\Components\Textarea::make('description')
                                ->minLength(2)
                                ->maxLength(65535)
                                ->columnSpan(['lg' => 2]),

                        ]),

                    Forms\Components\Section::make('User Status')
                        ->visibleOn('edit')
                        ->columns(['lg' => 2])
                        ->schema([
                            Forms\Components\Toggle::make('verified')
                                ->label('Verified')
                                ->helperText('User (Artist) is verified')
                                ->inline()
                                ->default(false),
                        ]),
                ])
                ->columnSpan([
                    'lg' => fn (?User $record) => $record === null ? 3 : 2,
                ]),

            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Placeholder::make('user_type')
                        ->content(fn (User $record): ?string => $record->role->value),

                    Forms\Components\Placeholder::make('email_verified_at')
                        ->content(fn (User $record): ?string => $record->email_verified_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (User $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (User $record): ?string => $record->updated_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('channels')
                        ->visible(fn (User $record) => $record->isArtist())
                        ->content(
                            fn (User $record) => number_format($record->channels()->count())
                        ),

                    Forms\Components\Placeholder::make('audios')
                        ->visible(fn (User $record) => $record->isArtist())
                        ->content(
                            fn (User $record) => number_format($record->articles()->count())
                        ),

                    Forms\Components\Placeholder::make('monthly_plays')
                        ->label('Monthly plays')
                        ->visible(fn (User $record) => $record->isArtist())
                        ->content(
                            fn (User $record) => number_format($record->getMonthlyPlays())
                        ),

                    Forms\Components\Placeholder::make('followers')
                        ->visible(fn (User $record) => $record->isArtist())
                        ->content(
                            fn (User $record) => number_format($record->followers()->count())
                        ),

                    Forms\Components\Placeholder::make('followings')
                        ->content(
                            fn (User $record) => number_format($record->followings()->count())
                        ),

                    Forms\Components\Placeholder::make('playings')
                        ->content(
                            fn (User $record) => number_format($record->playings()->count())
                        ),

                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?User $record) => $record === null),

        ];
    }

    public static function form(Form $form): Form
    {
        $userRoles = collect(UserRole::cases())
            ->keyBy(fn ($role) => $role->value)
            ->map(fn ($role) => ucfirst($role->value))
            ->toArray();

        return $form
            ->schema(self::formSchema($userRoles))
            ->columns([
                'sm' => 3,
                'lg' => null,
            ]);
    }

    public static function tableColumns(): array
    {
        return [
            Tables\Columns\ImageColumn::make('image.url')
                ->circular(),

            Tables\Columns\TextColumn::make('name')
                ->sortable()
                ->searchable(),

            Tables\Columns\TextColumn::make('email')
                ->sortable()
                ->searchable(),

            BadgeColumn::make('role')
                ->colors([
                    'success' => UserRole::ADMIN->value,
                    'primary' => UserRole::SINGER->value,
                    'warning' => UserRole::PODCASTER->value,
                    'secondary' => UserRole::GUEST->value,
                ])
                ->sortable(),

            Tables\Columns\TextColumn::make('phone')
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('country_name')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('country_code')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('timezone')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('city')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('region')
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),

            Tables\Columns\TextColumn::make('created_at')
                ->sortable()
                ->since(),
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::tableColumns())
            ->actions([
                Tables\Actions\LinkAction::make()
                    ->name('impersonate')
                    ->label('Impersonate')
                    ->hidden((function (User $record) {
                        $auth = auth()->user();

                        return ! $auth->canImpersonate() ||
                            ! $record->canBeImpersonated() ||
                            $auth->id === $record->id;
                    }))
                    ->url(function (User $record) {
                        return route('impersonate', $record);
                    }),
                Tables\Actions\EditAction::make()
                    ->hidden(function (User $record) {
                        $currentUser = auth()->user();

                        // Hide edit action if the record is the first admin and current user is not the first admin
                        return $record->isFirstAdmin() && $currentUser->id !== $record->id;
                    }),
                Tables\Actions\DeleteAction::make()
                    ->hidden(function (User $record) {
                        $currentUser = auth()->user();

                        return $record->isFirstAdmin() || $currentUser->id === $record->id;
                    }),
                Tables\Actions\RestoreAction::make(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (Collection $records) {
                            // Filter out the first admin user from bulk deletion
                            $records->reject(fn (User $record) => $record->isFirstAdmin())
                                ->each(fn (User $record) => $record->delete());
                        }),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            ImageRelationManager::class,

            RelationManagers\ChannelsRelationManager::class,
            RelationManagers\ArticlesRelationManager::class,

            RelationManagers\FollowersRelationManager::class,
            RelationManagers\FollowingsRelationManager::class,

            RelationManagers\CommentsRelationManager::class,

            RelationManagers\VotesGivenRelationManager::class,
            RelationManagers\VotesReceivedRelationManager::class,
            RelationManagers\SubscriptionsRelationManager::class,

        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\UserStats::class,
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'email'];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->globalSearchKeyBindings(['command+k', 'ctrl+k']);
    }
}
