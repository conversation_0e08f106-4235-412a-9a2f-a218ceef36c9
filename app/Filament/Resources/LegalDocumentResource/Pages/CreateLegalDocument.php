<?php

namespace App\Filament\Resources\LegalDocumentResource\Pages;

use App\Filament\Resources\LegalDocumentResource;
use App\Models\LegalDocument;
use Filament\Resources\Pages\CreateRecord;

class CreateLegalDocument extends CreateRecord
{
    protected static string $resource = LegalDocumentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If this is being set as active, deactivate other versions of the same type
        if ($data['is_active'] ?? false) {
            LegalDocument::where('type', $data['type'])
                ->update(['is_active' => false]);
        }

        return $data;
    }
}
