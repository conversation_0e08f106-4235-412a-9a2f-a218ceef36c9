<?php

namespace App\Filament\Resources\CommentResource\Widgets;

use App\Models\Morph\Comment;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class CommentStats extends BaseWidget
{
    protected function getCards(): array
    {
        $count = Comment::withTrashed()->count();

        if (! auth()->user()?->isAdmin()) {
            $count = Comment::where('user_id', auth()->id())
                ->count();
        }

        return [
            Card::make('Total Comments', $count)
                ->description('Total number of comments')
                ->descriptionIcon('heroicon-o-chat-bubble-left-right')
                ->color('primary')
                ->icon('heroicon-o-chat-bubble-left-right'),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
