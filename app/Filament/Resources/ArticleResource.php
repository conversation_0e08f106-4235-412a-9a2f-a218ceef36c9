<?php

namespace App\Filament\Resources;

use App\Filament\HasFileRelation;
use App\Filament\Resources\ArticleResource\Pages;
use App\Filament\Resources\ArticleResource\RelationManagers;
use App\Filament\Resources\ArticleResource\Widgets\ArticleStats;
use App\Filament\Resources\RelationManagers\CommentsRelationManager;
use App\Filament\Resources\RelationManagers\ImageRelationManager;
use App\Filament\Tables\Actions\MediaAction;
use App\Models\Article;
use App\Models\File;
use App\Models\User;
use App\Repositories\ArticleRepository;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ArticleResource extends Resource
{
    use HasFileRelation;

    protected static ?int $navigationSort = 7;

    protected static ?string $modelLabel = 'Audio';

    protected static ?string $pluralModelLabel = 'Audios';

    protected static ?string $model = Article::class;

    protected static ?string $navigationIcon = 'heroicon-o-play';

    protected static ?string $navigationGroup = 'Libraries';

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form, ?bool $reusedFromChannelView = false, ?\App\Models\Channel $owner = null): Form
    {
        $auth = self::auth();

        $fields = [
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),

            Forms\Components\Textarea::make('description')
                ->maxLength(65535),

            Forms\Components\FileUpload::make('image.filename_disk')
                ->hiddenOn('edit')
                ->label('Image')
                ->image()
                ->disk(File\Image::getDisk())
                ->directory(File\Image::FOLDER)
                ->minSize(File\Image::MIN_SIZE)
                ->maxSize(File\Image::MAX_SIZE)
                ->acceptedFileTypes(File\Image::IMAGE_MIME_TYPES),

            Forms\Components\Select::make('genre_id')
                ->label('Genre')
                ->relationship('genre', 'name')
                ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name} / {$record->category}")
                ->default($owner?->genre_id ?? null)
                ->preload()
                ->searchable(),

            $reusedFromChannelView ?
            Forms\Components\Hidden::make('') :
            Forms\Components\Select::make('channel_id')
                ->relationship('channel', 'name', function (Builder $query) use ($auth) {
                    if ($auth->isOnlyArtist()) {
                        $query->where('user_id', $auth->id);
                    }

                    return $query;
                })
                ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name} / {$record->user->email}")
                ->required()
                ->lazy()
                ->preload($auth->isOnlyArtist() ? true : false)
                ->searchable(),

            FileUpload::make('audio.filename_disk')
                ->label('Audio File')
                ->hiddenOn('edit')
                ->required()
                ->disk(File\Audio::getDisk())
                ->directory(File\Audio::FOLDER)
                ->minSize(File\Audio::MIN_SIZE)
                ->maxSize(File\Audio::MAX_SIZE)
                ->acceptedFileTypes(File\Audio::AUDIO_MIME_TYPES),

            Forms\Components\Select::make('user_id')
                ->label('Artist')
                ->hiddenOn('create')
                ->disabled()
                ->relationship('user', 'email')
                ->default(
                    $owner?->user_id ??
                    ($auth->isOnlyArtist() ? $auth->id : null)
                )
                ->required(),

            Forms\Components\DatePicker::make('year')
                ->format('Y')
                ->native(false)
                ->default(now())
                ->displayFormat('Y')
                ->required(),

            Forms\Components\TextInput::make('track_number')
                ->numeric()
                ->default($owner?->articles()->count() ?? 1)
                ->minValue(1)
                ->maxValue(6500),
        ];

        return $reusedFromChannelView ?
            $form->schema($fields) :
            $form->schema([
                Forms\Components\Card::make()
                    ->schema($fields)
                    ->columnSpan(['lg' => fn (?Article $record) => $record === null ? 3 : 2]),

                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Placeholder::make('id')
                            ->content(fn (Article $record): string => $record->id),

                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Article $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Article $record): ?string => $record->updated_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('likes')
                            ->content(
                                fn (Article $record) => number_format($record->likes()->count())
                            ),

                        Forms\Components\Placeholder::make('downloads')
                            ->content(
                                fn (Article $record) => number_format($record->downloads()->count())
                            ),

                        Forms\Components\Placeholder::make('comments')
                            ->content(
                                fn (Article $record) => number_format($record->comments()->count())
                            ),

                        Forms\Components\Placeholder::make('plays')
                            ->content(
                                fn (Article $record) => number_format($record->plays()->count())
                            ),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Article $record) => $record === null),

            ])->columns([
                'sm' => 3,
                'lg' => null,
            ]);
    }

    public static function table(Table $table, bool $showAdditionalColumns = false): Table
    {
        $additionalColumns = $showAdditionalColumns ? [
            Tables\Columns\TextColumn::make('likes_count')
                ->label('Likes')
                ->counts('likes'),

            Tables\Columns\TextColumn::make('downloads_count')
                ->label('Downloads')
                ->counts('downloads'),
        ] : [];

        return $table
            ->columns([
                // Tables\Columns\ImageColumn::make('image.url')
                //     ->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('genre.name'),

                Tables\Columns\TextColumn::make('channel.name'),

                Tables\Columns\TextColumn::make('user.name')->label('Artist'),

                ...$additionalColumns,

                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable(),
            ])
            ->modifyQueryUsing(
                fn (Builder $query) => $query->with(ArticleRepository::DEFAULT_RELATIONS)
            )
            ->actions([
                MediaAction::make('Audio')
                    ->iconButton()
                    ->setCustomMediaType('audio/mpeg')
                    ->media(fn (Article $record) => $record->audio?->url)
                    ->disabled(fn (Article $record) => ! $record->audio)
                    ->icon('heroicon-o-play'),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100])
            ->poll('10s');
    }

    public static function getRelations(): array
    {
        return [
            ImageRelationManager::class,
            RelationManagers\AudioRelationManager::class,
            RelationManagers\FeaturingsRelationManager::class,
            CommentsRelationManager::class,
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }

    public static function getWidgets(): array
    {
        return [
            ArticleStats::class,
        ];
    }

    public static function auth(): User
    {
        /**
         * @var User
         */
        $user = auth()->user();

        return $user;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticles::route('/'),
            'create' => Pages\CreateArticle::route('/create'),
            'edit' => Pages\EditArticle::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);

        $auth = self::auth();

        if ($auth->isOnlyArtist()) {
            $query->where('user_id', $auth->id);
        }

        return $query;
    }
}
