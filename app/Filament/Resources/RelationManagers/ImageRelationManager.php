<?php

namespace App\Filament\Resources\RelationManagers;

use App\Filament\HasFileRelation;
use App\Models\File\Image;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ImageRelationManager extends RelationManager
{
    use HasFileRelation;

    protected static string $relationship = 'image';

    protected static ?string $recordTitleAttribute = 'id';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('filename_disk')
                    ->image()
                    ->disk(Image::getDisk())
                    ->minSize(Image::MIN_SIZE)
                    ->maxSize(Image::MAX_SIZE)
                    ->directory(Image::FOLDER)
                    ->visibility(Image::VISIBILITY)
                    ->required()
                    ->acceptedFileTypes(Image::IMAGE_MIME_TYPES),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('url')
                    ->label('Image')
                    ->circular(),

                Tables\Columns\TextColumn::make('id'),

                Tables\Columns\TextColumn::make('created_at')
                    ->sortable()
                    ->since(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(
                        fn (array $data) => self::fillFileRequiredFields(
                            data: $data,
                            disk: Image::getDisk()
                        )
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): ?Builder
    {
        if (parent::getTableQuery()) {
            return parent::getTableQuery()
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }

        return null;
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
