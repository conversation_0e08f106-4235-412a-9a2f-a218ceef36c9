<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\Resources\ChannelResource;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChannelsRelationManager extends RelationManager
{
    protected static string $relationship = 'channels';

    protected static ?string $recordTitleAttribute = 'name';

    public function table(Table $table): Table
    {
        return $table
            ->columns(ChannelResource::table($table)->getColumns())
            ->actions([
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),

                Tables\Actions\ViewAction::make()
                    ->url(function (Tables\Actions\ViewAction $action) {
                        $record = $action->getRecord();

                        return ChannelResource::getUrl('edit', ['record' => $record->id]);
                    }),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): ?Builder
    {
        if (parent::getTableQuery()) {
            return parent::getTableQuery()
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }

        return null;
    }

    public static function canViewForRecord(Model $user, string $pageClass): bool
    {
        return $user->isArtist();
    }
}
