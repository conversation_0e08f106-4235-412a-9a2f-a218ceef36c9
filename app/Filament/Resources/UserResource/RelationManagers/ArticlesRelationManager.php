<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\Resources\ArticleResource;
use App\Filament\Tables\Actions\MediaAction;
use App\Models\Article;
use App\Repositories\ArticleRepository;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ArticlesRelationManager extends RelationManager
{
    protected static string $relationship = 'articles';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = 'Audios';

    public function table(Table $table): Table
    {
        return $table
            ->columns(ArticleResource::table($table)->getColumns())
            ->modifyQueryUsing(
                fn (Builder $query) => $query->with(ArticleRepository::DEFAULT_RELATIONS)
            )
            ->actions([
                MediaAction::make('Audio')
                    ->iconButton()
                    ->setCustomMediaType('audio/mpeg')
                    ->media(fn (Article $record) => $record->audio?->url)
                    ->disabled(fn (Article $record) => ! $record->audio)
                    ->icon('heroicon-o-play'),

                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),

                Tables\Actions\ViewAction::make()
                    ->url(function (Tables\Actions\ViewAction $action) {
                        $record = $action->getRecord();

                        return ArticleResource::getUrl('edit', ['record' => $record->id]);
                    }),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): ?Builder
    {
        if (parent::getTableQuery()) {
            return parent::getTableQuery()
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }

        return null;
    }

    public static function canViewForRecord(Model $user, string $pageClass): bool
    {
        return $user->isArtist();
    }
}
