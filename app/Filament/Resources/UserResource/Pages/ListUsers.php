<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Enums\UserRole;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\UserResource\Widgets\UserStats;
use Filament\Pages\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            UserStats::class,
        ];
    }

    public function getTabs(): array
    {
        return [
            null => Tab::make('All'),

            'admins' => Tab::make()->query(fn ($query) => $query->where('role', UserRole::ADMIN->value)),

            'singers' => Tab::make()->query(fn ($query) => $query->where('role', UserRole::SINGER->value)),

            'podcasters' => Tab::make()->query(fn ($query) => $query->where('role', UserRole::PODCASTER->value)),

            'guests' => Tab::make()->query(fn ($query) => $query->where('role', UserRole::GUEST->value)),
        ];
    }
}
