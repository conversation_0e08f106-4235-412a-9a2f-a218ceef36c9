<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Facades\Filament;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    public function mount(int|string $record): void
    {
        parent::mount($record);

        // Prevent other admins from accessing the first admin's edit page
        $currentUser = auth()->user();
        if ($this->record->isFirstAdmin() && $currentUser->id !== $this->record->id) {
            Filament::notify('danger', 'You are not authorized to edit this user.');
            $this->redirect(static::getResource()::getUrl('index'));
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->hidden(fn () => $this->record->isFirstAdmin()),
            Actions\RestoreAction::make(),
        ];
    }
}
