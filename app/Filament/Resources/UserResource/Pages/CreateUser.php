<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\HasFileRelation;
use App\Filament\Resources\UserResource;
use App\Models\File\Image;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class CreateUser extends CreateRecord
{
    use HasFileRelation;

    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['password'] = Hash::make($data['password']);

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $record = static::getModel()::create($data);

        if ($data['image'] && $data['image']['filename_disk']) {
            $record->image()->create(
                $this->fillFileRequiredFields(
                    data: $data['image'],
                    disk: Image::getDisk()
                )
            );
        }

        return $record;
    }
}
