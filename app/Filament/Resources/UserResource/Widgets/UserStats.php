<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class UserStats extends BaseWidget
{
    protected function getCards(): array
    {
        return [
            Card::make('Total Users', User::count()),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
