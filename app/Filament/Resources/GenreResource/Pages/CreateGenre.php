<?php

namespace App\Filament\Resources\GenreResource\Pages;

use App\Filament\HasFileRelation;
use App\Filament\Resources\GenreResource;
use App\Models\File\Image;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateGenre extends CreateRecord
{
    use HasFileRelation;

    protected static string $resource = GenreResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $record = static::getModel()::create($data);

        if ($data['image'] && $data['image']['filename_disk']) {
            $record->image()->create(
                $this->fillFileRequiredFields(
                    data: $data['image'],
                    disk: Image::getDisk()
                )
            );
        }

        return $record;
    }
}
