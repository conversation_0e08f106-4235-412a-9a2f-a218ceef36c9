<?php

namespace App\Filament\Resources\GenreResource\Widgets;

use App\Models\Genre;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class GenreStats extends BaseWidget
{
    protected function getCards(): array
    {
        return [
            Card::make('Total Genres', Genre::count()),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
