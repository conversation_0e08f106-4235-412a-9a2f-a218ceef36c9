<?php

namespace App\Filament\Resources\UserAgreementResource\Pages;

use App\Filament\Resources\UserAgreementResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUserAgreement extends CreateRecord
{
    protected static string $resource = UserAgreementResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the IP address if not provided
        if (empty($data['ip_address'])) {
            $data['ip_address'] = request()->ip();
        }

        // Set the user agent if not provided
        if (empty($data['user_agent'])) {
            $data['user_agent'] = request()->userAgent();
        }

        return $data;
    }
}
