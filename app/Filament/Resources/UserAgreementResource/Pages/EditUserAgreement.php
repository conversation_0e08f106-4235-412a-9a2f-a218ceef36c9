<?php

namespace App\Filament\Resources\UserAgreementResource\Pages;

use App\Filament\Resources\UserAgreementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditUserAgreement extends EditRecord
{
    protected static string $resource = UserAgreementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
