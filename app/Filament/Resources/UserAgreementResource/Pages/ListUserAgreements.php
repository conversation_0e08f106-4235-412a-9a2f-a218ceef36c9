<?php

namespace App\Filament\Resources\UserAgreementResource\Pages;

use App\Filament\Resources\UserAgreementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserAgreements extends ListRecords
{
    protected static string $resource = UserAgreementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
