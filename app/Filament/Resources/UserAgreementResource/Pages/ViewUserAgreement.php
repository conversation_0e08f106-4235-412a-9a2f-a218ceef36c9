<?php

namespace App\Filament\Resources\UserAgreementResource\Pages;

use App\Filament\Resources\UserAgreementResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewUserAgreement extends ViewRecord
{
    protected static string $resource = UserAgreementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }
}
