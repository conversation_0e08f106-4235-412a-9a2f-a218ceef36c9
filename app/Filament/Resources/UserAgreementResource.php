<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserAgreementResource\Pages;
use App\Models\UserAgreement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class UserAgreementResource extends Resource
{
    protected static ?string $model = UserAgreement::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationGroup = 'Legal';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'User Agreements';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Agreement Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('User')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->required(),
                        Forms\Components\Select::make('legal_document_id')
                            ->label('Legal Document')
                            ->relationship('legalDocument', 'title')
                            ->searchable()
                            ->required(),
                        Forms\Components\Select::make('competition_id')
                            ->label('Competition')
                            ->relationship('competition', 'name')
                            ->searchable()
                            ->nullable()
                            ->helperText('Optional: If this agreement is for a specific competition'),
                        Forms\Components\DateTimePicker::make('agreed_at')
                            ->label('Agreed At')
                            ->default(now())
                            ->required(),
                        Forms\Components\TextInput::make('ip_address')
                            ->label('IP Address')
                            ->maxLength(45)
                            ->helperText('IP address where the agreement was made'),
                        Forms\Components\Textarea::make('user_agent')
                            ->label('User Agent')
                            ->maxLength(500)
                            ->helperText('Browser/device information'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('legalDocument.title')
                    ->label('Document')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('legalDocument.type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'terms_of_service' => 'info',
                        'privacy_policy' => 'warning',
                        'competition_rules' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('legalDocument.version')
                    ->label('Version')
                    ->sortable(),
                Tables\Columns\TextColumn::make('competition.name')
                    ->label('Competition')
                    ->searchable()
                    ->sortable()
                    ->placeholder('General Agreement'),
                Tables\Columns\TextColumn::make('agreed_at')
                    ->label('Agreed At')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('legalDocument.type')
                    ->label('Document Type')
                    ->options([
                        'terms_of_service' => 'Terms of Service',
                        'privacy_policy' => 'Privacy Policy',
                        'competition_rules' => 'Competition Rules',
                    ]),
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable(),
                Tables\Filters\SelectFilter::make('competition')
                    ->relationship('competition', 'name')
                    ->searchable(),
                Tables\Filters\Filter::make('agreed_at')
                    ->form([
                        Forms\Components\DatePicker::make('agreed_from')
                            ->label('Agreed From'),
                        Forms\Components\DatePicker::make('agreed_until')
                            ->label('Agreed Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['agreed_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('agreed_at', '>=', $date),
                            )
                            ->when(
                                $data['agreed_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('agreed_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('agreed_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserAgreements::route('/'),
            'create' => Pages\CreateUserAgreement::route('/create'),
            'view' => Pages\ViewUserAgreement::route('/{record}'),
            'edit' => Pages\EditUserAgreement::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereDate('agreed_at', today())->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }

    public static function getNavigationBadgeTooltip(): ?string
    {
        return 'Agreements made today';
    }
}
