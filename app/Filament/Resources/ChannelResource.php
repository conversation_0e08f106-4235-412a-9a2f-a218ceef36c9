<?php

namespace App\Filament\Resources;

use App\Enums\ChannelType;
use App\Filament\Resources\ChannelResource\Pages;
use App\Filament\Resources\ChannelResource\RelationManagers;
use App\Filament\Resources\ChannelResource\Widgets\ChannelStats;
use App\Filament\Resources\RelationManagers\CommentsRelationManager;
use App\Filament\Resources\RelationManagers\ImageRelationManager;
use App\Models\Channel;
use App\Models\File\Image;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChannelResource extends Resource
{
    protected static ?int $navigationSort = 6;

    protected static ?string $modelLabel = 'Channel | Album';

    protected static ?string $pluralModelLabel = 'Channels | Albums';

    protected static ?string $model = Channel::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Libraries';

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        $auth = self::auth();

        $channelTypes = collect(ChannelType::cases())
            ->keyBy(fn ($type) => $type->value)
            ->map(fn ($type) => ucfirst($type->value));

        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535),

                        Forms\Components\Select::make('type')
                            ->options($channelTypes->toArray())
                            ->required(),

                        Forms\Components\FileUpload::make('image.filename_disk')
                            ->hiddenOn('edit')
                            ->label('Image')
                            ->image()
                            ->disk(Image::getDisk())
                            ->directory(Image::FOLDER)
                            ->visibility(Image::VISIBILITY)
                            ->minSize(Image::MIN_SIZE)
                            ->maxSize(Image::MAX_SIZE)
                            ->acceptedFileTypes(Image::IMAGE_MIME_TYPES),

                        Forms\Components\Select::make('genre_id')
                            ->label('Genre')
                            ->relationship('genre', 'name')
                            ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name} / {$record->category}")
                            ->preload()
                            ->searchable(),

                        Forms\Components\Select::make('user_id')
                            ->label('Artist')
                            ->disabled($auth->isOnlyArtist())
                            ->default($auth->isOnlyArtist() ? $auth->id : null)
                            ->relationship('user', 'email', function (Builder $query) use ($auth) {
                                $query->whereIn('role', User::getAllArtistTypes());

                                if ($auth->isOnlyArtist()) {
                                    $query->where('id', $auth->id);
                                }

                                return $query;
                            })
                            ->lazy()
                            ->searchable()
                            ->required(),

                    ])->columnSpan(['lg' => fn (?Channel $record) => $record === null ? 3 : 2]),

                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Placeholder::make('id')
                            ->content(fn (Channel $record): string => $record->id),

                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Channel $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Channel $record): ?string => $record->updated_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('likes')
                            ->content(
                                fn (Channel $record) => number_format($record->likes()->count())
                            ),

                        Forms\Components\Placeholder::make('downloads')
                            ->content(
                                fn (Channel $record) => number_format($record->downloads()->count())
                            ),

                        Forms\Components\Placeholder::make('audios')
                            ->content(
                                fn (Channel $record) => number_format($record->articles()->count())
                            ),

                        Forms\Components\Placeholder::make('comments')
                            ->content(
                                fn (Channel $record) => number_format($record->comments()->count())
                            ),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Channel $record) => $record === null),

            ])->columns([
                'sm' => 3,
                'lg' => null,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image.url')
                    ->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => ChannelType::CHANNEL->value,
                        'success' => ChannelType::ALBUM->value,
                        'secondary' => ChannelType::SINGLE->value,
                    ])
                    ->sortable(),

                Tables\Columns\TextColumn::make('genre.name'),

                Tables\Columns\TextColumn::make('user.name')->label('Artist'),

                Tables\Columns\TextColumn::make('likes_count')
                    ->label('Likes')
                    ->counts('likes')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('downloads_count')
                    ->label('Downloads')
                    ->counts('downloads')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('articles_count')
                    ->label('Audios')
                    ->counts('articles'),

                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            ImageRelationManager::class,
            RelationManagers\ArticlesRelationManager::class,
            CommentsRelationManager::class,
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }

    public static function getWidgets(): array
    {
        return [
            ChannelStats::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChannels::route('/'),
            'create' => Pages\CreateChannel::route('/create'),
            'edit' => Pages\EditChannel::route('/{record}/edit'),
        ];
    }

    public static function auth(): User
    {
        /**
         * @var User
         */
        $user = auth()->user();

        return $user;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);

        $auth = self::auth();

        if ($auth->isOnlyArtist()) {
            $query->where('user_id', $auth->id);
        }

        return $query;
    }
}
