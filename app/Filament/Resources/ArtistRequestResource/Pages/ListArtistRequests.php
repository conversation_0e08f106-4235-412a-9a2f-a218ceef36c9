<?php

namespace App\Filament\Resources\ArtistRequestResource\Pages;

use App\Filament\Resources\ArtistRequestResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListArtistRequests extends ListRecords
{
    protected static string $resource = ArtistRequestResource::class;

    protected function getTableQuery(): Builder
    {
        return parent::getTableQuery()->where('approved', false);
    }
}
