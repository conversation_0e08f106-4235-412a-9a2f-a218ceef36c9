<?php

namespace App\Filament\Resources\ArtistRequestResource\Pages;

use App\Filament\Resources\ArtistRequestResource;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewArtistRequest extends ViewRecord
{
    protected static string $resource = ArtistRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('Approve')
                ->action(function () {
                    $this->getRecord()?->fill(['approved' => true])->save();

                    Notification::make()
                        ->title('Artist request approved.')
                        ->success()
                        ->send();
                })
                ->requiresConfirmation(),
        ];
    }
}
