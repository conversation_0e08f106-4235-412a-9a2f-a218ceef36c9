<?php

namespace App\Filament\Resources\FileResource\Widgets;

use App\Models\Morph\File;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class FileStats extends BaseWidget
{
    protected function getCards(): array
    {
        return [
            Card::make('Total Files', File::count()),
        ];
    }

    public static function canView(): bool
    {
        /**
         * @var mixed
         */
        $user = auth()->user();

        return $user?->isAdmin() ?? false;
    }
}
