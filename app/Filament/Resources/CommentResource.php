<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CommentResource\Pages;
use App\Filament\Resources\CommentResource\Widgets\CommentStats;
use App\Models\Morph\Comment;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CommentResource extends Resource
{
    protected static ?int $navigationSort = 8;

    protected static ?string $model = Comment::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Libraries';

    public static function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('user.image.url')
                    ->circular(),

                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('comment')
                    ->limit(200),

                Tables\Columns\TextColumn::make('commentable_type'),

                Tables\Columns\TextColumn::make('created_at')
                    ->since(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->since(),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageComments::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            CommentStats::class,
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        if (auth()->user()?->isAdmin()) {
            return parent::getEloquentQuery()
                ->withoutGlobalScopes([SoftDeletingScope::class]);
        }

        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
