<?php

namespace App\Policies;

use App\Models\LegalDocument;
use App\Models\User;

class LegalDocumentPolicy
{
    /**
     * Determine whether the user can view any models.
     * Only admins can access the legal document management interface.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     * Only admins can view individual legal documents in admin panel.
     */
    public function view(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     * Only admins can create legal documents.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Only admins can update legal documents.
     */
    public function update(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     * Only admins can delete legal documents.
     */
    public function delete(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     * Only admins can restore legal documents.
     */
    public function restore(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only admins can permanently delete legal documents.
     */
    public function forceDelete(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can activate/deactivate the document.
     * Only admins can change document activation status.
     */
    public function activate(User $user, LegalDocument $legalDocument): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view public legal documents.
     * This is for public access to terms, privacy policy, etc.
     */
    public function viewPublic(?User $user, LegalDocument $legalDocument): bool
    {
        // Public documents can be viewed by anyone if they are active
        return $legalDocument->is_active;
    }
}
