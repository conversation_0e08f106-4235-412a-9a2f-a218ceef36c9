<?php

namespace App\Policies;

use App\Models\Subscription;
use App\Models\User;

class SubscriptionPolicy
{
    /**
     * Determine whether the user can view any models.
     * Only admins can access the subscription management interface.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     * Admins can view all subscriptions, users can view their own.
     */
    public function view(User $user, Subscription $subscription): bool
    {
        // Admins can view all subscriptions
        if ($user->isAdmin()) {
            return true;
        }

        // Users can view their own subscriptions
        return $user->id === $subscription->user_id;
    }

    /**
     * Determine whether the user can create models.
     * Only admins can manually create subscription records.
     * Normal subscription creation happens through payment flow.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Only admins can update subscription records.
     */
    public function update(User $user, Subscription $subscription): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     * Only admins can delete subscription records.
     */
    public function delete(User $user, Subscription $subscription): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     * Only admins can restore subscription records.
     */
    public function restore(User $user, Subscription $subscription): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only admins can permanently delete subscription records.
     */
    public function forceDelete(User $user, Subscription $subscription): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can cancel their own subscription.
     */
    public function cancel(User $user, Subscription $subscription): bool
    {
        // Users can cancel their own active subscriptions
        return $user->id === $subscription->user_id && $subscription->status === 'active';
    }
}
