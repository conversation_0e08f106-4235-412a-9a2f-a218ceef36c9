<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Prevent other admins from updating the first registered admin user
        // Only the first admin can update themselves
        if ($model->isFirstAdmin() && $user->id !== $model->id) {
            return false;
        }

        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        if ($user->id === $model->id) {
            // Users cannot delete themselves
            return false;
        }

        // Prevent deletion of the first registered admin user
        if ($model->isFirstAdmin()) {
            return false;
        }

        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        if ($user->id === $model->id) {
            // Users cannot force delete themselves
            return false;
        }

        // Prevent force deletion of the first registered admin user
        if ($model->isFirstAdmin()) {
            return false;
        }

        return $user->isAdmin();
    }

    /**
     * Determine whether the user can vote for this artist.
     */
    public function voteFor(User $user, User $artist): bool
    {
        // Users cannot vote for themselves
        if ($user->id === $artist->id) {
            return false;
        }

        // All authenticated users can vote for other artists
        return true;
    }

    /**
     * Determine whether the user can view their own vote history.
     */
    public function viewVoteHistory(User $user, User $model): bool
    {
        // Users can view their own vote history, admins can view anyone's
        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determine whether the user can view their own subscription details.
     */
    public function viewSubscriptions(User $user, User $model): bool
    {
        // Users can view their own subscriptions, admins can view anyone's
        return $user->id === $model->id || $user->isAdmin();
    }
}
