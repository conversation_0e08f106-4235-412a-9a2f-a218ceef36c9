<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vote;

class VotePolicy
{
    /**
     * Determine whether the user can view any models.
     * Only admins can access the vote management interface.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     * Only admins can view individual vote records.
     */
    public function view(User $user, Vote $vote): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     * Only admins can manually create vote records.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Only admins can update vote records.
     */
    public function update(User $user, Vote $vote): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     * Only admins can delete vote records.
     */
    public function delete(User $user, Vote $vote): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     * Only admins can restore vote records.
     */
    public function restore(User $user, Vote $vote): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only admins can permanently delete vote records.
     */
    public function forceDelete(User $user, Vote $vote): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can vote for an artist.
     * This is for the actual voting action, not admin management.
     */
    public function vote(User $user, User $artist): bool
    {
        // Users cannot vote for themselves
        if ($user->id === $artist->id) {
            return false;
        }

        // All authenticated users can vote (payment will be handled separately)
        return true;
    }
}
