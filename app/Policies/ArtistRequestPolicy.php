<?php

namespace App\Policies;

use App\Enums\UserRole;
use App\Models\ArtistRequest;
use App\Models\User;

class ArtistRequestPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->role === UserRole::ADMIN;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ArtistRequest $artistRequest): bool
    {
        return $user->id === $artistRequest->user_id || $user->role === UserRole::ADMIN;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->role === UserRole::GUEST && $user->artistRequest === null;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ArtistRequest $artistRequest): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ArtistRequest $artistRequest): bool
    {
        return $user->role === UserRole::ADMIN || $user->id === $artistRequest->user_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ArtistRequest $artistRequest): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ArtistRequest $artistRequest): bool
    {
        return false;
    }
}
