<?php

namespace App\Policies;

use App\Models\User;
use App\Models\VoteTransaction;

class VoteTransactionPolicy
{
    /**
     * Determine whether the user can view any models.
     * Only admins can access the vote transaction management interface.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     * Only admins can view individual vote transaction records.
     */
    public function view(User $user, VoteTransaction $voteTransaction): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     * Only admins can manually create vote transaction records.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Only admins can update vote transaction records.
     */
    public function update(User $user, VoteTransaction $voteTransaction): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     * Only admins can delete vote transaction records.
     */
    public function delete(User $user, VoteTransaction $voteTransaction): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     * Only admins can restore vote transaction records.
     */
    public function restore(User $user, VoteTransaction $voteTransaction): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only admins can permanently delete vote transaction records.
     */
    public function forceDelete(User $user, VoteTransaction $voteTransaction): bool
    {
        return $user->isAdmin();
    }
}
