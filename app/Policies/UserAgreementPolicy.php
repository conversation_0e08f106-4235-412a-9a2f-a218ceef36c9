<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserAgreement;

class UserAgreementPolicy
{
    /**
     * Determine whether the user can view any models.
     * Only admins can access the user agreement management interface.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     * Admins can view all agreements, users can view their own.
     */
    public function view(User $user, UserAgreement $userAgreement): bool
    {
        // Admins can view all user agreements
        if ($user->isAdmin()) {
            return true;
        }

        // Users can view their own agreements
        return $user->id === $userAgreement->user_id;
    }

    /**
     * Determine whether the user can create models.
     * Only admins can manually create user agreement records.
     * Normal agreement creation happens through the application flow.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Only admins can update user agreement records.
     * User agreements should generally be immutable once created.
     */
    public function update(User $user, UserAgreement $userAgreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     * Only admins can delete user agreement records.
     */
    public function delete(User $user, UserAgreement $userAgreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     * Only admins can restore user agreement records.
     */
    public function restore(User $user, UserAgreement $userAgreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     * Only admins can permanently delete user agreement records.
     */
    public function forceDelete(User $user, UserAgreement $userAgreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can record a new agreement.
     * Users can record agreements for themselves, admins can record for anyone.
     */
    public function record(User $user, string $targetUserId): bool
    {
        // Admins can record agreements for anyone
        if ($user->isAdmin()) {
            return true;
        }

        // Users can only record agreements for themselves
        return $user->id === $targetUserId;
    }
}
