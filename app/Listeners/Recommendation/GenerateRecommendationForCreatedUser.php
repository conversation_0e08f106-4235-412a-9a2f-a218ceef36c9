<?php

namespace App\Listeners\Recommendation;

use App\Jobs\Recommendation\GenerateSingleUserRecommendationJob;
use Illuminate\Auth\Events\Registered;

class GenerateRecommendationForCreatedUser
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        if ($event->user) {
            // Dispatch the job to generate recommendations for the new user
            GenerateSingleUserRecommendationJob::dispatch($event->user);
        }
    }
}
