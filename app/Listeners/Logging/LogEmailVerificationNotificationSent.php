<?php

namespace App\Listeners\Logging;

use App\Events\Auth\EmailVerificationNotificationSent;

class LogEmailVerificationNotificationSent
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(EmailVerificationNotificationSent $event): void
    {
        if ($event->user) {
            logActivity([
                'user_id' => $event->user->id,
                'model' => $event->user->getTable(),
                'json' => $event->user->toArray(),
                'description' => 'EmailVerificationNotificationSent',
            ]);
        }
    }
}
