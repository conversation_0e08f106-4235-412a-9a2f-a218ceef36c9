<?php

namespace App\Listeners\Logging;

use App\Events\Auth\UserPasswordUpdated;

class LogUserPasswordUpdated
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserPasswordUpdated $event): void
    {
        if ($event->user) {
            logActivity([
                'user_id' => $event->user->id,
                'model' => $event->user->getTable(),
                'json' => $event->user->toArray(),
                'description' => 'UserPasswordUpdated',
            ]);
        }
    }
}
