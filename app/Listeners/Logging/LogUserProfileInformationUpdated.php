<?php

namespace App\Listeners\Logging;

use App\Events\Auth\UserProfileInformationUpdated;

class LogUserProfileInformationUpdated
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserProfileInformationUpdated $event): void
    {
        if ($event->user) {
            logActivity([
                'user_id' => $event->user->id,
                'model' => $event->user->getTable(),
                'json' => $event->user->toArray(),
                'description' => 'UserProfileInformationUpdated',
            ]);
        }
    }
}
