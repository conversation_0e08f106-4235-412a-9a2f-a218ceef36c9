<?php

namespace App\Listeners\Logging;

use Illuminate\Auth\Events\Registered;

class LogRegisteredUser
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        if ($event->user) {
            logActivity([
                'user_id' => $event->user->id,
                'model' => $event->user->getTable(),
                'json' => $event->user->toArray(),
                'description' => 'Registered',
            ]);
        }
    }
}
