<?php

namespace App\Listeners\Logging;

use Illuminate\Auth\Events\Login;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        if ($event->user) {
            logActivity([
                'user_id' => $event->user->id,
                'model' => $event->user->getTable(),
                'json' => $event->user->toArray(),
                'description' => 'Login',
            ]);
        }
    }
}
