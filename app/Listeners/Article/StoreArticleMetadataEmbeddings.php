<?php

namespace App\Listeners\Article;

use App\Events;
use App\Jobs\Embedding;

class StoreArticleMetadataEmbeddings
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Events\ArticleCreated|Events\ArticleUpdated $event): void
    {
        // Embedding\StoreExtractedArticleMetadataEmbedding::dispatch($event->article);
    }
}
