<?php

namespace App\Listeners\Audio;

use App\Events\FileAudioCreated;
use App\Jobs\Embedding;

class StoreAudioEmbeddings
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FileAudioCreated $event): void
    {
        Embedding\StorePredictedAudioEmbedding::dispatch($event->file);
    }
}
