<?php

namespace App\Listeners\Audio;

use App\Events\FileAudioCreated;
use App\Jobs\StoreExtractedAudioMetadata;

class StoreAudioMetadata
{
    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(FileAudioCreated $event): void
    {
        // StoreExtractedAudioMetadata::dispatch($event->file);
    }
}
