<?php

namespace App\Services;

use App\Models\Article;
use App\Models\Embedding\ArticleAudioEmbedding;
use App\Models\Play;
use App\Models\User;
use App\Repositories\ArticleRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Pgvector\Laravel\Distance;
use Pgvector\Laravel\Vector;

class PlayerQueueService
{
    private array $config;

    public function __construct()
    {
        $this->config = config('recommendations'); // Reuse recommendations config for now
    }

    /**
     * Suggest the next track for the Auto DJ Mix based on similarity.
     *
     * @param  Collection  $sessionPlayedIds  IDs played in the current session (last is current track).
     * @param  User|null  $user  The currently authenticated user (optional for broader exclusions).
     * @return Article|null The suggested next Article, or null if none found.
     */
    public function getNextDjTrack(Collection $sessionPlayedIds, ?User $user = null): ?Article
    {
        if ($sessionPlayedIds->isEmpty()) {
            return null; // Cannot suggest without context
        }

        // --- Get the Seed Embedding ---
        $currentTrackId = $sessionPlayedIds->last();
        $seedEmbedding = $this->getAverageSeedEmbedding($sessionPlayedIds); // Optional: Average of last N tracks
        if (! $seedEmbedding) {
            Log::info("No average embedding found for session played IDs: {$sessionPlayedIds->implode(', ')}");
            $seedEmbedding = $this->getSeedEmbedding($currentTrackId);
        }

        if (! $seedEmbedding) {
            // Fallback: Maybe return a random popular track not in sessionPlayedIds?
            return $this->findFallbackTrack($sessionPlayedIds, $user);
        }

        // --- Prepare Exclusion List ---
        // Start with IDs played in this specific session
        $excludeIds = collect($sessionPlayedIds);

        // Optional: Add tracks played generally by the user recently
        if ($user && ($days = $this->config['dj_mix_exclude_recent_plays_days'] ?? null)) {
            $recentUserPlays = Play::where('player_id', $user->id)
                ->where('created_at', '>=', Carbon::now()->subDays($days))
                ->distinct()
                ->pluck('article_id');
            $excludeIds = $excludeIds->merge($recentUserPlays);
        }

        // Optional: Add disliked tracks (requires tracking dislikes)
        // if ($user) {
        //    $dislikedIds = $user->dislikes()->pluck('article_id'); // Assuming dislike relationship
        //    $excludeIds = $excludeIds->merge($dislikedIds);
        // }

        $excludeIds = $excludeIds->unique();

        // --- Find Similar Neighbors ---
        $neighborsToFetch = $this->config['dj_mix_neighbors_to_fetch'] ?? 20;
        $maxCandidatesToCheck = $this->config['duplicate_detection']['max_candidates_to_check'] ?? 50;

        try {
            $candidates = ArticleAudioEmbedding::query()
                ->select('article_id')
                ->nearestNeighbors('embedding', $seedEmbedding->toArray(), Distance::Cosine)
                ->whereNotNull('embedding')
                ->where('article_id', '!=', $currentTrackId) // Exclude the current track itself
                ->whereNotIn('article_id', $excludeIds)
                ->limit($maxCandidatesToCheck) // Fetch more candidates for duplicate filtering
                ->pluck('article_id');

        } catch (\Exception $e) {
            Log::error("DJ Mix similarity search failed for track {$currentTrackId}: ".$e->getMessage());

            return $this->findFallbackTrack($sessionPlayedIds, $user); // Attempt fallback on error
        }

        if ($candidates->isEmpty()) {
            // Fallback: Find a popular track not played yet
            return $this->findFallbackTrack($sessionPlayedIds, $user);
        }

        // --- Filter Out Duplicates and Apply Diversity Rules ---
        $filteredCandidates = $this->filterDuplicatesAndApplyDiversity(
            $candidates,
            $sessionPlayedIds,
            $currentTrackId
        );

        Log::info("DJ Mix: Found {$candidates->count()} candidates, filtered to {$filteredCandidates->count()} after duplicate detection", [
            'current_track_id' => $currentTrackId,
            'original_candidates' => $candidates->count(),
            'filtered_candidates' => $filteredCandidates->count(),
        ]);

        if ($filteredCandidates->isEmpty()) {
            Log::warning('DJ Mix: All candidates filtered out as duplicates, using fallback', [
                'current_track_id' => $currentTrackId,
                'session_played_count' => $sessionPlayedIds->count(),
            ]);

            // If all candidates were filtered out, try fallback
            return $this->findFallbackTrack($sessionPlayedIds, $user);
        }

        // --- Select the Best Candidate ---
        // Apply similarity vs diversity balance
        $nextTrackId = $this->selectCandidateWithBalance($filteredCandidates, $neighborsToFetch);

        // --- Fetch Full Article Details ---
        // Load necessary relationships for the player
        return Article::with(ArticleRepository::RELATIONS)->findSole(id: $nextTrackId);
    }

    /**
     * Get the embedding vector for a given track ID.
     */
    private function getSeedEmbedding(int|string $articleId): ?Vector
    {
        // Assuming your embedding model stores the Vector object directly
        $embeddingRecord = ArticleAudioEmbedding::where('article_id', $articleId)->first(['embedding']);

        return $embeddingRecord?->embedding;
    }

    /**
     *  Calculate average embedding of the last N tracks.
     */
    private function getAverageSeedEmbedding(Collection $sessionPlayedIds): ?Vector
    {
        $count = $this->config['dj_mix_use_embedding_average_count'] ?? 3;
        $idsToAverage = $sessionPlayedIds->slice(-$count); // Get last N IDs

        if ($idsToAverage->isEmpty()) {
            return null;
        }

        $embeddings = ArticleAudioEmbedding::whereIn('article_id', $idsToAverage)
            ->whereNotNull('embedding')
            ->pluck('embedding'); // Pluck the Vector objects or arrays

        if ($embeddings->isEmpty()) {
            return null;
        }

        // Assuming embeddings are Vector objects or arrays of numbers
        $numVectors = $embeddings->count();
        $dimension = count(value: $embeddings->first()->toArray()); // Assumes consistent dimension
        $average = array_fill(0, $dimension, 0.0);

        foreach ($embeddings as $vector) {
            // Handle both Vector object and array cases if necessary
            $vectorArray = is_array($vector) ? $vector : $vector->toArray();
            if (count($vectorArray) !== $dimension) {
                continue;
            } // Skip malformed

            for ($i = 0; $i < $dimension; $i++) {
                $average[$i] += (float) ($vectorArray[$i] ?? 0.0);
            }
        }

        for ($i = 0; $i < $dimension; $i++) {
            $average[$i] /= $numVectors;
        }

        return new Vector($average); // Return a new Vector object
    }

    /**
     * Provides a fallback track if similarity search fails or yields no results.
     * Finds a popular track not in the exclusion list.
     */
    private function findFallbackTrack(Collection $excludeIds, ?User $user = null): ?Article
    {
        $popularityDays = $this->config['popularity_days'] ?? 7;
        $minPlays = $this->config['min_popular_plays'] ?? 5;

        Log::info("Finding fallback track, excluding IDs: {$excludeIds->implode(', ')}");

        $fallbackId = Play::select('article_id')
            ->where('created_at', '>=', Carbon::now()->subDays($popularityDays))
            ->groupBy('article_id')
            ->having(DB::raw('COUNT(*)'), '>=', $minPlays)
            ->whereNotIn('article_id', $excludeIds) // Exclude session/recent plays
            ->orderByDesc(DB::raw('COUNT(*)'))
            ->value('article_id'); // Get just the ID of the most popular suitable track

        if ($fallbackId) {
            return Article::with(ArticleRepository::RELATIONS)->findSole($fallbackId);
        } else {
            return null; // Truly nothing found
        }
    }

    /**
     * Filter out duplicate tracks and apply diversity rules to candidate list.
     *
     * @param  Collection  $candidateIds  List of candidate article IDs
     * @param  Collection  $sessionPlayedIds  IDs played in current session
     * @param  int|string  $currentTrackId  Current track ID
     * @return Collection Filtered candidate IDs
     */
    private function filterDuplicatesAndApplyDiversity(
        Collection $candidateIds,
        Collection $sessionPlayedIds,
        int|string $currentTrackId
    ): Collection {
        if (! $this->config['duplicate_detection']['enabled'] ?? true) {
            return $candidateIds; // Return unfiltered if duplicate detection is disabled
        }

        // Get all session tracks + current track for comparison
        $sessionTrackIds = $sessionPlayedIds->push($currentTrackId);

        // Load session tracks with necessary relationships for comparison
        $sessionTracks = Article::with(['user', 'audioMetadata'])
            ->whereIn('id', $sessionTrackIds)
            ->get()
            ->keyBy('id');

        // Load candidate tracks with necessary relationships
        $candidateTracks = Article::with(['user', 'audioMetadata'])
            ->whereIn('id', $candidateIds)
            ->get()
            ->keyBy('id');

        $filteredCandidates = collect();
        $currentTrack = $sessionTracks->get($currentTrackId);

        foreach ($candidateIds as $candidateId) {
            $candidate = $candidateTracks->get($candidateId);

            if (! $candidate) {
                continue; // Skip if candidate not found
            }

            // Check if this candidate is a duplicate of any session track
            $isDuplicate = false;
            foreach ($sessionTracks as $sessionTrack) {
                if ($this->areTracksDuplicate($candidate, $sessionTrack)) {
                    $isDuplicate = true;
                    break;
                }
            }

            if ($isDuplicate) {
                Log::debug('DJ Mix: Skipping duplicate track', [
                    'candidate_id' => $candidateId,
                    'candidate_name' => $candidate->name,
                    'candidate_artist' => $candidate->user?->name,
                ]);

                continue; // Skip duplicates
            }

            // Apply diversity rules
            if (! $this->passesArtistDiversityCheck($candidate, $currentTrack)) {
                Log::debug('DJ Mix: Skipping track for artist diversity', [
                    'candidate_id' => $candidateId,
                    'candidate_name' => $candidate->name,
                    'candidate_artist' => $candidate->user?->name,
                    'current_artist' => $currentTrack?->user?->name,
                ]);

                continue; // Skip if fails artist diversity check
            }

            $filteredCandidates->push($candidateId);
        }

        return $filteredCandidates;
    }

    /**
     * Check if two tracks are considered duplicates based on various criteria.
     */
    private function areTracksDuplicate(Article $track1, Article $track2): bool
    {
        $config = $this->config['duplicate_detection'];

        // Same ID is obviously the same track
        if ($track1->id === $track2->id) {
            return true;
        }

        // Check name similarity
        if ($config['name_similarity_threshold'] ?? 0.85) {
            $similarity = $this->calculateStringSimilarity($track1->name, $track2->name);
            if ($similarity >= ($config['name_similarity_threshold'] ?? 0.85)) {

                // If names are very similar, check additional criteria
                $additionalChecks = 0;
                $passedChecks = 0;

                // Check same artist/user
                if ($config['check_same_artist'] ?? true) {
                    $additionalChecks++;
                    if ($track1->user_id === $track2->user_id) {
                        $passedChecks++;
                    }
                }

                // Check audio duration similarity
                if ($config['check_audio_duration'] ?? true) {
                    $additionalChecks++;
                    if ($this->areAudioDurationsSimilar($track1, $track2)) {
                        $passedChecks++;
                    }
                }

                // Check audio metadata similarity (tempo, etc.)
                if ($config['check_audio_metadata'] ?? true) {
                    $additionalChecks++;
                    if ($this->areAudioMetadataSimilar($track1, $track2)) {
                        $passedChecks++;
                    }
                }

                // Consider duplicate if name similarity is high and at least one additional check passes
                return $additionalChecks > 0 && $passedChecks > 0;
            }
        }

        return false;
    }

    /**
     * Calculate string similarity using Levenshtein distance.
     */
    private function calculateStringSimilarity(string $str1, string $str2): float
    {
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));

        if ($str1 === $str2) {
            return 1.0;
        }

        $maxLength = max(strlen($str1), strlen($str2));
        if ($maxLength === 0) {
            return 1.0;
        }

        $distance = levenshtein($str1, $str2);

        return 1.0 - $distance / $maxLength;
    }

    /**
     * Check if audio durations are similar within tolerance.
     */
    private function areAudioDurationsSimilar(Article $track1, Article $track2): bool
    {
        $tolerance = $this->config['duplicate_detection']['duration_tolerance_seconds'] ?? 5;

        $duration1 = $track1->audioMetadata?->duration ?? 0;
        $duration2 = $track2->audioMetadata?->duration ?? 0;

        if ($duration1 == 0 || $duration2 == 0) {
            return false; // Can't compare if duration data is missing
        }

        return abs($duration1 - $duration2) <= $tolerance;
    }

    /**
     * Check if audio metadata (tempo, etc.) are similar.
     */
    private function areAudioMetadataSimilar(Article $track1, Article $track2): bool
    {
        $tempoTolerance = $this->config['duplicate_detection']['tempo_tolerance_bpm'] ?? 3;

        $tempo1 = $track1->audioMetadata?->tempo ?? 0;
        $tempo2 = $track2->audioMetadata?->tempo ?? 0;

        if ($tempo1 == 0 || $tempo2 == 0) {
            return false; // Can't compare if tempo data is missing
        }

        return abs($tempo1 - $tempo2) <= $tempoTolerance;
    }

    /**
     * Check if candidate passes artist diversity rules.
     */
    private function passesArtistDiversityCheck(Article $candidate, ?Article $currentTrack): bool
    {
        if (! $currentTrack || ! ($this->config['duplicate_detection']['exclude_duplicate_artists_consecutive'] ?? true)) {
            return true; // No diversity check needed
        }

        // Avoid same artist back-to-back
        return $candidate->user_id !== $currentTrack->user_id;
    }

    /**
     * Select a candidate based on the similarity vs diversity balance configuration.
     *
     * @param  Collection  $filteredCandidates  Candidates after duplicate filtering (ordered by similarity)
     * @param  int  $neighborsToFetch  Maximum number of candidates to consider
     * @return int|string Selected candidate ID
     */
    private function selectCandidateWithBalance(Collection $filteredCandidates, int $neighborsToFetch): int|string
    {
        $balance = $this->config['dj_mix_similarity_vs_diversity_balance'] ?? 0.7;
        $candidateCount = min($filteredCandidates->count(), $neighborsToFetch);

        if ($candidateCount <= 1) {
            return $filteredCandidates->first();
        }

        // Calculate selection range based on balance
        // balance = 0.0: select from top 1-2 (most similar)
        // balance = 0.5: select from top half
        // balance = 1.0: select from all candidates (most diverse)
        $selectionRangeSize = max(1, (int) ceil($candidateCount * (0.1 + $balance * 0.9)));

        $selectionPool = $filteredCandidates->take($selectionRangeSize);

        Log::debug('DJ Mix: Candidate selection', [
            'total_filtered' => $filteredCandidates->count(),
            'selection_pool_size' => $selectionPool->count(),
            'balance_setting' => $balance,
        ]);

        return $selectionPool->random();
    }
}
