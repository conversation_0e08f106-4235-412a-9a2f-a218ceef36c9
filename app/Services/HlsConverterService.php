<?php

namespace App\Services;

use App\Media\TmpFile;
use App\Models\File\Audio;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process;

class HlsConverterService
{
    public function convertToHls(Audio $audio)
    {
        $localDisk = Storage::disk('local');
        $fileOutputPath = 'hls/'.$audio->id;

        $outputDir = $localDisk->path($fileOutputPath);
        $localDisk->makeDirectory($outputDir);

        $tmpFile = new TmpFile($audio); // use tmp file since we don't know where the file is stored
        $fullInputPath = $tmpFile->path();

        $bitrates = $this->getHlsBitrates();
        $segmentLength = $this->getHlsSegmentLength();

        // Create a master playlist
        $masterPlaylist = "#EXTM3U\n";
        $masterPlaylist .= "#EXT-X-VERSION:3\n";

        foreach ($bitrates as $quality => $bitrate) {
            $qualityDir = $outputDir.'/'.$quality;
            if (! file_exists($qualityDir)) {
                mkdir($qualityDir, 0755, true);
            }

            // Convert bitrate string (e.g., '128k') to integer bps for BANDWIDTH calculation
            $bitrateBps = (int) str_ireplace('k', '000', $bitrate);
            // Estimate peak bandwidth (e.g., 1.1x target) - adjust multiplier as needed
            $peakBandwidth = (int) ($bitrateBps * 1.1);

            $qualityCommand = [
                'ffmpeg',
                '-i', $fullInputPath,

                // Audio Encoding Options
                '-c:a', 'aac',                 // Use AAC codec
                '-ac', '2',                    // Force stereo output
                '-b:a', $bitrate,              // Set the target audio bitrate (e.g., '128k')

                // Mapping
                '-map', '0:a',                 // Select only the audio stream

                // HLS Output Options
                '-f', 'hls',                   // Use the HLS muxer
                '-hls_time', (string) $segmentLength, // Target segment duration
                '-hls_playlist_type', 'vod',   // Mark as Video-on-Demand
                '-hls_segment_filename', $qualityDir.'/segment_%03d.ts', // Pattern for segment filenames (relative to playlist)

                // Output Media Playlist for this quality
                $qualityDir.'/playlist.m3u8',
            ];

            $process = new Process($qualityCommand);
            $process->setTimeout(3600); // 1 hour timeout
            $process->run();

            if (! $process->isSuccessful()) {
                // Clean up generated files for this failed attempt might be good practice here
                $localDisk->deleteDirectory($fileOutputPath); // Delete the whole attempt on failure
                throw new \RuntimeException(sprintf(
                    'FFmpeg process failed for quality "%s" (%s): %s',
                    $quality,
                    $bitrate,
                    $process->getErrorOutput()
                ));
            }

            // --- Add entry to Master Playlist ---
            $masterPlaylist .= sprintf(
                "#EXT-X-STREAM-INF:BANDWIDTH=%d,AVERAGE-BANDWIDTH=%d,CODECS=\"mp4a.40.2\"\n",
                $peakBandwidth, // Use estimated peak bandwidth
                $bitrateBps     // Use target average bandwidth
            );
            $masterPlaylist .= $quality."/playlist.m3u8\n";
        }

        // --- Save the Master Playlist ---
        $masterPlaylistPath = $outputDir.'/master.m3u8';
        if (File::put($masterPlaylistPath, $masterPlaylist) === false) {
            $localDisk->deleteDirectory($fileOutputPath); // Clean up if saving master fails
            throw new \RuntimeException('Failed to write master playlist to: '.$masterPlaylistPath);
        }

        $localDisksName = ['local', 'public'];
        // If the audio file is stored on a local disk
        // upload to the remote storage all generated files, then delete the local files
        if (! in_array($audio->storage, $localDisksName)) {
            $remoteDisk = Storage::disk($audio->storage);
            $allFiles = File::allFiles($outputDir);

            foreach ($allFiles as $file) {
                $filePath = $file->getRealPath();
                // Calculate remote path relative to the base output directory
                $relativePath = ltrim(str_replace($outputDir, '', $filePath), DIRECTORY_SEPARATOR);
                $remotePath = $fileOutputPath.'/'.$relativePath;

                // Use streaming put for potentially large files
                $stream = fopen($filePath, 'r+');
                $remoteDisk->put($remotePath, $stream);
                if (is_resource($stream)) {
                    fclose($stream);
                }
            }

            File::deleteDirectory($outputDir);
        }

        return [
            'directory' => $fileOutputPath,
            'master_playlist' => $fileOutputPath.'/master.m3u8',
        ];
    }

    public function getStreamUrl($hlsDirectory)
    {
        return url('audio-hls/'.$hlsDirectory.'/master.m3u8');
    }

    private function getHlsBitrates(): array
    {
        $bitrates = config('audio.hls_bitrates');
        if (empty($bitrates) || ! is_array($bitrates)) {
            // Provide a sensible default or throw an error if config is missing/invalid
            return ['low' => '64k', 'medium' => '128k', 'high' => '192k'];
        }

        return $bitrates;
    }

    private function getHlsSegmentLength(): int
    {
        $length = config('audio.hls_segment_length');

        // Provide a sensible default
        return is_int($length) && $length > 0 ? $length : 6;
    }
}
