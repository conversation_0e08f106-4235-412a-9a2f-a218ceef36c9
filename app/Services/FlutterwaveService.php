<?php

namespace App\Services;

use App\Models\Competition;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Vote;
use App\Models\VoteTransaction;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class FlutterwaveService
{
    /**
     * The Flutterwave API base URL.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The Flutterwave secret key.
     *
     * @var string
     */
    protected $secretKey;

    /**
     * Create a new Flutterwave service instance.
     */
    public function __construct()
    {
        $this->baseUrl = config('services.flutterwave.base_url', 'https://api.flutterwave.com/v3');
        $this->secretKey = config('services.flutterwave.secret_key', 'FLUTTERWAVE_SECRET_KEY');
    }

    /**
     * Initialize a subscription payment.
     *
     * @param  User  $user  The user subscribing
     * @param  string  $plan  The subscription plan (annual, monthly)
     * @param  float  $amount  The payment amount
     * @param  string  $redirectUrl  The URL to redirect to after payment
     * @return array
     */
    public function initializeSubscriptionPayment(User $user, string $plan, float $amount, string $redirectUrl)
    {
        // For now, this is a mock implementation
        // In a real implementation, we would make an API call to Flutterwave

        // Create a mock transaction reference
        $txRef = 'SUB_'.Str::random(10);

        // Log the payment initialization
        Log::info('Mock Flutterwave subscription payment initialized', [
            'user' => $user->id,
            'plan' => $plan,
            'amount' => $amount,
            'tx_ref' => $txRef,
        ]);

        // In a real implementation, we would return the payment link from Flutterwave
        // For now, we'll return a mock response
        return [
            'status' => 'success',
            'message' => 'Subscription payment initialized',
            'data' => [
                'link' => route('app.voting.payment.mock', [
                    'tx_ref' => $txRef,
                    'voter_id' => $user->id,
                    'artist_id' => null,
                    'competition_id' => null,
                    'amount' => $amount,
                    'redirect_url' => $redirectUrl,
                    'subscription_plan' => $plan,
                ]),
            ],
        ];
    }

    /**
     * Initialize a payment for voting.
     *
     * @param  User  $voter  The user making the payment
     * @param  User  $artist  The artist being voted for
     * @param  Competition  $competition  The competition
     * @param  int  $voteCount  The number of votes
     * @param  float  $amount  The payment amount
     * @param  string  $redirectUrl  The URL to redirect to after payment
     * @return array
     */
    public function initializeVotePayment(User $voter, User $artist, Competition $competition, int $voteCount, float $amount, string $redirectUrl)
    {
        // For now, this is a mock implementation
        // In a real implementation, we would make an API call to Flutterwave

        // Create a mock transaction reference
        $txRef = 'VOTE_'.Str::random(10);

        // Log the payment initialization
        Log::info('Mock Flutterwave vote payment initialized', [
            'voter' => $voter->id,
            'artist' => $artist->id,
            'competition' => $competition->id,
            'vote_count' => $voteCount,
            'amount' => $amount,
            'tx_ref' => $txRef,
        ]);

        // In a real implementation, we would return the payment link from Flutterwave
        // For now, we'll return a mock response
        return [
            'status' => 'success',
            'message' => 'Vote payment initialized',
            'data' => [
                'link' => route('app.voting.payment.mock', [
                    'tx_ref' => $txRef,
                    'voter_id' => $voter->id,
                    'artist_id' => $artist->id,
                    'competition_id' => $competition->id,
                    'vote_count' => $voteCount,
                    'amount' => $amount,
                    'redirect_url' => $redirectUrl,
                ]),
            ],
        ];
    }

    /**
     * Verify a payment.
     *
     * @param  string  $transactionId  The transaction ID from Flutterwave
     * @return array
     */
    public function verifyPayment(string $transactionId)
    {
        // For now, this is a mock implementation
        // In a real implementation, we would make an API call to Flutterwave

        // Log the payment verification
        Log::info('Mock Flutterwave payment verification', [
            'transaction_id' => $transactionId,
        ]);

        // In a real implementation, we would verify the payment with Flutterwave
        // For now, we'll return a mock successful response
        return [
            'status' => 'success',
            'message' => 'Payment verified',
            'data' => [
                'status' => 'successful',
                'tx_ref' => $transactionId,
                'amount' => 1.00,
                'currency' => 'USD',
            ],
        ];
    }

    /**
     * Process a successful subscription payment.
     *
     * @param  array  $paymentData  The payment data
     * @return Subscription
     */
    public function processSubscriptionPayment(array $paymentData)
    {
        // Extract payment data
        $userId = $paymentData['voter_id'];
        $amount = $paymentData['amount'];
        $transactionId = $paymentData['transaction_id'];
        $plan = $paymentData['subscription_plan'] ?? 'annual';

        // Calculate subscription duration
        $startDate = now();
        $endDate = $plan === 'annual' ? $startDate->copy()->addYear() : $startDate->copy()->addMonth();

        // Create the subscription
        $subscription = Subscription::create([
            'user_id' => $userId,
            'plan' => $plan,
            'status' => 'active',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'transaction_id' => $transactionId,
            'amount' => $amount,
        ]);

        return $subscription;
    }

    /**
     * Process a successful vote payment.
     *
     * @param  array  $paymentData  The payment data
     * @return Vote|null
     */
    public function processVotePayment(array $paymentData)
    {
        // Extract payment data
        $voterId = $paymentData['voter_id'];
        $artistId = $paymentData['artist_id'];
        $competitionId = $paymentData['competition_id'];
        $amount = $paymentData['amount'];
        $transactionId = $paymentData['transaction_id'];
        $voteCount = $paymentData['vote_count'] ?? 1;

        // Check if we have a subscription plan in the payment data (legacy support)
        if (isset($paymentData['subscription_plan'])) {
            // Process as a subscription payment
            $this->processSubscriptionPayment($paymentData);

            // Create a free vote for subscription
            $vote = null;
            if ($artistId && $competitionId) {
                $vote = Vote::create([
                    'competition_id' => $competitionId,
                    'user_id' => $voterId,
                    'artist_id' => $artistId,
                    'vote_count' => 1,
                    'paid' => false, // Free with subscription
                ]);
            }

            return $vote;
        }

        // Create the paid vote
        $vote = null;
        if ($artistId && $competitionId) {
            $vote = Vote::create([
                'competition_id' => $competitionId,
                'user_id' => $voterId,
                'artist_id' => $artistId,
                'vote_count' => $voteCount,
                'paid' => true,
            ]);

            // Create the vote transaction record
            VoteTransaction::create([
                'vote_id' => $vote->id,
                'amount' => $amount,
                'transaction_id' => $transactionId,
                'status' => 'completed',
                'provider' => 'flutterwave',
            ]);
        }

        return $vote;
    }
}
