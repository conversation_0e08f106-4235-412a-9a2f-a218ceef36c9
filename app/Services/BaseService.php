<?php

namespace App\Services;

abstract class BaseService
{
    protected function __construct()
    {
        $this->validate();
    }

    abstract protected function url(): ?string;

    abstract public function handle();

    protected function validate()
    {
        throw_if(
            condition: blank($this->url()),
            exception: new \Exception('Service URL not defined')
        );
    }
}
