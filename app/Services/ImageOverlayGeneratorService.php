<?php

namespace App\Services;

class ImageOverlayGeneratorService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    public function handle() {}

    public function url(): ?string
    {
        $config = config('services.ig.url');

        if (empty($config)) {
            return null;
        }

        return (string) $config.'/overlay/generate';
    }
}
