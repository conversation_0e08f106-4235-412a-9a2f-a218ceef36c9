<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class AudioEmbeddingGeneratorService extends BaseService
{
    public function __construct(
        private string $waveFile
    ) {
        parent::__construct();
    }

    public function handle()
    {
        $response = Http::attach(
            name: 'audio',

            contents: @fopen($this->waveFile, 'rb'),

            filename: str($this->waveFile)->basename(),

            headers: ['Content-Type' => 'audio/wav']
        )->asMultipart()->timeout(60 * 30)->post($this->url());

        $json = $response->json();

        throw_if(
            $json['status'] !== 'ok',
            new \Exception('Invalid embedding request status', $response->status())
        );

        return $json;
    }

    protected function url(): string
    {
        return (string) config('services.audio_embedding_generator.url');
    }
}
