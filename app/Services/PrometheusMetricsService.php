<?php

namespace App\Services;

use App\Models\Article;
use App\Models\Channel;
use App\Models\Competition;
use App\Models\CompetitionEntry;
use App\Models\Embedding\ArticleAudioEmbedding;
use App\Models\Following;
use App\Models\Genre;
use App\Models\Morph\Comment;
use App\Models\Morph\Like;
use App\Models\Play;
use App\Models\Playlist\Playlist;
use App\Models\User;
use App\Models\Vote;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use Prometheus\Storage\InMemory;

class PrometheusMetricsService
{
    private CollectorRegistry $registry;

    private RenderTextFormat $renderer;

    public function __construct()
    {
        $this->registry = new CollectorRegistry(new InMemory);
        $this->renderer = new RenderTextFormat;
    }

    /**
     * Generate all metrics and return formatted output
     */
    public function generateMetrics(): string
    {
        $cacheKey = 'prometheus_metrics';
        $cacheDuration = config('metrics.cache_duration', 60);

        if ($cacheDuration > 0) {
            return Cache::remember($cacheKey, $cacheDuration, function () {
                return $this->collectMetrics();
            });
        }

        return $this->collectMetrics();
    }

    /**
     * Collect all metrics
     */
    private function collectMetrics(): string
    {
        $enabledMetrics = config('metrics.enabled_metrics', []);

        if ($enabledMetrics['application'] ?? true) {
            $this->registerApplicationMetrics();
        }

        if ($enabledMetrics['business'] ?? true) {
            $this->registerBusinessMetrics();
        }

        if ($enabledMetrics['infrastructure'] ?? true) {
            $this->registerInfrastructureMetrics();
        }

        if ($enabledMetrics['performance'] ?? true) {
            $this->registerPerformanceMetrics();
        }

        // Add threshold metrics for alerting
        $this->registerThresholdMetrics();

        return $this->renderer->render($this->registry->getMetricFamilySamples());
    }

    /**
     * Register application health and performance metrics
     */
    private function registerApplicationMetrics(): void
    {
        // Application info
        $appInfo = $this->registry->getOrRegisterGauge(
            'smovee',
            'app_info',
            'Application information',
            ['version', 'environment']
        );
        $appInfo->set(1, [config('app.version', '1.0.0'), config('app.env')]);

        // Database connection status
        $dbStatus = $this->registry->getOrRegisterGauge(
            'smovee',
            'database_connection_status',
            'Database connection status (1=connected, 0=disconnected)'
        );

        try {
            DB::connection()->getPdo();
            $dbStatus->set(1);
        } catch (\Exception $e) {
            $dbStatus->set(0);
        }

        // Cache status
        $cacheStatus = $this->registry->getOrRegisterGauge(
            'smovee',
            'cache_connection_status',
            'Cache connection status (1=connected, 0=disconnected)'
        );

        try {
            Cache::store('redis')->get('test');
            $cacheStatus->set(1);
        } catch (\Exception $e) {
            $cacheStatus->set(0);
        }
    }

    /**
     * Register business metrics
     */
    private function registerBusinessMetrics(): void
    {
        // User metrics
        $this->registerUserMetrics();

        // Content metrics
        $this->registerContentMetrics();

        // Engagement metrics
        $this->registerEngagementMetrics();

        // Competition metrics
        $this->registerCompetitionMetrics();
    }

    /**
     * Register user-related metrics
     */
    private function registerUserMetrics(): void
    {
        // Total users
        $totalUsers = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_total',
            'Total number of users',
            ['role']
        );

        $userCounts = User::selectRaw('role, COUNT(*) as count')
            ->groupBy('role')
            ->pluck('count', 'role');

        foreach ($userCounts as $role => $count) {
            $totalUsers->set($count, [$role]);
        }

        // Active users (last 30 days)
        $activeUsers = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_active_30d',
            'Active users in the last 30 days'
        );

        $activeCount = User::whereHas('plays', function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })->orWhereHas('likes', function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })->count();

        $activeUsers->set($activeCount);

        // New registrations (last 24h, 7d, 30d)
        $newUsers24h = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_new_24h',
            'New user registrations in the last 24 hours'
        );
        $newUsers24h->set(User::where('created_at', '>=', now()->subDay())->count());

        $newUsers7d = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_new_7d',
            'New user registrations in the last 7 days'
        );
        $newUsers7d->set(User::where('created_at', '>=', now()->subDays(7))->count());

        $newUsers30d = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_new_30d',
            'New user registrations in the last 30 days'
        );
        $newUsers30d->set(User::where('created_at', '>=', now()->subDays(30))->count());
    }

    /**
     * Register content-related metrics
     */
    private function registerContentMetrics(): void
    {
        // Total articles (tracks)
        $totalArticles = $this->registry->getOrRegisterGauge(
            'smovee',
            'articles_total',
            'Total number of articles/tracks'
        );
        $totalArticles->set(Article::count());

        // Total channels (albums)
        $totalChannels = $this->registry->getOrRegisterGauge(
            'smovee',
            'channels_total',
            'Total number of channels/albums'
        );
        $totalChannels->set(Channel::count());

        // Total playlists
        $totalPlaylists = $this->registry->getOrRegisterGauge(
            'smovee',
            'playlists_total',
            'Total number of playlists'
        );
        $totalPlaylists->set(Playlist::count());

        // Content by genre
        $contentByGenre = $this->registry->getOrRegisterGauge(
            'smovee',
            'articles_by_genre',
            'Number of articles by genre',
            ['genre']
        );

        $genreCounts = Article::join('genres', 'articles.genre_id', '=', 'genres.id')
            ->selectRaw('genres.name as genre_name, COUNT(*) as count')
            ->groupBy('genres.name')
            ->pluck('count', 'genre_name');

        foreach ($genreCounts as $genre => $count) {
            $contentByGenre->set($count, [$genre]);
        }

        // New content (last 24h, 7d, 30d)
        $newArticles24h = $this->registry->getOrRegisterGauge(
            'smovee',
            'articles_new_24h',
            'New articles in the last 24 hours'
        );
        $newArticles24h->set(Article::where('created_at', '>=', now()->subDay())->count());

        $newChannels24h = $this->registry->getOrRegisterGauge(
            'smovee',
            'channels_new_24h',
            'New channels in the last 24 hours'
        );
        $newChannels24h->set(Channel::where('created_at', '>=', now()->subDay())->count());
    }

    /**
     * Register engagement metrics
     */
    private function registerEngagementMetrics(): void
    {
        // Total plays
        $totalPlays = $this->registry->getOrRegisterGauge(
            'smovee',
            'plays_total',
            'Total number of plays'
        );
        $totalPlays->set(Play::count());

        // Plays in different time periods
        $plays24h = $this->registry->getOrRegisterGauge(
            'smovee',
            'plays_24h',
            'Plays in the last 24 hours'
        );
        $plays24h->set(Play::where('created_at', '>=', now()->subDay())->count());

        $plays7d = $this->registry->getOrRegisterGauge(
            'smovee',
            'plays_7d',
            'Plays in the last 7 days'
        );
        $plays7d->set(Play::where('created_at', '>=', now()->subDays(7))->count());

        // Total likes
        $totalLikes = $this->registry->getOrRegisterGauge(
            'smovee',
            'likes_total',
            'Total number of likes'
        );
        $totalLikes->set(Like::count());

        // Total comments
        $totalComments = $this->registry->getOrRegisterGauge(
            'smovee',
            'comments_total',
            'Total number of comments'
        );
        $totalComments->set(Comment::count());

        // Total follows
        $totalFollows = $this->registry->getOrRegisterGauge(
            'smovee',
            'follows_total',
            'Total number of follows'
        );
        $totalFollows->set(Following::count());

        // Unique listeners (last 30 days)
        $uniqueListeners = $this->registry->getOrRegisterGauge(
            'smovee',
            'unique_listeners_30d',
            'Unique listeners in the last 30 days'
        );
        $uniqueListeners->set(
            Play::where('created_at', '>=', now()->subDays(30))
                ->distinct('player_id')
                ->whereNotNull('player_id')
                ->count('player_id')
        );
    }

    /**
     * Register competition metrics
     */
    private function registerCompetitionMetrics(): void
    {
        // Total competitions
        $totalCompetitions = $this->registry->getOrRegisterGauge(
            'smovee',
            'competitions_total',
            'Total number of competitions'
        );
        $totalCompetitions->set(Competition::count());

        // Active competitions
        $activeCompetitions = $this->registry->getOrRegisterGauge(
            'smovee',
            'competitions_active',
            'Number of active competitions'
        );
        $activeCompetitions->set(Competition::active()->count());

        // Competition entries
        $totalEntries = $this->registry->getOrRegisterGauge(
            'smovee',
            'competition_entries_total',
            'Total competition entries'
        );
        $totalEntries->set(CompetitionEntry::count());

        // Competition votes
        $totalVotes = $this->registry->getOrRegisterGauge(
            'smovee',
            'competition_votes_total',
            'Total competition votes'
        );
        $totalVotes->set(Vote::count());

        // Paid votes
        $paidVotes = $this->registry->getOrRegisterGauge(
            'smovee',
            'competition_votes_paid',
            'Total paid competition votes'
        );
        $paidVotes->set(Vote::where('paid', true)->count());

        // Votes in last 24h
        $votes24h = $this->registry->getOrRegisterGauge(
            'smovee',
            'competition_votes_24h',
            'Competition votes in the last 24 hours'
        );
        $votes24h->set(Vote::where('created_at', '>=', now()->subDay())->count());
    }

    /**
     * Register infrastructure and performance metrics
     */
    private function registerInfrastructureMetrics(): void
    {
        // Queue metrics
        $this->registerQueueMetrics();

        // Memory usage
        $memoryUsage = $this->registry->getOrRegisterGauge(
            'smovee',
            'memory_usage_bytes',
            'Current memory usage in bytes'
        );
        $memoryUsage->set(memory_get_usage(true));

        // Peak memory usage
        $peakMemory = $this->registry->getOrRegisterGauge(
            'smovee',
            'memory_peak_bytes',
            'Peak memory usage in bytes'
        );
        $peakMemory->set(memory_get_peak_usage(true));
    }

    /**
     * Register queue-related metrics
     */
    private function registerQueueMetrics(): void
    {
        // Pending jobs
        $pendingJobs = $this->registry->getOrRegisterGauge(
            'smovee',
            'queue_jobs_pending',
            'Number of pending jobs in queue',
            ['queue']
        );

        // Failed jobs
        $failedJobs = $this->registry->getOrRegisterGauge(
            'smovee',
            'queue_jobs_failed',
            'Number of failed jobs'
        );

        try {
            // Get queue sizes for different queues
            $queues = ['default', 'recommender'];
            foreach ($queues as $queue) {
                $size = Queue::size($queue);
                $pendingJobs->set($size, [$queue]);
            }

            // Get failed jobs count
            $failedCount = DB::table('failed_jobs')->count();
            $failedJobs->set($failedCount);
        } catch (\Exception $e) {
            // If we can't get queue metrics, set to 0
            foreach (['default', 'recommender'] as $queue) {
                $pendingJobs->set(0, [$queue]);
            }
            $failedJobs->set(0);
        }
    }

    /**
     * Register performance metrics
     */
    private function registerPerformanceMetrics(): void
    {
        // Database query count (from current request)
        $queryCount = $this->registry->getOrRegisterGauge(
            'smovee',
            'database_queries_count',
            'Number of database queries in current request'
        );
        $queryCount->set(count(DB::getQueryLog()));

        // Cache hit rate (if available)
        try {
            $cacheHits = Cache::get('metrics:cache_hits', 0);
            $cacheMisses = Cache::get('metrics:cache_misses', 0);
            $total = $cacheHits + $cacheMisses;

            $hitRate = $this->registry->getOrRegisterGauge(
                'smovee',
                'cache_hit_rate',
                'Cache hit rate percentage'
            );

            if ($total > 0) {
                $hitRate->set(($cacheHits / $total) * 100);
            } else {
                $hitRate->set(0);
            }
        } catch (\Exception $e) {
            // If cache metrics aren't available, skip
        }

        // Additional performance metrics
        $this->registerRecommendationMetrics();
        $this->registerAudioMetrics();
        $this->registerSearchMetrics();
    }

    /**
     * Register recommendation system metrics
     */
    private function registerRecommendationMetrics(): void
    {
        // Recommendation generation metrics
        $recommendationJobs = $this->registry->getOrRegisterGauge(
            'smovee',
            'recommendation_jobs_pending',
            'Number of pending recommendation jobs'
        );

        try {
            $pendingRecommendations = Queue::size('recommender');
            $recommendationJobs->set($pendingRecommendations);
        } catch (\Exception $e) {
            $recommendationJobs->set(0);
        }

        // User recommendation coverage
        $usersWithRecommendations = $this->registry->getOrRegisterGauge(
            'smovee',
            'users_with_recommendations',
            'Number of users with generated recommendations'
        );

        try {
            $count = Playlist::recommendations()
                ->distinct('user_id')
                ->count('user_id');
            $usersWithRecommendations->set($count);
        } catch (\Exception $e) {
            $usersWithRecommendations->set(0);
        }
    }

    /**
     * Register audio processing metrics
     */
    private function registerAudioMetrics(): void
    {
        // Audio files with embeddings
        $audioWithEmbeddings = $this->registry->getOrRegisterGauge(
            'smovee',
            'audio_files_with_embeddings',
            'Number of audio files with generated embeddings'
        );

        try {
            $count = ArticleAudioEmbedding::count();
            $audioWithEmbeddings->set($count);
        } catch (\Exception $e) {
            $audioWithEmbeddings->set(0);
        }

        // Audio files with HLS conversion
        $audioWithHls = $this->registry->getOrRegisterGauge(
            'smovee',
            'audio_files_with_hls',
            'Number of audio files with HLS conversion'
        );

        try {
            $count = Article::whereHas('audio', function ($query) {
                $query->whereNotNull('hls_path');
            })->count();
            $audioWithHls->set($count);
        } catch (\Exception $e) {
            $audioWithHls->set(0);
        }
    }

    /**
     * Register search engine metrics
     */
    private function registerSearchMetrics(): void
    {
        // Search index status
        $searchIndexStatus = $this->registry->getOrRegisterGauge(
            'smovee',
            'search_index_status',
            'Search engine index status (1=healthy, 0=unhealthy)'
        );

        try {
            // Try to perform a simple search to check if Meilisearch is working
            Article::search('test')->take(1)->get();
            $searchIndexStatus->set(1);
        } catch (\Exception $e) {
            $searchIndexStatus->set(0);
        }

        // Indexed documents count
        $indexedArticles = $this->registry->getOrRegisterGauge(
            'smovee',
            'search_indexed_articles',
            'Number of articles in search index'
        );

        $indexedUsers = $this->registry->getOrRegisterGauge(
            'smovee',
            'search_indexed_users',
            'Number of users in search index'
        );

        $indexedChannels = $this->registry->getOrRegisterGauge(
            'smovee',
            'search_indexed_channels',
            'Number of channels in search index'
        );

        try {
            // These would need to be implemented based on your Meilisearch setup
            // For now, we'll use the database counts as approximations
            $indexedArticles->set(Article::count());
            $indexedUsers->set(User::count());
            $indexedChannels->set(Channel::count());
        } catch (\Exception $e) {
            $indexedArticles->set(0);
            $indexedUsers->set(0);
            $indexedChannels->set(0);
        }
    }

    /**
     * Register threshold metrics for alerting
     */
    private function registerThresholdMetrics(): void
    {
        $thresholds = config('metrics.thresholds', []);

        // Max failed jobs threshold
        if (isset($thresholds['max_failed_jobs'])) {
            $maxFailedJobs = $this->registry->getOrRegisterGauge(
                'smovee',
                'threshold_max_failed_jobs',
                'Maximum allowed failed jobs threshold'
            );
            $maxFailedJobs->set($thresholds['max_failed_jobs']);
        }

        // Max queue size threshold
        if (isset($thresholds['max_queue_size'])) {
            $maxQueueSize = $this->registry->getOrRegisterGauge(
                'smovee',
                'threshold_max_queue_size',
                'Maximum allowed queue size threshold'
            );
            $maxQueueSize->set($thresholds['max_queue_size']);
        }

        // Min cache hit rate threshold
        if (isset($thresholds['min_cache_hit_rate'])) {
            $minCacheHitRate = $this->registry->getOrRegisterGauge(
                'smovee',
                'threshold_min_cache_hit_rate',
                'Minimum required cache hit rate threshold'
            );
            $minCacheHitRate->set($thresholds['min_cache_hit_rate']);
        }

        // Max response time threshold
        if (isset($thresholds['max_response_time'])) {
            $maxResponseTime = $this->registry->getOrRegisterGauge(
                'smovee',
                'threshold_max_response_time',
                'Maximum allowed response time threshold in milliseconds'
            );
            $maxResponseTime->set($thresholds['max_response_time']);
        }

        // Min disk space threshold
        if (isset($thresholds['min_disk_space_gb'])) {
            $minDiskSpace = $this->registry->getOrRegisterGauge(
                'smovee',
                'threshold_min_disk_space_gb',
                'Minimum required disk space threshold in GB'
            );
            $minDiskSpace->set($thresholds['min_disk_space_gb']);
        }
    }
}
