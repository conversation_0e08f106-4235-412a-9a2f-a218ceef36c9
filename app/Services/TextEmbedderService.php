<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class TextEmbedderService extends BaseService
{
    public function __construct(private string $text)
    {
        parent::__construct();
    }

    public function handle()
    {
        $response = Http::timeout(100)
            ->withHeaders(['Content-Type' => 'application/json'])
            ->post(
                url: $this->url(),
                data: ['text' => $this->text]
            );

        return $response->json();
    }

    protected function url(): string
    {
        return (string) config('services.image_generator.url');
    }
}
