<?php

namespace App\Services;

use App\Events\RecommendationPlaylistCreated;
use App\Models\Article;
use App\Models\Embedding\ArticleAudioEmbedding;
use App\Models\Genre; // Added
use App\Models\Morph\Like;
use App\Models\Play;
use App\Models\Playlist\Playlist;
use App\Models\Playlist\PlaylistItem;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Pgvector\Laravel\Distance;

class RecommendationService
{
    private array $config;

    private User $user;

    private Carbon $generationTime;

    private Collection $excludedArticleIds; // IDs to exclude from recommendations

    private array $strategyResults = []; // Raw candidates: ['type'=>..., 'article_id'=>..., 'score'=>..., 'context'=>...]

    public function __construct()
    {
        $this->config = config('recommendations');
        $this->generationTime = Carbon::now();
    }

    /**
     * Generate playlist recommendations for a specific user.
     */
    public function generateForUser(User $user): void
    {
        $this->user = $user;
        $this->strategyResults = []; // Reset

        Log::info("Starting playlist recommendation generation for User ID: {$this->user->id}");

        $this->fetchExclusionData();
        $this->runStrategies();
        $allConceptualPlaylists = $this->buildConceptualPlaylists();
        $selectedPlaylists = $this->selectTopPlaylists($allConceptualPlaylists);

        $this->storePlaylists($selectedPlaylists);

        event(new RecommendationPlaylistCreated($this->user));
        Log::info("Finished playlist recommendation generation for User ID: {$this->user->id}. Stored ".count($selectedPlaylists).' playlists.');
    }

    /**
     * Fetch history, likes, and items in RECENT system playlists for exclusion.
     */
    private function fetchExclusionData(): void
    {
        $historyLookback = max(
            $this->config['recent_plays_days'],
            $this->config['popularity_days'],
            $this->config['following_days'],
            $this->config['playlist_freshness_days']
        ) + 1;

        $historyIds = Play::where('player_id', $this->user->id)
            ->where('created_at', '>=', Carbon::now()->subDays($historyLookback))
            ->distinct()
            ->pluck('article_id');

        $likeIds = Like::where('user_id', $this->user->id)
            ->where('likeable_type', Article::class)
            ->distinct()
            ->pluck('likeable_id');

        // Get items from system playlists generated recently
        $recentPlaylistIds = Playlist::where('user_id', $this->user->id)
            ->where('is_system_generated', true)
            ->where('generated_at', '>=', Carbon::now()->subDays($this->config['playlist_freshness_days']))
            ->pluck('id');

        $recentRecommendationIds = PlaylistItem::whereIn('playlist_id', $recentPlaylistIds)
            ->distinct()
            ->pluck('article_id');

        $this->excludedArticleIds = $historyIds
            ->merge($likeIds)
            ->merge($recentRecommendationIds)
            ->unique();

        Log::debug('Excluding '.$this->excludedArticleIds->count()." items for User ID: {$this->user->id}");
    }

    /**
     * Run all enabled recommendation strategies.
     */
    private function runStrategies(): void
    {
        $strategies = $this->config['enabled_strategies'];

        // Run in a potentially logical order, though buildConceptualPlaylists controls final assembly
        if (in_array('following', $strategies)) {
            $this->recommendBasedOnFollowings();
        }
        if (in_array('recent_play_similarity', $strategies)) {
            $this->recommendBasedOnSimilarity('play');
        }
        if (in_array('like_similarity', $strategies)) {
            $this->recommendBasedOnSimilarity('like');
        }
        if (in_array('popular_genre', $strategies)) { // <-- Added
            $this->recommendBasedOnGenrePopularity();
        }
        if (in_array('popular_global', $strategies)) {
            $this->recommendBasedOnPopularity(null); // Global
        }
        if (in_array('collaborative', $strategies)) {
            $this->recommendBasedOnSimilarUsers();
        }
    }

    // --- Individual Strategy Implementations ---

    private function recommendBasedOnFollowings(): void
    {
        $followedUserIds = $this->user->followings()->pluck('user_id');
        if ($followedUserIds->isEmpty()) {
            return;
        }

        $newReleases = Article::with('user:id,name') // Eager load minimal user info
            ->whereIn('user_id', $followedUserIds)
            ->where('created_at', '>=', Carbon::now()->subDays($this->config['following_days']))
            ->whereNotIn('id', $this->excludedArticleIds)
            ->orderByDesc('created_at')
            ->limit($this->config['strategy_candidate_limit'])
            ->get();

        $weight = $this->config['weights']['following'] ?? 1.0;
        foreach ($newReleases as $article) {
            $this->addStrategyResult('following', $article->id, $weight, ['seed_artist_name' => $article->user?->name]);
        }
    }

    private function recommendBasedOnSimilarity(string $type): void
    {
        $seedArticleIds = collect();

        if ($type === 'play') {
            $seedArticleIds = Play::where('player_id', $this->user->id)
                ->where('created_at', '>=', Carbon::now()->subDays($this->config['recent_plays_days']))
                ->select('article_id', 'created_at')
                ->orderByDesc('created_at')
                ->distinct()
                ->limit($this->config['max_seed_items_per_strategy'])
                ->pluck('article_id')
                ->shuffle();

        } elseif ($type === 'like') {
            $seedArticleIds = Like::where('user_id', $this->user->id)
                ->where('likeable_type', Article::class)
                ->select('likeable_id', 'created_at')
                ->orderByDesc('created_at')
                ->distinct()
                ->limit($this->config['max_seed_items_per_strategy'])
                ->pluck('likeable_id')
                ->shuffle();
        }
        if ($seedArticleIds->isEmpty()) {
            return;
        }

        $seedArticles = ArticleAudioEmbedding::with('article:id,name')
            ->whereIn('article_id', $seedArticleIds)
            ->whereNotNull('embedding')
            ->get()
            ->shuffle();

        if ($seedArticles->isEmpty()) {
            return;
        }

        $strategyType = ($type === 'play' ? 'recent_play_similarity' : 'like_similarity');
        $weight = $this->config['weights'][$strategyType] ?? 1.0;
        $limitPerSeed = $this->config['neighbors_per_seed'];
        $foundNeighborIds = collect();

        foreach ($seedArticles as $seed) {
            if (! $seed->embedding || ! $seed->article) {
                continue;
            }
            try {
                // Find neighbors using Cosine Distance (<=>)
                $neighbors = ArticleAudioEmbedding::query()
                    ->nearestNeighbors('embedding', $seed->embedding->toArray(), Distance::Cosine)
                    ->where('article_id', '!=', $seed->article_id)
                    ->whereNotNull('embedding')
                    ->whereNotIn('article_id', $this->excludedArticleIds->merge($foundNeighborIds))
                    ->limit($limitPerSeed)
                    ->pluck('article_id');

                foreach ($neighbors as $neighborId) {
                    $this->addStrategyResult($strategyType, $neighborId, $weight, [
                        'seed_article_id' => $seed->article_id,
                        'seed_article_name' => $seed->article->name,
                    ]);
                    $foundNeighborIds->push($neighborId);
                }
            } catch (\Exception $e) {
                Log::error("Similarity search failed for seed {$seed->article_id}, User {$this->user->id}: ".$e->getMessage());
            }
        }
    }

    /**
     * Recommend based on genre popularity derived from user's listening history.
     */
    private function recommendBasedOnGenrePopularity(): void
    {
        // 1. Find user's top genres based on recent plays
        $genrePlayCounts = Play::join('articles', 'plays.article_id', '=', 'articles.id')
            ->join('genres', 'articles.genre_id', '=', 'genres.id')
            ->where('plays.player_id', $this->user->id)
            ->where('plays.created_at', '>=', Carbon::now()->subDays($this->config['recent_plays_days']))
            ->select('genres.id', 'genres.name', DB::raw('COUNT(plays.id) as play_count'))
            ->groupBy('genres.id', 'genres.name')
            ->having(DB::raw('COUNT(plays.id)'), '>=', $this->config['min_plays_for_top_genre'])
            ->orderByDesc('play_count')
            ->limit($this->config['max_user_top_genres']) // Limit to top N genres
            ->get();

        if ($genrePlayCounts->isEmpty()) {
            Log::debug("User {$this->user->id} has no top genres based on recent plays.");

            return;
        }

        Log::debug("User {$this->user->id} top genres: ".$genrePlayCounts->pluck('name')->implode(', '));

        // 2. For each top genre, find popular articles within that genre (excluding user's interacted items)
        $weight = $this->config['weights']['popular_genre'] ?? 0.8;
        $minGenrePlays = $this->config['min_popular_plays_genre'];
        $limit = $this->config['strategy_candidate_limit']; // Limit candidates per genre

        foreach ($genrePlayCounts as $topGenre) {
            // Find popular articles IN THIS GENRE across ALL users (within popularity days)
            $popularInGenre = Article::join('plays', 'articles.id', '=', 'plays.article_id')
                ->select('articles.id', DB::raw('COUNT(plays.id) as total_play_count'))
                ->where('articles.genre_id', $topGenre->id)
                ->where('plays.created_at', '>=', Carbon::now()->subDays($this->config['popularity_days']))
                ->groupBy('articles.id')
                ->having(DB::raw('COUNT(plays.id)'), '>=', $minGenrePlays)
                ->orderByDesc('total_play_count')
                ->whereNotIn('articles.id', $this->excludedArticleIds) // Exclude already interacted/recommended
                ->limit($limit)
                ->pluck('articles.id');

            foreach ($popularInGenre as $articleId) {
                $this->addStrategyResult('popular_genre', $articleId, $weight, [
                    'genre_id' => $topGenre->id,
                    'genre_name' => $topGenre->name,
                ]);
            }
        }
    }

    private function recommendBasedOnPopularity(?int $genreId = null): void // Only used for GLOBAL now
    {
        if ($genreId !== null) {
            return;
        }

        $minPlays = $this->config['min_popular_plays'];
        $limit = $this->config['strategy_candidate_limit'];
        $weight = $this->config['weights']['popular_global'] ?? 0.5;

        $excludedArticleIds = collect($this->excludedArticleIds)
            ->shuffle()
            ->take($this->excludedArticleIds->count() / 2); // Randomly exclude half of the already excluded items

        $popularArticles = Play::select(['article_id', DB::raw('COUNT(*) as play_count')])
            ->where('created_at', '>=', Carbon::now()->subDays($this->config['popularity_days']))
            ->groupBy('article_id')
            ->having(DB::raw('COUNT(*)'), '>=', $minPlays)
            ->orderByDesc('play_count')
            ->whereNotIn('article_id', $excludedArticleIds)
            ->whereHas('article')
            ->limit($limit)
            ->pluck('article_id');

        foreach ($popularArticles as $articleId) {
            $this->addStrategyResult('popular_global', $articleId, $weight);
        }
    }

    /**
     * Fetches the top N most listened items for the user within the configured timeframe.
     * Does NOT use the general exclusion list.
     */
    private function getTopListenedItems(): Collection
    {
        $days = $this->config['top_listened_playlist_days'] ?? 90;
        $limit = $this->config['top_listened_playlist_size'] ?? 20;

        return Play::select(['article_id', DB::raw('COUNT(id) as play_count')])
            ->where('player_id', $this->user->id)
            ->where('created_at', '>=', Carbon::now()->subDays($days))
            ->groupBy('article_id')
            ->whereHas('article')
            ->orderByDesc('play_count')
            ->limit($limit)
            ->pluck('article_id'); // We only need the IDs for the playlist items
    }

    /**
     * Adds a raw result candidate. Handles potential duplicates from same strategy.
     */
    private function addStrategyResult(string $type, string $articleId, float $score, array $context = []): void
    {
        // Prevent adding exact duplicates within a single run if strategy accidentally finds same item twice
        $uniqueKey = $type.'_'.$articleId.'_'.($context['seed_article_id'] ?? ($context['genre_id'] ?? '')); // Make key more specific

        if (! isset($this->strategyResults[$uniqueKey])) {
            $this->strategyResults[$uniqueKey] = [
                'type' => $type,
                'article_id' => $articleId,
                'score' => $score,
                'context' => $context,
            ];
        } else {
            // If found again via different seed/context within same strategy, potentially update score
            $this->strategyResults[$uniqueKey]['score'] = max($this->strategyResults[$uniqueKey]['score'], $score);
        }
    }

    private function recommendBasedOnSimilarUsers(): void
    {
        // Skip if not enough user data
        if (! $this->hasSufficientHistory()) {
            return;
        }

        $similarUsers = $this->findSimilarUsers();
        if ($similarUsers->isEmpty()) {
            return;
        }

        $recommendations = $this->getCollaborativeRecommendations($similarUsers);
        $this->processCollaborativeResults($recommendations);
    }

    private function hasSufficientHistory(): bool
    {
        $minInteractions = $this->config['collaborative']['min_user_interactions'] ?? 20;

        // Count plays
        $playCount = Play::where('player_id', $this->user->id)->count();

        // Count likes on articles
        $likeCount = Like::where('user_id', $this->user->id)
            ->where('likeable_type', Article::class)
            ->count();

        // Total interactions
        $interactionCount = $playCount + $likeCount;

        return $interactionCount >= $minInteractions;
    }

    private function findSimilarUsers(): Collection
    {
        // Get user's interacted article IDs (plays + likes)
        $userArticleIds = $this->getUserInteractionSignature();

        // Find users with overlapping interactions
        return DB::table('plays')
            ->select('player_id as user_id', DB::raw('COUNT(DISTINCT article_id) as score'))
            ->whereIn('article_id', $userArticleIds)
            ->where('player_id', '!=', $this->user->id)
            ->groupBy('player_id')
            ->unionAll(
                DB::table('likes')
                    ->select('user_id', DB::raw('COUNT(DISTINCT likeable_id) as score'))
                    ->where('likeable_type', Article::class)
                    ->whereIn('likeable_id', $userArticleIds)
                    ->where('user_id', '!=', $this->user->id)
                    ->groupBy('user_id')
            )
            ->get()
            ->groupBy('user_id')
            ->map(fn ($scores) => $scores->sum('score'))
            ->sortDesc()
            ->take($this->config['collaborative']['max_similar_users'] ?? 50)
            ->filter(fn ($score) => $score >= ($this->config['collaborative']['min_similarity_score'] ?? 3))
            ->keys();
    }

    private function getUserInteractionSignature(): Collection
    {
        return Play::where('player_id', $this->user->id)
            ->pluck('article_id')
            ->merge(
                Like::where('user_id', $this->user->id)
                    ->where('likeable_type', Article::class)
                    ->pluck('likeable_id')
            )
            ->unique();
    }

    private function getCollaborativeRecommendations(Collection $similarUserIds): Collection
    {
        // Get popular articles from similar users that target user hasn't interacted with
        return DB::table('articles')
            ->select('articles.id', DB::raw('COUNT(DISTINCT interactions.user_id) as popularity'))
            ->joinSub(
                DB::table('plays')
                    ->select('article_id', 'player_id as user_id')
                    ->whereIn('player_id', $similarUserIds)
                    ->unionAll(
                        DB::table('likes')
                            ->select('likeable_id as article_id', 'user_id')
                            ->where('likeable_type', Article::class)
                            ->whereIn('user_id', $similarUserIds)
                    ),
                'interactions',
                function ($join) {
                    $join->on('articles.id', '=', 'interactions.article_id');
                }
            )
            ->where('articles.id', 'interactions.article_id')
            ->whereNotIn('articles.id', $this->excludedArticleIds)
            ->groupBy('articles.id')
            ->orderByDesc('popularity')
            ->limit($this->config['collaborative']['recommendation_limit'] ?? 50)
            ->pluck('id');
    }

    private function processCollaborativeResults(Collection $recommendations): void
    {
        $weight = $this->config['weights']['collaborative'] ?? 0.7;

        foreach ($recommendations as $articleId) {
            $this->addStrategyResult(
                'collaborative',
                $articleId,
                $weight,
                ['source' => 'similar_users']
            );
        }
    }
    // --- Playlist Building Logic ---

    /**
     * Assembles conceptual playlist data structure from strategy results.
     */
    private function buildConceptualPlaylists(): array
    {
        $conceptualPlaylists = [];
        $assignedArticleIds = collect(); // Track items assigned to *any* playlist in this run

        // --- 0. Generate Top Listened Playlist (Highest Priority)
        if ($this->config['top_listened_playlist_enabled'] ?? false) {
            $topListenedItems = $this->getTopListenedItems();
            $minItemsRequired = $this->config['top_listened_playlist_min_items'] ?? 5;

            if ($topListenedItems->count() >= $minItemsRequired) {
                $playlistScore = $this->config['weights']['top_listened'] ?? 10.0; // Use high score from config
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle('top_listened'),
                    'items' => $topListenedItems->values()->all(),
                    'type' => 'top_listened',
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => 'top_listened',
                        'playlist_source' => 'user_history',
                    ],
                ];
                // IMPORTANT: Add these items to assigned so they aren't reused by lower priority strategies
                $assignedArticleIds = $assignedArticleIds->merge($topListenedItems);
                Log::debug("Generated 'Top Listened' playlist for User ID: {$this->user->id} with ".$topListenedItems->count().' items.');
            } else {
                Log::debug("Skipping 'Top Listened' playlist for User ID: {$this->user->id} - not enough items (found ".$topListenedItems->count().', required '.$minItemsRequired.').');
            }
        }

        // --- Playlist Creation Order (From Strategies - Lower Priority) ---

        // 1. Following ("Release Radar")
        // IMPORTANT: Filter against assignedArticleIds which now includes top listened tracks
        $followingItems = collect($this->strategyResults)->where('type', 'following')->sortByDesc('score');
        if ($followingItems->isNotEmpty()) {
            $items = $followingItems->pluck('article_id')->unique()->diff($assignedArticleIds)->take($this->config['items_per_playlist']);
            // Add diversity enforcement here

            if ($items->isNotEmpty()) {
                $playlistScore = $this->calculatePlaylistScore($items, 'following', $followingItems);
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle('following'),
                    'items' => $items->values()->all(),
                    'type' => 'following',
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => 'following',
                        'playlist_source' => 'following',
                    ],
                ];
                $assignedArticleIds = $assignedArticleIds->merge($items);
            }
        }

        // 2. Similarity Mixes (Likes & Plays)
        // IMPORTANT: Filter against assignedArticleIds
        $similarityItemsGrouped = collect($this->strategyResults)
            ->whereIn('type', ['recent_play_similarity', 'like_similarity'])
            ->groupBy('context.seed_article_id');

        foreach ($similarityItemsGrouped as $seedId => $itemsFromSeed) {
            if (empty($seedId)) {
                continue;
            }

            $items = $itemsFromSeed->sortByDesc('score')->pluck('article_id')->unique()->diff($assignedArticleIds)->take($this->config['items_per_playlist']);
            // Add diversity enforcement here

            if ($items->count() > 1) { // Require a few items
                $firstItemContext = $itemsFromSeed->first()['context'] ?? [];
                $type = $itemsFromSeed->first()['type'];
                $playlistScore = $this->calculatePlaylistScore($items, $type, $itemsFromSeed);
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle($type, $firstItemContext),
                    'items' => $items->values()->all(),
                    'type' => $type,
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => $type,
                        'playlist_source' => 'similarity',
                        'seed_article_id' => $seedId,
                        'seed_article_name' => $firstItemContext['seed_article_name'] ?? null,
                    ],
                ];
                $assignedArticleIds = $assignedArticleIds->merge($items);
            }
        }

        // 3. Genre Mixes
        // IMPORTANT: Filter against assignedArticleIds
        $genreItemsGrouped = collect($this->strategyResults)
            ->where('type', 'popular_genre')
            ->groupBy('context.genre_id');

        foreach ($genreItemsGrouped as $genreId => $itemsInGenre) {
            if (empty($genreId)) {
                continue;
            }

            $items = $itemsInGenre->sortByDesc('score')->pluck('article_id')->unique()->diff($assignedArticleIds)->take($this->config['items_per_playlist']);
            // Add diversity enforcement here

            if ($items->isNotEmpty()) {
                $firstItemContext = $itemsInGenre->first()['context'] ?? [];
                $playlistScore = $this->calculatePlaylistScore($items, 'popular_genre', $itemsInGenre);
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle('popular_genre', $firstItemContext),
                    'items' => $items->values()->all(),
                    'type' => 'popular_genre',
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => 'popular_genre',
                        'playlist_source' => 'genre',
                        'genre_id' => $genreId,
                        'genre_name' => $firstItemContext['genre_name'] ?? null,
                    ],
                ];
                $assignedArticleIds = $assignedArticleIds->merge($items);
            }
        }

        // 4. Global Popular ("Trending Now") - Fallback/Filler
        // IMPORTANT: Filter against assignedArticleIds
        $popularItems = collect($this->strategyResults)->where('type', 'popular_global')->sortByDesc('score');
        if ($popularItems->isNotEmpty()) {
            // Note: The strategy itself already excluded some items, this diff removes items used in higher-priority playlists *this run*
            $items = $popularItems->pluck('article_id')->unique()->diff($assignedArticleIds)->take($this->config['items_per_playlist']);
            // Add diversity enforcement here

            if ($items->isNotEmpty()) {
                $playlistScore = $this->calculatePlaylistScore($items, 'popular_global', $popularItems);
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle('popular_global'),
                    'items' => $items->values()->all(),
                    'type' => 'popular_global',
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => 'popular_global',
                        'playlist_source' => 'global',
                    ],
                ];
                $assignedArticleIds = $assignedArticleIds->merge($items); // Although likely last, good practice
            }
        }

        // 5. Collaborative Filtering Playlist ("Fans Also Like")
        $collaborativeItems = collect($this->strategyResults)
            ->where('type', 'collaborative')
            ->sortByDesc('score');

        if ($collaborativeItems->isNotEmpty()) {
            $items = $collaborativeItems->pluck('article_id')
                ->unique()
                ->diff($assignedArticleIds)
                ->take($this->config['items_per_playlist']);

            if ($items->isNotEmpty()) {
                $playlistScore = $this->calculatePlaylistScore($items, 'collaborative', $collaborativeItems);
                $conceptualPlaylists[] = [
                    'title' => $this->generatePlaylistTitle('collaborative'),
                    'items' => $items->values()->all(),
                    'type' => 'collaborative',
                    'score' => $playlistScore,
                    'context' => [
                        'playlist_type' => 'collaborative',
                        'playlist_source' => 'collaborative',
                    ],
                ];
                $assignedArticleIds = $assignedArticleIds->merge($items);
            }
        }

        // No need to explicitly limit here, selectTopPlaylists will handle it
        return $conceptualPlaylists;
    }

    /**
     * Generates a user-facing playlist title based on type and context.
     */
    private function generatePlaylistTitle(string $type, array $context = []): string
    {
        switch ($type) {
            case 'following':
                return 'Your Release Radar';
            case 'recent_play_similarity':
                $seedName = Str::limit($context['seed_article_name'] ?? 'what you played', 30);

                return 'More like '.$seedName;
            case 'like_similarity':
                $seedName = Str::limit($context['seed_article_name'] ?? 'what you liked', 30);

                return 'Because you liked '.$seedName;
            case 'popular_genre':
                $genreName = $context['genre_name'] ?? 'Your Sound';

                return $genreName.' Mix';
            case 'popular_global':
                return 'Trending Now';
            case 'top_listened':
                return $this->config['top_listened_playlist_title'] ?? 'Your Top Tracks';
            case 'collaborative':
                return 'Fans Also Like';
            default:
                return 'Recommended For You';
        }
    }

    /**
     * Helper function to calculate a score for a conceptual playlist.
     * (Simple version using config weight + average item score).
     */
    private function calculatePlaylistScore(Collection $itemIds, string $type, ?Collection $sourceItems = null): float
    {
        // Base configuration weight for this playlist type
        $baseWeight = $this->config['weights'][$type] ?? 0.5;

        // Calculate average item score from source items
        $averageItemScore = $this->calculateAverageItemScore($itemIds, $sourceItems);

        // Calculate diversity score (0-1 scale)
        $diversityScore = $this->calculateDiversityScore($itemIds);

        // Calculate freshness score (0-1 scale)
        $freshnessScore = $this->calculateFreshnessScore($itemIds);

        // Weighted combination (adjust coefficients as needed)
        return ($baseWeight * 0.6)
            + ($averageItemScore * 0.3)
            + ($diversityScore * 0.1)
            + ($freshnessScore * 0.1);
    }

    private function calculateAverageItemScore(Collection $itemIds, ?Collection $sourceItems): float
    {
        if (! $sourceItems || $sourceItems->isEmpty()) {
            return 0.0;
        }

        $relevantScores = $sourceItems->filter(function ($item) use ($itemIds) {
            return $itemIds->contains($item['article_id']);
        })->pluck('score');

        return $relevantScores->avg() ?? 0.0;
    }

    private function calculateDiversityScore(Collection $itemIds): float
    {
        if ($itemIds->isEmpty()) {
            return 0.0;
        }

        // Get artist and genre distribution
        $articles = Article::whereIn('id', $itemIds)
            ->select('user_id as artist_id', 'genre_id')
            ->with(['user:id,name', 'genre:id,name'])
            ->get();

        // Count unique artists and genres
        $uniqueArtists = $articles->pluck('artist_id')->unique()->count();
        $uniqueGenres = $articles->pluck('genre_id')->unique()->count();
        $totalItems = $itemIds->count();

        // Prevent division by zero
        $artistDiversity = $totalItems > 0 ? $uniqueArtists / $totalItems : 0;
        $genreDiversity = $totalItems > 0 ? $uniqueGenres / $totalItems : 0;

        // Combine scores with slight preference for genre diversity
        return ($artistDiversity * 0.4) + ($genreDiversity * 0.6);
    }

    private function calculateFreshnessScore(Collection $itemIds): float
    {
        if ($itemIds->isEmpty()) {
            return 0.0;
        }

        $articles = Article::whereIn('id', $itemIds)
            ->select('id', 'created_at')
            ->get();

        $now = now();
        $maxAgeDays = 180; // Consider articles older than 6 months as "not fresh"

        $ageScores = $articles->map(function ($article) use ($now, $maxAgeDays) {
            $ageDays = $now->diffInDays($article->created_at);

            return max(0, 1 - ($ageDays / $maxAgeDays));
        });

        return $ageScores->avg();
    }

    /**
     * Selects the top N playlists from all candidates based on score.
     *
     * @param  array  $candidatePlaylists  Playlists generated by buildConceptualPlaylists
     * @return array The selected top playlists.
     */
    private function selectTopPlaylists(array $candidatePlaylists): array
    {
        if (empty($candidatePlaylists)) {
            return [];
        }

        // Sort candidates by their calculated 'score' descending
        usort($candidatePlaylists, function ($a, $b) {
            return ($b['score'] ?? 0.0) <=> ($a['score'] ?? 0.0);
        });

        // Take the top N based on config limit
        $limit = $this->config['target_playlist_count'] ?? 12;

        return array_slice($candidatePlaylists, 0, $limit);
    }

    /**
     * Generate a human-readable explanation for why this playlist was created
     *
     * This helps users understand why they're seeing these recommendations,
     * increasing transparency and trust in the recommendation system.
     *
     * @param  array  $playlistData  The playlist data
     * @return string A human-readable explanation
     */
    private function generatePlaylistExplanation(array $playlistData): string
    {
        $type = $playlistData['type'] ?? '';
        $context = $playlistData['context'] ?? [];

        switch ($type) {
            case 'following':
                return 'New releases from artists you follow. We update this playlist as artists you follow release new music.';

            case 'recent_play_similarity':
                $seedName = $context['seed_article_name'] ?? 'tracks you recently played';

                return "Songs that are musically similar to {$seedName}. Based on audio analysis and listening patterns.";

            case 'like_similarity':
                $seedName = $context['seed_article_name'] ?? 'tracks you liked';

                return "We found more songs like {$seedName} that we think you'll enjoy, based on your likes.";

            case 'popular_genre':
                $genreName = $context['genre_name'] ?? 'genres you listen to';

                return "Popular {$genreName} tracks that match your listening preferences. Updated regularly with fresh tracks.";

            case 'popular_global':
                return "Trending tracks across the platform right now. Discover what's popular with other listeners.";

            case 'top_listened':
                $days = $this->config['top_listened_playlist_days'] ?? 90;

                return "Your most played tracks from the past {$days} days. This playlist reflects your personal favorites.";

            case 'collaborative':
                return 'Tracks enjoyed by people with similar taste to yours. Based on listening patterns of users who enjoy the same music you do.';

            default:
                return 'Personalized recommendations based on your listening history and preferences.';
        }
    }

    /**
     * Store the generated conceptual playlists as actual Playlist records in the DB.
     */
    private function storePlaylists(array $conceptualPlaylists): void
    {
        if (empty($conceptualPlaylists)) {
            Log::info("No conceptual playlists to store for User ID: {$this->user->id}");

            return;
        }

        DB::transaction(function () use ($conceptualPlaylists) {
            // 1. Delete old SYSTEM-GENERATED playlists for this user
            $deletedCount = Playlist::where('user_id', $this->user->id)
                ->where('is_system_generated', true)
                // Delete playlists generated before this run started (or add a buffer)
                ->where('generated_at', '<', $this->generationTime)
                ->delete();

            // 2. Create new Playlist records and their items
            foreach ($conceptualPlaylists as $playlistData) {
                if (empty($playlistData['items'])) {
                    continue;
                }

                // Create the playlist header
                $playlist = Playlist::create([
                    'user_id' => $this->user->id,
                    'name' => $playlistData['title'],
                    'is_system_generated' => true,
                    'description' => $this->generatePlaylistExplanation($playlistData),
                    'generation_strategy' => $playlistData['type'],
                    'generated_at' => $this->generationTime,
                ]);

                // Prepare items for batch insertion
                $itemsToInsert = [];
                $rank = 1;
                foreach ($playlistData['items'] as $articleId) {
                    $itemsToInsert[] = [
                        'playlist_id' => $playlist->id,
                        'article_id' => $articleId,
                        'rank' => $rank++,
                        'created_at' => $this->generationTime,
                        'updated_at' => $this->generationTime,
                    ];
                }

                // Batch insert items for efficiency
                if (! empty($itemsToInsert)) {
                    foreach ($itemsToInsert as $item) {
                        PlaylistItem::create($item);
                    }
                }
            }
        });
        // Log success outside the transaction
        Log::info('Stored '.count($conceptualPlaylists)." new system playlists for User ID: {$this->user->id}");
    }
}
