<?php

namespace App\Services;

use App\Models\File\Audio;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Support\Facades\Http;

class AudioMetadataExtractorService extends BaseService
{
    public function __construct(private Audio $audio, private Filesystem $filesystem)
    {
        parent::__construct();
    }

    public function handle()
    {
        $response = Http::attach(
            name: 'audio',

            contents: $this->filesystem->readStream($this->audio->filename_download),

            filename: $this->audio->filename_disk,

            headers: ['Content-Type' => $this->audio->type]
        )->timeout(300)->post($this->url());

        return $response->json();
    }

    protected function url(): string
    {
        return (string) config('services.audio_metadata_extractor.url');
    }
}
