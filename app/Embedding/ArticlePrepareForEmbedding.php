<?php

namespace App\Embedding;

use App\Models\Article;
use App\Models\ArticleAudioMetadata;
use App\Models\Featuring;

class ArticlePrepareForEmbedding
{
    private ?ArticleAudioMetadata $metadata;

    public function __construct(
        private Article $article
    ) {
        $this->metadata = $article->audioMetadata;
    }

    public function hasAudioMetadata(): bool
    {
        return $this->metadata !== null;
    }

    public function getCategoricalMetadata(): string
    {
        $artist = $this->article->user;
        $numerical = (object) $this->numericalMetadata();

        // Include Featurings
        $featurings = $this->article->featurings->map(function (Featuring $featuring) {
            return $featuring->user->name;
        })->join(', ');

        $artistName = $this->fixArtists("{$artist->name}".(filled($featurings) ? ", {$featurings}" : ''));
        $genreName = $this->getGenre();

        return "{$artistName} - {$genreName} - {$numerical->plays} - {$numerical->likes} - {$numerical->year} - {$numerical->duration} - {$numerical->tempo} - {$numerical->bitrate}";
    }

    private function numericalMetadata()
    {
        $array = [
            'tempo' => 0,
            'bitrate' => 0,
            'plays' => $this->article->plays()->count(),
            'likes' => $this->article->likes()->count(),
            'year' => $this->getYear(),
            'duration' => $this->numberfy($this->getDuration()),
        ];

        if ($this->hasAudioMetadata()) {
            $array['tempo'] = $this->numberfy($this->metadata->tempo);
            $array['bitrate'] = $this->numberfy($this->metadata->bitrate);
            $array['duration'] = $this->numberfy($this->getDuration());
        }

        return $array;
    }

    private function fixArtists(string $str_list): string
    {
        $trimmed = trim($str_list, '[]');
        $artists = explode("', '", $trimmed);

        return implode(', ', $artists);
    }

    private function getGenre(): string
    {
        $metadata = $this->metadata?->json;
        $genre = $this->article->genre;

        if ($genre === null && $metadata !== null && $metadata['genre']) {
            return $metadata['genre'];
        }

        $genre = $genre ?? $this->article->channel?->genre;

        return $genre?->name ?? 'music';
    }

    private function getDuration()
    {
        $duration = $this->metadata?->duration ?? 0;

        if ($duration > 0) {
            return $duration;
        }

        $audio = $this->article->audio;

        if ($audio !== null && $audio->duration) {
            return $audio->duration ?? 0;
        }

        return 0;
    }

    private function getYear()
    {
        $year = $this->article->year;

        if ($year === null && $this->metadata?->year) {
            $year = $this->metadata->year;

        } elseif ($year === null) {
            $year = $this->article->created_at?->year;

        }

        return intval($year ?? 0);
    }

    private function numberfy($value): float|int
    {
        return $value * 1;
    }
}
