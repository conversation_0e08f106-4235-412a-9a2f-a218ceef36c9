<?php

namespace App\Repositories;

use App\Http\Resources\Article\ArticleCollection;
use App\Http\Resources\Likeable\ArticleLikeableCollection;
use App\Models\Article;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class ArticleRepository
{
    public const RELATIONS = [
        'channel',
        'channel' => ChannelRepository::RELATIONS,
        'user',
        'user' => UserRepository::ARTIST_RELATIONS,
        'likes',
        'image',
        'audio',
        'genre',
    ];

    public const DEFAULT_RELATIONS = [
        'image',
        'audio',
        'genre',
        'channel',
        'user',
    ];

    public function searchArticles(?string $query, int $limit = 5): ArticleCollection
    {
        return new ArticleCollection(
            Article::search($query)
                ->query(
                    fn (Builder $builder) => $builder->whereHas('audio')
                        ->with(ArticleRepository::DEFAULT_RELATIONS)
                        ->limit($limit)
                )
                ->get()
        );
    }

    public function getUserLikesArticles(User $user, int $limit = 36): ArticleLikeableCollection
    {
        $articles = $user->likes()
            ->with([
                'likeable' => function ($query) {
                    $query->with(ArticleRepository::DEFAULT_RELATIONS);
                },
            ])
            ->whereMorphedTo('likeable', Article::class)
            ->latest()
            ->paginate($limit);

        return new ArticleLikeableCollection($articles);
    }

    public function getUserFavoriteArticles(User $user, int $limit = 100)
    {
        if ($limit > 100) {
            $limit = 100;
        }

        return $this->getUserLikesArticles($user, $limit)->collection;
    }
}
