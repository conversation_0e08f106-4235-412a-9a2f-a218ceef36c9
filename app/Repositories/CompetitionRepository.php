<?php

namespace App\Repositories;

use App\Models\Competition;
use App\Models\CompetitionEntry;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CompetitionRepository
{
    /**
     * Get competitions that are accepting entries (includes both entry and voting phases).
     */
    public function getActiveCompetitions(Request $request)
    {
        return Competition::acceptingEntries()
            ->with('image')
            ->get();
    }

    /**
     * Get competition details.
     */
    public function getCompetitionDetails(Competition $competition, Request $request)
    {
        return $competition->load('image');
    }

    /**
     * Get the leaderboard for a competition.
     */
    public function getLeaderboard(Competition $competition, Request $request, int $limit = 10)
    {
        $page = $request->input('page', 1);
        $offset = ($page - 1) * $limit;

        // Get all approved artists in this competition
        $approvedArtistIds = CompetitionEntry::where('competition_id', $competition->id)
            ->where('status', 'approved')
            ->pluck('user_id');

        if ($approvedArtistIds->isEmpty()) {
            // Return empty result if no approved artists
            return [
                'data' => [],
                'meta' => [
                    'current_page' => (int) $page,
                    'last_page' => 1,
                    'per_page' => $limit,
                    'total' => 0,
                ],
            ];
        }

        // Get all artists with their vote counts and monthly listeners (including those with zero votes)
        $leaderboard = User::select([
            'users.*',
            DB::raw('COALESCE(vote_counts.votes_count, 0) as votes_count'),
            DB::raw('COALESCE(monthly_listeners.listeners_count, 0) as monthly_listeners_count'),
            DB::raw('(COALESCE(vote_counts.votes_count, 0) * 0.75 + COALESCE(monthly_listeners.listeners_count, 0) * 0.25) as composite_score'),
        ])
            ->whereIn('users.id', $approvedArtistIds)
            ->leftJoin(DB::raw("(
                SELECT artist_id, SUM(vote_count) as votes_count
                FROM votes
                WHERE competition_id = '{$competition->id}'
                GROUP BY artist_id
            ) as vote_counts"), 'users.id', '=', 'vote_counts.artist_id')
            ->leftJoin(DB::raw("(
                SELECT
                    articles.user_id,
                    COUNT(DISTINCT plays.player_id) as listeners_count
                FROM plays
                INNER JOIN articles ON plays.article_id = articles.id
                WHERE plays.player_id IS NOT NULL
                    AND plays.created_at >= DATE_TRUNC('month', CURRENT_DATE)
                    AND plays.created_at < DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'
                GROUP BY articles.user_id
            ) as monthly_listeners"), 'users.id', '=', 'monthly_listeners.user_id')
            ->orderByDesc('composite_score')
            ->orderBy('users.name') // Secondary sort by name for artists with same composite score
            ->with('image')
            ->offset($offset)
            ->limit($limit)
            ->get();

        // Get the total count for pagination (all approved artists)
        $totalCount = $approvedArtistIds->count();

        return [
            'data' => $leaderboard,
            'meta' => [
                'current_page' => (int) $page,
                'last_page' => ceil($totalCount / $limit),
                'per_page' => $limit,
                'total' => $totalCount,
            ],
        ];
    }

    /**
     * Get artists similar to the ones the user has voted for.
     */
    public function getSimilarToUserVotes(Request $request, int $limit = 5)
    {
        $user = $request->user();

        if (! $user) {
            return [];
        }

        // Get the genres of artists the user has voted for
        $votedArtistIds = $user->votesGiven()->pluck('artist_id');

        if ($votedArtistIds->isEmpty()) {
            return [];
        }

        // Find artists in the same genres that the user hasn't voted for yet
        $similarArtists = User::artist()
            ->whereHas('channels', function (Builder $query) use ($votedArtistIds) {
                $query->whereIn('genre_id', function ($subquery) use ($votedArtistIds) {
                    $subquery->select('genre_id')
                        ->from('channels')
                        ->whereIn('user_id', $votedArtistIds)
                        ->distinct();
                });
            })
            ->whereNotIn('id', $votedArtistIds)
            ->with('image')
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        return $similarArtists;
    }

    /**
     * Get recently voted artists.
     */
    public function getRecentlyVotedArtists(Request $request, int $limit = 5)
    {
        $user = $request->user();

        if (! $user) {
            return [];
        }

        // Get the artists the user has recently voted for
        $recentlyVotedArtists = User::select('users.*')
            ->join('votes', 'users.id', '=', 'votes.artist_id')
            ->where('votes.user_id', $user->id)
            ->orderByDesc('votes.created_at')
            ->with('image')
            ->limit($limit)
            ->get();

        return $recentlyVotedArtists;
    }

    /**
     * Check if there are any active competitions.
     */
    public function hasActiveCompetitions(): bool
    {
        return Competition::active()->exists();
    }
}
