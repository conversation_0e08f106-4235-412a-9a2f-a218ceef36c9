<?php

namespace App\Repositories;

use App\Http\Resources\Article\ArticleCollection;
use App\Http\Resources\Channel\ChannelCollection;
use App\Http\Resources\Genre\GenreCollection;
use App\Http\Resources\Genre\GenreResource;
use App\Models\Genre;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class GenreRepository
{
    public const RELATIONS = [
        'image',
    ];

    public function getGenreHasChild()
    {
        $genres = Genre::with(self::RELATIONS)
            ->where(function (Builder $builder) {
                $builder->whereHas('articles')
                    ->orWhereHas('channels');
            })
            ->paginate(30);

        return new GenreCollection($genres);
    }

    public function searchGenres(?string $query, int $limit = 5)
    {
        return new GenreCollection(
            Genre::search($query)
                ->query(
                    function (Builder $builder) use ($limit) {
                        $builder->where(
                            fn (Builder $builder) => $builder->whereHas('articles')
                                ->orWhereHas('channels')
                        )->limit($limit);
                    }

                )
                ->get()
        );
    }

    public function getGenreDetails(Genre $genre): GenreResource
    {
        return new GenreResource($genre);
    }

    public function getGenreChannels(Genre $genre, User $user): ChannelCollection
    {
        $channels = $genre->channels()
            ->with(ChannelRepository::RELATIONS)
            ->whereHas('articles')
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->paginate(18);

        return new ChannelCollection($channels);
    }

    public function getGenreArticles(Genre $genre, User $user): ArticleCollection
    {
        $articles = $genre->articles()
            ->with(ArticleRepository::RELATIONS)
            ->whereHas('audio')
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->paginate(18);

        return new ArticleCollection($articles);
    }

    public function getGenreRandomArticles(Genre $genre, User $user): ArticleCollection
    {
        $articles = $genre->articles()
            ->with(ArticleRepository::RELATIONS)
            ->whereHas('audio')
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->limit(100)
            ->get();

        return new ArticleCollection($articles);
    }

    public function findOrCreate(string $name)
    {
        $genre = Genre::where('name', 'like', $name)->first();

        if (blank($genre)) {
            $splittedName = explode(' ', $name);
            $genreName = $splittedName[0];

            $category = isset($splittedName[1]) && filled($splittedName[1]) ? $splittedName[1] : $genreName;

            $genre = Genre::firstOrCreate([
                'name' => $genreName,
                'category' => $category,
            ]);
        }

        return $genre;
    }
}
