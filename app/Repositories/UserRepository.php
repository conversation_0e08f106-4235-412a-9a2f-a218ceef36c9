<?php

namespace App\Repositories;

use App\Http\Resources\Article\ArticleResource;
use App\Http\Resources\Following\FollowingCollection;
use App\Http\Resources\User\ArtistCollection;
use App\Http\Resources\User\ArtistShowResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class UserRepository
{
    public const ARTIST_RELATIONS = ['image', 'followings'];

    public function getArtistDetails(User $artist): ArtistShowResource
    {
        // Load competition entries with their competitions
        $artist->load([
            'competitionEntries' => function ($query) {
                $query->where('status', 'approved')
                    ->with(['competition' => function ($competitionQuery) {
                        $competitionQuery->where('status', true)
                            ->where('end_date', '>=', now());
                    }]);
            },
        ]);

        return new ArtistShowResource($artist);
    }

    public function isArtistFollowedByUser(User $artist, User $user): bool
    {
        return $artist->followers()->where('follower_id', $user->id)->exists();
    }

    public function searchArtists(?string $query, int $limit = 5): ArtistCollection
    {
        return new ArtistCollection(
            User::search($query)
                ->query(
                    fn (Builder $builder) => $builder
                        ->whereIn('role', User::getAllArtistTypes())
                        ->where(function (Builder $builder) {
                            $builder
                                ->whereHas('articles')
                                ->orWhereHas('channels');
                        })
                        ->with(self::ARTIST_RELATIONS)
                        ->limit($limit)
                )
                ->get()
        );
    }

    public function getRandomArtists(User $user): ArtistCollection
    {
        $artists = User::artist()
            ->with(self::ARTIST_RELATIONS)
            ->where(function (Builder $builder) {
                $builder
                    ->whereHas('articles')
                    ->orWhereHas('channels');
            })
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->paginate(36);

        return new ArtistCollection($artists);
    }

    public function getUserFollowings(User $user): FollowingCollection
    {
        $artists = $user->followings()
            ->with([
                'user' => function ($builder) {
                    $builder->with(self::ARTIST_RELATIONS);
                },
            ])
            ->latest()
            ->paginate(36);

        return new FollowingCollection($artists);
    }

    public function getArtistTopChartArticles(User $artist, $limit = 5)
    {
        $articles = $artist->articles()
            ->select(['articles.*', DB::raw('COUNT(plays.id) as play_count')])
            ->leftJoin('plays', 'articles.id', '=', 'plays.article_id')
            ->groupBy('articles.id')
            ->orderByDesc('play_count')
            ->with(ArticleRepository::RELATIONS)
            ->limit($limit);

        return ArticleResource::collection($articles->get());
    }
}
