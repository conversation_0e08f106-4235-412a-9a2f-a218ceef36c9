<?php

namespace App\Repositories;

use App\Http\Resources\Channel\ChannelCollection;
use App\Http\Resources\Channel\ChannelShowResource;
use App\Http\Resources\Likeable\ChannelLikeableCollection;
use App\Models\Channel;
use App\Models\Following;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ChannelRepository
{
    public const RELATIONS = [
        'image',
        'user',
        'user' => UserRepository::ARTIST_RELATIONS,
        'likes',
    ];

    public function getChannelDetails(Channel $channel): ChannelShowResource
    {
        return new ChannelShowResource($channel);
    }

    public function getMoreChannelsFromArtist(Channel $channel): ChannelCollection
    {
        $channels = $channel->user->channels()
            ->with(self::RELATIONS)
            ->whereHas('articles')
            ->whereKeyNot($channel->id)
            ->latest()
            ->paginate(12);

        return new ChannelCollection($channels);
    }

    public function getLatestChannelsByUserFollowings(User $user, Request $request): ChannelCollection
    {
        $channels = Channel::with(self::RELATIONS)
            ->withCount('likes')
            ->whereHas('articles')
            ->orderByDesc(
                Following::select('id')
                    ->where('follower_id', $user->id)
                    ->whereColumn('user_id', 'channels.user_id')
                    ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            )
            ->paginate(18);

        return new ChannelCollection($channels);
    }

    public function searchChannels(?string $query, int $limit = 5): ChannelCollection
    {
        return new ChannelCollection(
            Channel::search($query)
                ->query(
                    fn (Builder $builder) => $builder
                        ->limit($limit)
                        ->with(self::RELATIONS)
                        ->whereHas('articles')
                )
                ->get()
        );
    }

    public function getRandomChannels(User $user): ChannelCollection
    {
        $channels = Channel::with(self::RELATIONS)
            ->whereHas('articles')
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->paginate(36);

        return new ChannelCollection($channels);
    }

    public function getArtistChannels(User $artist): ChannelCollection
    {
        return new ChannelCollection(
            $artist->channels()
                ->whereHas('articles')
                ->with(ChannelRepository::RELATIONS)
                ->latest()
                ->paginate(6)
        );
    }

    public function getChannelTypePodcasts(User $user): ChannelCollection
    {
        $channels = Channel::podcast()
            ->with(self::RELATIONS)
            ->whereHas('articles')
            ->orderByRaw('md5(?)', [$user->id.date('Y-m-d')])
            ->paginate(36);

        return new ChannelCollection($channels);
    }

    public function getUserLikesChannels(User $user): ChannelLikeableCollection
    {
        $channels = $user->likes()
            ->with([
                'likeable' => function ($query) {
                    $query->with(self::RELATIONS);
                },
            ])
            ->whereMorphedTo('likeable', Channel::class)
            ->latest()
            ->paginate(36);

        return new ChannelLikeableCollection($channels);
    }
}
