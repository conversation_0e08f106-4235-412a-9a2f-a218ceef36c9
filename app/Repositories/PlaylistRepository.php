<?php

namespace App\Repositories;

use App\Http\Resources\Likeable\PlaylistLikeableCollection;
use App\Http\Resources\Playlist\PlaylistCollection;
use App\Http\Resources\Playlist\PlaylistResource;
use App\Models\Playlist\Playlist;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class PlaylistRepository
{
    public const RELATIONS = [
        'image',
        'user',
        'user' => UserRepository::ARTIST_RELATIONS,
        'likes',
    ];

    public function getPlaylistDetails(Playlist $playlist): PlaylistResource
    {
        return new PlaylistResource($playlist);
    }

    public function getMorePlaylistsFromArtist(Playlist $playlist): PlaylistCollection
    {
        $playlists = $playlist->user->playlists()
            ->with(self::RELATIONS)
            ->whereHas('articles')
            ->whereKeyNot($playlist->id)
            ->latest()
            ->paginate(12);

        return new PlaylistCollection($playlists);
    }

    public function searchPlaylists(?string $query, int $limit = 5): PlaylistCollection
    {
        return new PlaylistCollection(
            Playlist::search($query)
                ->query(
                    fn (Builder $builder) => $builder
                        ->limit($limit)
                        ->whereHas('articles')
                )
                ->get()
        );
    }

    public function getArtistPlaylists(User $artist): PlaylistCollection
    {
        return new PlaylistCollection(
            $artist->playlists()
                ->whereHas('articles')
                ->with(PlaylistRepository::RELATIONS)
                ->latest()
                ->paginate(6)
        );
    }

    public function getUserLikesPlaylists(User $user): PlaylistLikeableCollection
    {
        $playlists = $user->likes()
            ->whereMorphedTo('likeable', Playlist::class)
            ->latest()
            ->paginate(36);

        return new PlaylistLikeableCollection($playlists);
    }
}
