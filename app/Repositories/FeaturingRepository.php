<?php

namespace App\Repositories;

use App\Http\Resources\Channel\FeaturingChannelCollection;
use App\Models\Article;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FeaturingRepository
{
    public function createManyByNames(Article $article, array $names)
    {
        $user = $article->user;

        $featuring = collect($names)
            ->filter(fn ($name) => filled($name))
            ->map(fn (string $name) => artistfy($name))
            ->filter(fn (string $name) => strlen($name) > 1)
            ->map(function (string $name) use ($user) {
                $email = emailfy(
                    text: $name,
                    domain: Str::after($user->email, '@')
                );

                $newUser = User::where('email', 'like', $email)->first();

                if (filled($newUser)) {
                    return $newUser;
                }

                return User::create([
                    'name' => $name,
                    'email' => $email,
                    'role' => $user->role,
                    'password' => Str::random(10),
                ]);
            });

        $featuring->each(function (User $user) use ($article) {
            $article->featurings()->firstOrCreate(['user_id' => $user->id]);
        });
    }

    public function getArtistFeaturings(User $artist)
    {
        $sql = Article::select(['channel_id'])
            ->whereRaw('articles.id = featurings.article_id')
            ->toSql();

        $featurings = $artist->featurings()
            ->select(DB::raw("($sql) as channel_id"))
            ->groupBy('channel_id')
            ->paginate(6);

        return new FeaturingChannelCollection($featurings);
    }
}
