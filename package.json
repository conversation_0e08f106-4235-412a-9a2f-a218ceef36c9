{"private": true, "type": "module", "scripts": {"dev": "concurrently \"pnpm vite\" \"pnpm laravel-serve\"", "preview": "concurrently \"pnpm ssr\" \"pnpm laravel-serve\"", "vite": "vite", "laravel-serve": "php artisan serve --host=0.0.0.0", "ssr": "pnpm build && php artisan inertia:start-ssr", "ssr:preview": "pnpm build:preview && php artisan inertia:start-ssr", "build": "vite build && vite build --ssr", "build:preview": "vite build --minify false --sourcemap inline && vite build --ssr --minify false --sourcemap inline", "model:typer": "php artisan model:typer --optional-relations --no-hidden --optional-nullables > ./resources/scripts/types/models.tmp.ts", "pint": "./vendor/bin/pint", "phpstan": "./vendor/bin/phpstan analyse"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.17", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "chokidar": "^3.6.0", "concurrently": "^8.2.2", "globals": "^16.1.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "postcss-nesting": "^11.3.0", "tailwind-scrollbar-hide": "^1.3.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "workbox-core": "^7.3.0", "workbox-window": "^7.3.0"}, "dependencies": {"@headlessui/react": "^1.7.19", "@inertiajs/react": "^2.0.11", "@privjs/gradients": "1.0.11", "axios": "^1.9.0", "clsx": "^1.2.1", "embla-carousel-react": "^8.6.0", "hls.js": "^1.6.2", "jotai": "^2.12.4", "laravel-echo": "^2.1.4", "lodash": "^4.17.21", "pusher-js": "8.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "4.10.1", "tailwind-merge": "^3.3.0", "use-debounce": "^10.0.4", "ziggy-js": "^2.5.3"}, "pnpm": {"ignoredBuiltDependencies": ["esbuild"]}}