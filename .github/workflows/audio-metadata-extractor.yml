name: <PERSON><PERSON> and <PERSON>ush Docker audio-metadata-extractor Image

on:
  push:
    branches:
      - main

    tags-ignore:
      - "**"

    paths:
      - "services/audio-metadata-extractor/**"
      - ".github/workflows/audio-metadata-extractor.yml"

  pull_request:
    paths:
      - "services/audio-metadata-extractor/**"
      - ".github/workflows/audio-metadata-extractor.yml"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        service: ["audio-metadata-extractor"]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: "./.github/actions/docker-build-push"
        with:
          service-name: ${{ matrix.service }}
          secret-token: ${{ secrets.GITHUB_TOKEN }}
