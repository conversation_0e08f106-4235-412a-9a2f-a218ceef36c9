name: <PERSON><PERSON> and <PERSON><PERSON> Docker text-embedder Image

on:
  push:
    branches:
      - main

    tags-ignore:
      - "**"

    paths:
      - "services/text-embedder/**"
      - ".github/workflows/text-embedder.yml"

  pull_request:
    paths:
      - "services/text-embedder/**"
      - ".github/workflows/text-embedder.yml"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        service: ["text-embedder"]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: "./.github/actions/docker-build-push"
        with:
          service-name: ${{ matrix.service }}
          secret-token: ${{ secrets.GITHUB_TOKEN }}
