name: <PERSON><PERSON> and <PERSON>ush Docker image-generator Image

on:
  push:
    branches:
      - main

    tags-ignore:
      - "**"

    paths:
      - "services/image-generator/**"
      - ".github/workflows/image-generator.yml"

  pull_request:
    paths:
      - "services/image-generator/**"
      - ".github/workflows/image-generator.yml"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        service: ["image-generator"]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: "./.github/actions/docker-build-push"
        with:
          service-name: ${{ matrix.service }}
          secret-token: ${{ secrets.GITHUB_TOKEN }}
