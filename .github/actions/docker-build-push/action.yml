name: "Docker Build and Push"
description: "Builds and pushes Docker images when changes are detected in service directories"
inputs:
  service-name:
    description: "Name of the service to build (directory under services/)"
    required: true

  secret-token:
    description: "Github Secret token"
    required: true

outputs:
  image-tag:
    value: ""
    description: "The tag of the built Docker image"

runs:
  using: "composite"
  steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ inputs.secret-token }}

    # - name: Extract metadata (branch name, tag, etc)
    #   id: meta
    #   uses: docker/metadata-action@v5
    #   with:
    #     images: ghcr.io/${{ github.repository }}/${{ inputs.service-name }}

    - name: Build and Push Image
      uses: docker/build-push-action@v5
      id: docker_build
      with:
        context: ./services/${{ inputs.service-name }}
        file: ./services/${{ inputs.service-name }}/Dockerfile
        push: true
        tags: ghcr.io/${{ github.repository }}/${{ inputs.service-name }}:latest
        cache-from: type=registry,ref=ghcr.io/${{ github.repository }}/${{ inputs.service-name }}:latest
        cache-to: type=inline
        # labels: ${{ steps.meta.outputs.labels }}

    - name: Output Image Tag
      run: echo "::set-output name=image-tag::${{ steps.docker_build.outputs.tags }}"
      shell: bash
