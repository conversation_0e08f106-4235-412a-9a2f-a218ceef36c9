SHELL:=/bin/bash
isDocker:=$(shell docker info > /dev/null 2>&1 && echo 1)
python:=python3
venv:=.venv/bin/activate
source:=source
pa:=php artisan


.DEFAULT_GOAL := help
.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'




.PHONY: install
install:
	composer install
	pnpm install





.PHONY: dev
dev:
	@pnpm dev





.PHONY: pull
pull:
	git pull origin main






.PHONY: build-service
service?=
build-service:
	docker image rm smovee/$(service) || echo ""
	cd services/$(service) && \
	docker build -t smovee/$(service) .





.PHONY: build-services
build-services:
	make build-service service=audio-metadata-extractor






.PHONY: supervisor-config
supervisor-config:
	bash scripts/supervisord/supervisor_config.sh





.PHONY: services
services:
	docker stack deploy -c compose.services.yaml smovee





.PHONY: config-cache
config-cache:
	$(pa) config:clear
	$(pa) config:cache






.PHONY: laravel-setup
laravel-setup:
	$(pa) key:generate
	$(pa) migrate
	$(pa) storage:link
	$(pa) reverb:install








.PHONY: prod
prod:
	composer install --optimize-autoloader --no-dev

	$(pa) config:clear
	$(pa) config:cache

	$(pa) event:clear
	$(pa) event:cache

	$(pa) view:clear
	$(pa) view:cache

	$(pa) icons:cache

	$(pa) filament:clear-cached-components
	$(pa) filament:cache-components

	$(pa) optimize:clear
	$(pa) optimize

	$(pa) migrate --force

	pnpm install
	pnpm build

	sudo systemctl restart supervisor





.PHONY: queue-clear
queue-clear:
	$(pa) queue:flush
	$(pa) queue:clear
	$(pa) queue:prune-batches
	$(pa) queue:prune-failed







.PHONY: queue-dev
queue-dev: queue-clear
	$(pa) queue:listen --queue=recommender,default







.PHONY: scheduler-cron
scheduler-cron:
	bash scripts/scheduler/cron.sh







.PHONY: scheduler-rm-cron
scheduler-rm-cron:
	bash scripts/scheduler/cron-rm.sh



.PHONY: scout-import
scout-import:
	$(pa) scout:import "App\Models\User"
	$(pa) scout:import "App\Models\Genre"
	$(pa) scout:import "App\Models\Channel"
	$(pa) scout:import "App\Models\Article"
	$(pa) scout:import "App\Models\Playlist\Playlist"



.PHONY: php-install
php-install:
	bash scripts/php/install.sh







.PHONY: dependencies
dependencies:
	bash scripts/.dependencies.sh








.PHONY: nginx-config
nginx-config:
	bash scripts/nginx/nginx_config.sh







.PHONY: prod-refresh
prod-refresh: pull prod services









.PHONY: ide
ide:
	$(pa) clear-compiled
	$(pa) ide-helper:generate
	$(pa) ide-helper:models -M
	$(pa) ide-helper:meta






.PHONY: store-audio-metadata
store-audio-metadata:
	sudo systemctl stop supervisor
	$(pa) queue:clear
	$(pa) telescope:clear
	$(pa) app:store-audio-metadata
	sudo systemctl start supervisor


.PHONY: k8s-deploy
k8s-deploy:
	kubectl apply -k .deploy/k8s

.PHONY: destroy-k8s-deploy
destroy-k8s-deploy:
	kubectl delete -k .deploy/k8s
