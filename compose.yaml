services:
  # ############################## DATABASES ##############################
  postgres:
    image: pgvector/pgvector:0.8.0-pg15
    restart: always
    volumes:
      - db_postgres:/var/lib/postgresql/data
    env_file:
      - .env.docker

  redis:
    image: "redis:alpine"
    volumes:
      - redis_data:/var/lib/redis/data
    restart: always

  meilisearch:
    image: getmeili/meilisearch:v1.13.3
    restart: always
    volumes:
      - meili_data:/meili_data
    env_file:
      - .env.docker

  # ############################## SERVICES ##############################

  audio-embedding-generator:
    image: quay.io/codait/max-audio-embedding-generator:latest
    stdin_open: true
    tty: true

  # ############################## APP ##############################

  app-server:
    image: ghcr.io/paradoxe35/smovee:latest
    restart: always
    depends_on: &server_depends_on
      - postgres
      - redis
      - meilisearch
    entrypoint: ["/app/entrypoints/server.sh"]
    ports:
      - "8000:8000"
    env_file:
      - .env.docker

  queue-worker:
    image: ghcr.io/paradoxe35/smovee:latest
    restart: always
    depends_on: *server_depends_on
    entrypoint: ["/app/entrypoints/queue-worker.sh"]
    env_file:
      - .env.docker

  scheduler-worker:
    image: ghcr.io/paradoxe35/smovee:latest
    restart: always
    depends_on: *server_depends_on
    entrypoint: ["/app/entrypoints/scheduler-worker.sh"]
    env_file:
      - .env.docker

# Names our volume
volumes:
  db_postgres:
  redis_data:
  meili_data:
