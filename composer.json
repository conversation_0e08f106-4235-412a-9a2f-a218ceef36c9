{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "cloudstudio/ollama-laravel": "^1.1", "dedoc/scramble": "^0.12.23", "filament/filament": "^3.3", "guzzlehttp/guzzle": "^7.9", "http-interop/http-factory-guzzle": "^1.2", "hugomyb/filament-media-action": "^3.1", "inertiajs/inertia-laravel": "^2.0", "lab404/laravel-impersonate": "^1.7", "laravel-notification-channels/telegram": "^6.0", "laravel/fortify": "^1.25", "laravel/framework": "^12.1", "laravel/horizon": "^5.31", "laravel/octane": "^2.8", "laravel/pennant": "^1.16", "laravel/pulse": "^1.4", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/scout": "^10.13", "laravel/socialite": "^5.18", "laravel/telescope": "^5.5", "laravel/tinker": "^2.10", "league/commonmark": "^2.7", "league/flysystem-aws-s3-v3": "^3.0", "meilisearch/meilisearch-php": "^1.13", "pgvector/pgvector": "^0.2.2", "predis/predis": "^2.3", "promphp/prometheus_client_php": "^2.14", "pusher/pusher-php-server": "^7.2", "spatie/fork": "^1.2", "spatie/laravel-backup": "^9.2", "spiral/roadrunner-cli": "^2.7", "spiral/roadrunner-http": "^3.5", "tightenco/ziggy": "^2.5"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.1", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan filament:upgrade"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}