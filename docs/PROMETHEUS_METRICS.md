# Prometheus Metrics for Smovee

This document describes the comprehensive Prometheus metrics implementation for the Smovee music streaming and competition platform.

## Overview

The `/metrics` endpoint provides detailed metrics about the application's health, performance, and business KPIs in Prometheus format. These metrics can be scraped by Prometheus and used for monitoring, alerting, and dashboards.

## Endpoint

- **URL**: `/metrics`
- **Method**: `GET`
- **Content-Type**: `text/plain; version=0.0.4; charset=utf-8`

## Security

The metrics endpoint can be secured using several methods:

### Environment Variables

```env
# Enable access restrictions
METRICS_ACCESS_RESTRICTED=true

# IP-based restrictions (comma-separated)
METRICS_ALLOWED_IPS=127.0.0.1,10.0.0.0/8

# Authentication requirements
METRICS_REQUIRE_AUTH=true
METRICS_REQUIRE_ADMIN=true

# API token authentication
METRICS_API_TOKEN=your-secret-token

# Caching (in seconds, 0 = no cache)
METRICS_CACHE_DURATION=60
```

### Access Methods

1. **IP Restriction**: Only allow specific IP addresses
2. **User Authentication**: Require logged-in user
3. **Admin Role**: Require admin privileges
4. **API Token**: Use token-based authentication

Token can be provided via:
- Header: `Authorization: Bearer your-token`
- Header: `X-Metrics-Token: your-token`
- Query parameter: `?token=your-token`

## Metrics Categories

### 1. Application Health Metrics

- `smovee_app_info`: Application version and environment information
- `smovee_database_connection_status`: Database connectivity (1=connected, 0=disconnected)
- `smovee_cache_connection_status`: Cache connectivity (1=connected, 0=disconnected)
- `smovee_memory_usage_bytes`: Current memory usage
- `smovee_memory_peak_bytes`: Peak memory usage

### 2. User Metrics

- `smovee_users_total{role}`: Total users by role (artist, listener, admin)
- `smovee_users_active_30d`: Active users in last 30 days
- `smovee_users_new_24h`: New registrations in last 24 hours
- `smovee_users_new_7d`: New registrations in last 7 days
- `smovee_users_new_30d`: New registrations in last 30 days

### 3. Content Metrics

- `smovee_articles_total`: Total number of tracks/articles
- `smovee_channels_total`: Total number of albums/channels
- `smovee_playlists_total`: Total number of playlists
- `smovee_articles_by_genre{genre}`: Articles count by genre
- `smovee_articles_new_24h`: New tracks in last 24 hours
- `smovee_channels_new_24h`: New albums in last 24 hours

### 4. Engagement Metrics

- `smovee_plays_total`: Total number of plays
- `smovee_plays_24h`: Plays in last 24 hours
- `smovee_plays_7d`: Plays in last 7 days
- `smovee_likes_total`: Total number of likes
- `smovee_comments_total`: Total number of comments
- `smovee_follows_total`: Total number of follows
- `smovee_unique_listeners_30d`: Unique listeners in last 30 days

### 5. Competition Metrics

- `smovee_competitions_total`: Total number of competitions
- `smovee_competitions_active`: Currently active competitions
- `smovee_competition_entries_total`: Total competition entries
- `smovee_competition_votes_total`: Total competition votes
- `smovee_competition_votes_paid`: Total paid votes
- `smovee_competition_votes_24h`: Votes in last 24 hours

### 6. Infrastructure Metrics

- `smovee_queue_jobs_pending{queue}`: Pending jobs by queue name
- `smovee_queue_jobs_failed`: Total failed jobs
- `smovee_database_queries_count`: Database queries in current request

### 7. Performance Metrics

- `smovee_cache_hit_rate`: Cache hit rate percentage
- `smovee_recommendation_jobs_pending`: Pending recommendation jobs
- `smovee_users_with_recommendations`: Users with generated recommendations

### 8. Audio Processing Metrics

- `smovee_audio_files_with_embeddings`: Audio files with ML embeddings
- `smovee_audio_files_with_hls`: Audio files with HLS conversion

### 9. Search Engine Metrics

- `smovee_search_index_status`: Search engine health (1=healthy, 0=unhealthy)
- `smovee_search_indexed_articles`: Articles in search index
- `smovee_search_indexed_users`: Users in search index
- `smovee_search_indexed_channels`: Channels in search index

### 10. Threshold Metrics (for Alerting)

- `smovee_threshold_max_failed_jobs`: Maximum allowed failed jobs
- `smovee_threshold_max_queue_size`: Maximum allowed queue size
- `smovee_threshold_min_cache_hit_rate`: Minimum required cache hit rate
- `smovee_threshold_max_response_time`: Maximum allowed response time (ms)
- `smovee_threshold_min_disk_space_gb`: Minimum required disk space (GB)

## Configuration

### Enabling/Disabling Metric Categories

```env
METRICS_ENABLE_APPLICATION=true
METRICS_ENABLE_BUSINESS=true
METRICS_ENABLE_INFRASTRUCTURE=true
METRICS_ENABLE_PERFORMANCE=true
METRICS_ENABLE_RECOMMENDATIONS=true
METRICS_ENABLE_AUDIO=true
METRICS_ENABLE_SEARCH=true
```

### Business Metrics Configuration

```env
METRICS_POPULAR_TRACK_MIN_PLAYS=10
METRICS_POPULARITY_WINDOW_DAYS=7
```

### Performance Metrics Configuration

```env
METRICS_ENABLE_QUERY_LOGGING=false
METRICS_MAX_SLOW_QUERIES=100
METRICS_SLOW_QUERY_THRESHOLD=1000
```

### Alerting Thresholds

```env
METRICS_THRESHOLD_MAX_FAILED_JOBS=100
METRICS_THRESHOLD_MAX_QUEUE_SIZE=1000
METRICS_THRESHOLD_MIN_CACHE_HIT_RATE=80
METRICS_THRESHOLD_MAX_RESPONSE_TIME=2000
METRICS_THRESHOLD_MIN_DISK_SPACE_GB=10
```

## Prometheus Configuration

Add this job to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'smovee'
    static_configs:
      - targets: ['your-app-domain.com']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s
    # If using token authentication:
    authorization:
      type: Bearer
      credentials: 'your-secret-token'
```

## Sample Alerting Rules

```yaml
groups:
  - name: smovee
    rules:
      - alert: HighFailedJobs
        expr: smovee_queue_jobs_failed > smovee_threshold_max_failed_jobs
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of failed jobs"
          description: "Failed jobs count is {{ $value }}, threshold is {{ query \"smovee_threshold_max_failed_jobs\" }}"

      - alert: LowCacheHitRate
        expr: smovee_cache_hit_rate < smovee_threshold_min_cache_hit_rate
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value }}%, threshold is {{ query \"smovee_threshold_min_cache_hit_rate\" }}%"

      - alert: DatabaseDown
        expr: smovee_database_connection_status == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection lost"
          description: "Cannot connect to the database"

      - alert: SearchEngineDown
        expr: smovee_search_index_status == 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Search engine unhealthy"
          description: "Search engine is not responding properly"
```

## Grafana Dashboard

Key metrics to monitor in Grafana:

1. **Application Health**: Database/Cache status, Memory usage
2. **User Activity**: Active users, New registrations, Engagement rates
3. **Content Growth**: New tracks/albums, Content by genre
4. **Performance**: Queue sizes, Cache hit rates, Response times
5. **Business KPIs**: Plays, Likes, Competition participation
6. **Infrastructure**: Failed jobs, Search engine health

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check IP restrictions and authentication settings
2. **500 Error**: Check logs for database connectivity or permission issues
3. **Empty Response**: Verify Prometheus client library is installed
4. **Slow Response**: Consider enabling caching or disabling expensive metrics

### Debugging

Enable debug logging to troubleshoot metrics collection:

```env
LOG_LEVEL=debug
```

Check the application logs for metrics-related errors and performance information.
