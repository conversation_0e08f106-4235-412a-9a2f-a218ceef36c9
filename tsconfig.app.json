{
    "compilerOptions": {
        "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "module": "ESNext",
        "skipLibCheck": true,
        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "isolatedModules": true,
        "moduleDetection": "force",
        "noEmit": true,
        "jsx": "react-jsx",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true,
        "types": [
            "vite-plugin-pwa/react"
        ],
        "paths": {
            "@/*": [
                "./resources/scripts/*"
            ],
            "@components/*": [
                "./resources/scripts/components/*"
            ],
            "@pages/*": [
                "./resources/views/pages/*"
            ],
            "@images/*": [
                "./resources/images/*"
            ],
        }
    },
    "include": [
        "resources/scripts",
        "resources/images",
        "resources/views/pages",
    ],
    "exclude": [
        "resources/scripts/types/models.tmp.ts"
    ]
}