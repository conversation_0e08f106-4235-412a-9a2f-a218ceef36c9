<?php

return [
    // --- General <PERSON> ---
    'total_recommendation_items_limit' => 100, // Max items across ALL playlists per user per run
    'strategy_candidate_limit' => 50,      // How many candidates to fetch per strategy initially
    'recent_plays_days' => 14,
    'popularity_days' => 7,
    'following_days' => 30,
    'max_seed_items_per_strategy' => 6,   // Max recent plays/likes to use as seeds for similarity
    'neighbors_per_seed' => 20,
    'min_popular_plays' => 10, // Minimum plays for general popularity
    'min_popular_plays_genre' => 5, // Minimum plays for genre popularity (can be lower)

    // --- Playlist Settings ---
    'target_playlist_count' => 30,        // Aim for up to this many playlists
    'items_per_playlist' => 24,           // Default number of items per playlist
    'playlist_freshness_days' => 7,       // Exclude items recommended in the last X days

    // --- Genre Strategy Settings ---
    'max_user_top_genres' => 3,          // Create mixes for top N genres based on plays
    'min_plays_for_top_genre' => 5,     // Min plays needed to consider a genre "top" for a user

    // --- Strategy Weights (Optional - Influences ranking within conceptual playlists before final rank) ---
    'weights' => [
        'following' => 1.5, // Prioritize new releases
        'recent_play_similarity' => 1.0,
        'like_similarity' => 1.2,
        'popular_genre' => 0.8, // Give genre mixes decent weight
        'popular_global' => 0.5,
        'fallback_popular' => 0.3,
        'collaborative' => 0.7,
        // Add a high weight for top listened to ensure it's selected if enabled
        'top_listened' => 10.0, // Give it a very high score
    ],

    // --- Similarity Settings ---
    'diversity' => [
        'max_artist_ratio' => 0.3,  // Max 30% from single artist
        'min_genres' => 3,          // Minimum different genres required
        'max_similarity' => 0.75,   // Max allowed cosine similarity between consecutive tracks
        'max_embedding_iterations' => 50, // Max swaps for ordering
    ],

    // --- Collaborative Filtering Settings ---
    'collaborative' => [
        'enabled' => true,
        'min_user_interactions' => 20,  // Minimum plays+likes needed to use CF
        'max_similar_users' => 50,       // Top N similar users to consider
        'min_similarity_score' => 3,     // Minimum shared interactions to be "similar"
        'recommendation_limit' => 50,     // Max recommendations to generate
    ],

    // --- Enabled Strategies ---
    'enabled_strategies' => [
        'following',
        'recent_play_similarity',
        'like_similarity',
        'popular_genre',
        'popular_global',
        'collaborative',
    ],

    // --- Recently Played Settings ---
    'recently_played_albums_lookback_days' => 30, // How far back to look for plays
    'recently_played_albums_limit' => 12,       // Max number of unique albums to return

    // --- DJ Mix Settings ---
    'dj_mix_neighbors_to_fetch' => 20, // Final pool size for random selection (higher = more diversity, less similarity)
    'dj_mix_exclude_recent_plays_days' => 1, // Optionally exclude tracks played generally in last X days
    'dj_mix_use_embedding_average_count' => 3, // Optional: Average last N tracks instead of just current
    'dj_mix_similarity_vs_diversity_balance' => 0.5, // 0.0 = most similar, 1.0 = most diverse (affects selection from filtered candidates)

    // --- Duplicate Detection Settings ---
    'duplicate_detection' => [
        'enabled' => true,
        'name_similarity_threshold' => 0.85, // Levenshtein similarity threshold for track names
        'check_same_artist' => true, // Check if tracks are from same artist/user
        'check_audio_duration' => true, // Compare audio duration for similarity
        'duration_tolerance_seconds' => 5, // Allow ±5 seconds difference in duration
        'check_audio_metadata' => false, // Use audio metadata for duplicate detection
        'tempo_tolerance_bpm' => 3, // Allow ±3 BPM difference in tempo
        'exclude_duplicate_artists_consecutive' => false, // Avoid same artist back-to-back
        'max_candidates_to_check' => 50, // Max candidates to analyze for duplicates
    ],

    // --- Top Listened Playlist Settings ---  <-- NEW SECTION
    'top_listened_playlist_enabled' => true,   // Set to true to generate this playlist
    'top_listened_playlist_size' => 20,      // Number of items in the playlist
    'top_listened_playlist_days' => 90,      // Lookback period for plays (e.g., last 90 days)
    'top_listened_playlist_min_items' => 5,   // Minimum items needed to create the playlist
    'top_listened_playlist_title' => 'Your Top Tracks', // Title for the playlist
];
