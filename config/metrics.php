<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Metrics Access Control
    |--------------------------------------------------------------------------
    |
    | These settings control access to the /metrics endpoint for Prometheus
    | monitoring. You can restrict access by IP, require authentication,
    | or use API tokens for security.
    |
    */

    // Whether to restrict access to the metrics endpoint
    'access_restricted' => env('METRICS_ACCESS_RESTRICTED', false),

    // Allowed IP addresses (empty array means no IP restriction)
    'allowed_ips' => array_filter(explode(',', env('METRICS_ALLOWED_IPS', ''))),

    // Whether to require user authentication
    'require_auth' => env('METRICS_REQUIRE_AUTH', false),

    // Whether to require admin role (implies require_auth)
    'require_admin' => env('METRICS_REQUIRE_ADMIN', false),

    // API token for metrics access (can be passed as header or query param)
    'api_token' => env('METRICS_API_TOKEN'),

    /*
    |--------------------------------------------------------------------------
    | Metrics Collection Settings
    |--------------------------------------------------------------------------
    |
    | Configure which metrics to collect and how often to refresh them.
    | Some metrics are expensive to calculate, so you may want to cache
    | them or disable them in high-traffic environments.
    |
    */

    // Cache metrics for this many seconds (0 = no caching)
    'cache_duration' => env('METRICS_CACHE_DURATION', 60),

    // Enable/disable specific metric categories
    'enabled_metrics' => [
        'application' => env('METRICS_ENABLE_APPLICATION', true),
        'business' => env('METRICS_ENABLE_BUSINESS', true),
        'infrastructure' => env('METRICS_ENABLE_INFRASTRUCTURE', true),
        'performance' => env('METRICS_ENABLE_PERFORMANCE', true),
        'recommendations' => env('METRICS_ENABLE_RECOMMENDATIONS', true),
        'audio' => env('METRICS_ENABLE_AUDIO', true),
        'search' => env('METRICS_ENABLE_SEARCH', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Metrics Configuration
    |--------------------------------------------------------------------------
    |
    | Configure time periods and thresholds for business metrics calculation.
    |
    */

    'business' => [
        // Time periods for "active user" calculations (in days)
        'active_user_periods' => [
            'daily' => 1,
            'weekly' => 7,
            'monthly' => 30,
        ],

        // Time periods for "new content" calculations (in days)
        'new_content_periods' => [
            'daily' => 1,
            'weekly' => 7,
            'monthly' => 30,
        ],

        // Minimum plays to consider a track "popular"
        'popular_track_min_plays' => env('METRICS_POPULAR_TRACK_MIN_PLAYS', 10),

        // Time window for popularity calculations (in days)
        'popularity_window_days' => env('METRICS_POPULARITY_WINDOW_DAYS', 7),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Metrics Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for performance-related metrics collection.
    |
    */

    'performance' => [
        // Enable database query logging for metrics (may impact performance)
        'enable_query_logging' => env('METRICS_ENABLE_QUERY_LOGGING', false),

        // Maximum number of slow queries to track
        'max_slow_queries' => env('METRICS_MAX_SLOW_QUERIES', 100),

        // Slow query threshold in milliseconds
        'slow_query_threshold' => env('METRICS_SLOW_QUERY_THRESHOLD', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Metrics
    |--------------------------------------------------------------------------
    |
    | Define custom metrics specific to your application needs.
    | These will be added to the standard metrics collection.
    |
    */

    'custom_metrics' => [
        // Example: Track specific events or counters
        // 'user_registrations_today' => [
        //     'type' => 'gauge',
        //     'description' => 'Number of user registrations today',
        //     'query' => 'SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()',
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Alerting Thresholds
    |--------------------------------------------------------------------------
    |
    | Define thresholds for various metrics that can be used by Prometheus
    | alerting rules. These are exported as separate metrics.
    |
    */

    'thresholds' => [
        'max_failed_jobs' => env('METRICS_THRESHOLD_MAX_FAILED_JOBS', 100),
        'max_queue_size' => env('METRICS_THRESHOLD_MAX_QUEUE_SIZE', 1000),
        'min_cache_hit_rate' => env('METRICS_THRESHOLD_MIN_CACHE_HIT_RATE', 80),
        'max_response_time' => env('METRICS_THRESHOLD_MAX_RESPONSE_TIME', 2000),
        'min_disk_space_gb' => env('METRICS_THRESHOLD_MIN_DISK_SPACE_GB', 10),
    ],
];
