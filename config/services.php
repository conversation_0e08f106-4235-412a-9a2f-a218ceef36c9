<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT'),
    ],

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Services
    |--------------------------------------------------------------------------
    */

    'telegram-bot-api' => [
        'token' => env('TELEGRAM_BOT_TOKEN', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | Local Services
    |--------------------------------------------------------------------------
    */

    // audio-metadata-extractor
    'audio_metadata_extractor' => [
        'url' => env('SERVICE_AME_URL'),
    ],

    // audio-embedding-generator
    'audio_embedding_generator' => [
        'url' => env('SERVICE_MAEG_URL'),
    ],

    // text-embedder
    'text_embedder' => [
        'url' => env('SERVICE_TEXT_EMBEDDER_URL'),
    ],

    // image-generator
    'image_generator' => [
        'url' => env('SERVICE_IMAGE_GENERATOR_URL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Services
    |--------------------------------------------------------------------------
    */

    'flutterwave' => [
        'base_url' => env('FLUTTERWAVE_BASE_URL', 'https://api.flutterwave.com/v3'),
        'secret_key' => env('FLUTTERWAVE_SECRET_KEY'),
        'public_key' => env('FLUTTERWAVE_PUBLIC_KEY'),
        'encryption_key' => env('FLUTTERWAVE_ENCRYPTION_KEY'),
        'is_live' => env('FLUTTERWAVE_IS_LIVE', false),
    ],
];
