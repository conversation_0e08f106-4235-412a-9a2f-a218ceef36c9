<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Audio Processing
    |--------------------------------------------------------------------------
    |
    | Configuration for audio processing settings.
    |
    */
    'max_audio_length' => 150, // Maximum audio length in seconds

    /*
    |--------------------------------------------------------------------------
    | Embedding Dimensions
    |--------------------------------------------------------------------------
    |
    | Define the dimensions used for different types of embeddings.
    | This ensures consistency across migrations, jobs, and services.
    | **Important**: Changing this value after embeddings have been generated
    | requires migrating existing data or regenerating all embeddings.
    |
    */

    'audio_dimension' => 128,

    'text_dimension' => 768,
];
