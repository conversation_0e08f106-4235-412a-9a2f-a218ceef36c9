Web:
    ✔ Api Mobile endpoint @done(24-01-31 23:18)
    ✔ APIs @done(24-02-03 14:17)
        ✔ Write Channel create api, @done(24-02-03 14:17)
        ✔ search Channel API, @done(24-02-03 14:17)
        ✔ Random Genre api @done(24-02-03 14:17)
        ✔ Create Article (Audio) @done(24-02-03 14:17)

    Add queue to determine if a channel is album | single
    ✔ Migrate mysql to postgres @done(24-02-10 00:16)
    ✔ Record all user activities in database (for later music recommander) @done(24-02-12 06:20)
    ✔ Save article audio metadata in database @done(24-02-14 16:25)
        ✔ create model and migration @done(24-02-12 08:31)
        ✔ create python service that extract information from a file @done(24-02-12 20:44)
        ✔ Fix profile migration error (!! Urgent) @done(24-02-13 01:31)
        ✔ implement laravel queue that fetch information from service and store it in database @done(24-02-13 17:12)
    ✔ Add Auto database Backup @done(24-02-15 12:52)
        ✘ use Zstandard  as compression tool @cancelled(24-02-15 20:08)
        ✘ and send the compressed file to Mega cloud or whatever @cancelled(24-02-15 20:09)
        ✔ setup notification system @done(24-02-16 08:14)
    ✔ Add vector database and vectorize user data @done(24-02-19 09:38)
        ✔ Audio Embeddings @done(24-02-19 16:16)
        ✔ Audio Metadata embeddings @done(24-03-15 06:43)
        ✔ Extract all song information for the embeddings @done(24-03-15 06:43)
    ✔ Audio classification and pertinent audio recommendation @done(25-03-27 19:31)
        ✔ Two types of recommendation @done(25-03-27 19:31)
            ✔ User based recommendation @done(25-03-27 19:31)
                ✔ based on user recent plays @done(25-03-27 19:31)
                ✔ based on user likes @done(25-03-27 19:31)
                ✔ based on latest song from user followings @done(25-03-27 19:31)
            ✔ Global music based recommendation @done(25-03-27 19:31)
                ✔ based on popular musics @done(25-03-27 19:31)
                ✔ Genre music @done(25-03-27 19:31)
    ✔ add repeat and shuffle feature on player @done(25-03-27 19:32)
    ✔ Social login (Google and facebook) @done(25-03-27 19:32)

    ✔ Display recommendation items in discover page @done(25-03-28 10:23)

    [] Add Music player  mobile
    [] Add ViteJS PWA
    
    [] Playlist should be searchable

    [] Add Playlists
    [] Create playlist
    [] Add songs to playlist

    [] Rewrite audio player
        [] Shared Live audio player between session
        ✔ Save Audio player in user account database instead of localstorage browser (localstorage improved) @done(25-03-27 19:31)

    [] Multi languages
    [] Admin Panel - Dashboard Charts Widget
        [] display listeners grouped by country or area
    
    [] Display list instead 


Mobile:
    [] Layout configuration
    [] Authentication