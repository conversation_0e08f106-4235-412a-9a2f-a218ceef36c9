services:
  audio-metadata-extractor:
    image: ghcr.io/paradoxe35/smovee/audio-metadata-extractor:latest
    ports:
      - "127.0.0.1:5001:5000"
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  max-audio-embedding-generator:
    image: quay.io/codait/max-audio-embedding-generator:latest
    ports:
      - "127.0.0.1:5010:5000"
    stdin_open: true
    tty: true
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  text-embedder:
    image: ghcr.io/paradoxe35/smovee/text-embedder:latest
    ports:
      - "127.0.0.1:5020:5000"
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  image-generator:
    image: ghcr.io/paradoxe35/smovee/image-generator:latest
    ports:
      - "127.0.0.1:5030:5000"
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  ollama:
    image: ollama/ollama:latest
    volumes:
      - ollama:/root/.ollama
    ports:
      - "127.0.0.1:11434:11434"
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

volumes:
  ollama:
