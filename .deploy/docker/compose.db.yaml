services:
  db:
    image: mariadb:10.6
    restart: always
    environment:
      MYSQL_DATABASE: "smovee"
      MYSQL_USER: "root"
      MYSQL_PASSWORD: "Uhoo7hah"
      MYSQL_ROOT_PASSWORD: "Uhoo7hah"
    ports:
      - "127.0.0.1:3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  db-postgres:
    image: pgvector/pgvector:0.8.0-pg15
    restart: always
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - db_postgres:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: smovee
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Uhoo7hah

  redis:
    image: "redis:alpine"
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/var/lib/redis/data
    restart: always

  meilisearch:
    image: getmeili/meilisearch:v1.13.3
    container_name: meilisearch
    restart: always
    ports:
      - "127.0.0.1:7700:7700"
    environment:
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY:-masterKey}
    volumes:
      - meili_data:/meili_data

# Names our volume
volumes:
  db_data:
  db_postgres:
  redis_data:
  meili_data:
