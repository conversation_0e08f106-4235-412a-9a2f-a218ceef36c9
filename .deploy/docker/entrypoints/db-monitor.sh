#!/bin/sh
set -e

echo "Ensuring database is ready..."

# Set timeout variables
DEFAULT_TIMEOUT=120  # 2 minutes default
TIMEOUT_SECONDS=${1:-$DEFAULT_TIMEOUT}  # Use first argument if provided, otherwise default
SLEEP_INTERVAL=5     # 5 seconds between retries

start_time=$(date +%s)
end_time=$((start_time + TIMEOUT_SECONDS))
attempt=1

# Try database monitor with timeout
while [ $(date +%s) -lt $end_time ]; do
  echo "Database check attempt $attempt ($(($end_time - $(date +%s))) seconds remaining)"

  if php artisan db:monitor --silent > /dev/null 2>&1; then
    echo "Database connection successful!"
    exit 0
  else
    echo "Database not ready yet, retrying in $SLEEP_INTERVAL seconds..."
    sleep $SLEEP_INTERVAL
    attempt=$((attempt + 1))
  fi
done

# If we got here, we timed out
echo "ERROR: Database connection timed out after $TIMEOUT_SECONDS seconds"
exit 1