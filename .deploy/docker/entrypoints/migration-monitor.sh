#!/bin/sh
set -e

echo "Monitoring migrations status..."

# Set timeout variables
DEFAULT_TIMEOUT=300  # 5 minutes default
TIMEOUT_SECONDS=${1:-$DEFAULT_TIMEOUT}  # Use first argument if provided, otherwise default
SLEEP_INTERVAL=5     # 5 seconds between checks

start_time=$(date +%s)
end_time=$((start_time + TIMEOUT_SECONDS))
attempt=1

# Check migrations status with timeout
while [ $(date +%s) -lt $end_time ]; do
  echo "Migration check attempt $attempt ($(($end_time - $(date +%s))) seconds remaining)"
  
  # Run migration status command and capture output
  migration_output=$(php artisan migrate:status 2>&1)
  
  # Count number of pending migrations
  pending_count=$(echo "$migration_output" | grep -c "Pending" || true)
  
  if [ "$pending_count" -gt 0 ]; then
    echo "Found $pending_count pending migration(s), waiting for completion..."
    echo "$migration_output" | grep "Pending"
    sleep $SLEEP_INTERVAL
    attempt=$((attempt + 1))
  else
    echo "All migrations completed successfully!"
    echo "$migration_output" | grep -E "(\[[0-9]+\] Ran|Pending)" | tail -5  # Show last few migration statuses
    exit 0
  fi
done

# If we got here, we timed out
echo "ERROR: Timed out after $TIMEOUT_SECONDS seconds waiting for migrations to complete"
echo "Current migration status:"
php artisan migrate:status | grep -E "(\[[0-9]+\] Ran|Pending)" | tail -10
exit 1