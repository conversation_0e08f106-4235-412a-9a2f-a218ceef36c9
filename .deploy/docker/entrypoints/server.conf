[supervisord]
nodaemon=true
user=root
logfile=/dev/stdout
logfile_maxbytes=0
loglevel=info
minfds=10000


[program:octane]
command=php /app/artisan octane:start --host=0.0.0.0 --port=8000 --caddyfile=/app/.deploy/docker/caddy/Caddyfile
directory=/app
autostart=true
autorestart=true
startsecs=5
startretries=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:inertia-ssr]
command=php /app/artisan inertia:start-ssr
directory=/app
autostart=true
autorestart=true
startsecs=5
startretries=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0