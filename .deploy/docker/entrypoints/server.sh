#!/bin/sh
set -e

# First run the database monitor script
/app/entrypoints/db-monitor.sh

# Running migrations
php artisan migrate --force

# Optimize
php artisan optimize:clear
php artisan optimize

# Check if DEFAULT_GENRES environment variable is set to true
if [ "$DEFAULT_GENRES" = "true" ]; then
  echo "Filling default genres..."
  php artisan app:fill-default-genres & # Run in the background
fi

# Update scout driver index settings in the background
php artisan scout:sync-index-settings --quiet > /dev/null 2>&1 &

echo "Starting web server with Octane + Inertia SSR..."
supervisord -c /app/entrypoints/server.conf
