FROM dunglas/frankenphp:php8.3-alpine AS base

RUN apk add --no-cache \
    libxml2-dev \
    libcurl \
    curl-dev \
    ffmpeg \
    oniguruma-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libwebp-dev \
    giflib-dev \
    icu-dev \
    libzip-dev \
    linux-headers \
    postgresql-dev \
    supervisor

RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp

RUN docker-php-ext-install \
    mbstring \
    exif \
    intl \
    pcntl \
    bcmath \
    gd \
    zip \
    sockets \
    pdo_pgsql

WORKDIR /app
COPY . /app

RUN mkdir -p /app/storage/app/public
RUN mkdir -p /app/bootstrap/cache
RUN mkdir -p /app/storage/framework/cache/data
RUN mkdir -p /app/storage/framework/sessions
RUN mkdir -p /app/storage/framework/views
RUN mkdir -p /app/storage/framework/testing
RUN mkdir -p /app/storage/logs

# Build frontend
FROM node:20-alpine AS frontend

RUN npm install -g npm@latest

WORKDIR /app

COPY ./package.json /app/package.json
RUN npm install

COPY . /app
RUN npm run build && \
    npm prune --production


# Release step
FROM base AS release

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install nodejs
RUN apk add --no-cache nodejs

ENV APP_ENV=production
ENV OCTANE_SERVER=frankenphp
ENV CADDY_SERVER_EXTRA_DIRECTIVES="header /build/sw.js \"Service-Worker-Allowed\" \"/\""

# Install production dependencies only
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress

# Clear caches and optimize
RUN php artisan route:cache \
    && php artisan view:cache \
    && php artisan event:cache \
    && php artisan storage:link \
    && php artisan icons:cache \
    && php artisan filament:cache-components

# Copy assets
COPY --from=frontend /app/public /app/public
COPY --from=frontend /app/bootstrap/ssr /app/bootstrap/ssr
COPY --from=frontend /app/node_modules /app/node_modules

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini" && \
    sed -i 's/upload_max_filesize = 2M/upload_max_filesize = 200M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/post_max_size = 8M/post_max_size = 200M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/memory_limit = 128M/memory_limit = 256M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/max_execution_time = 30/max_execution_time = 300/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/max_input_time = 60/max_input_time = 300/' "$PHP_INI_DIR/php.ini"

# Entry point script
COPY .deploy/docker/entrypoints /app/entrypoints
RUN chmod +x entrypoints/*.sh

EXPOSE 8000
