---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: audio-embedding-generator
  namespace: smovee
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: audio-embedding-generator
  template:
    metadata:
      labels:
        app: audio-embedding-generator
    spec:
      containers:
        - name: audio-embedding-generator
          image: quay.io/codait/max-audio-embedding-generator:latest
          imagePullPolicy: IfNotPresent
          stdin: true
          tty: true
          ports:
            - name: http
              containerPort: 5000
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: "1000m"
              memory: 2Gi

          readinessProbe:
            httpGet:
              path: /model/metadata
              port: 5000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            httpGet:
              path: /model/metadata
              port: 5000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

---
apiVersion: v1
kind: Service
metadata:
  name: audio-embedding-generator
  namespace: smovee
  labels:
    app: audio-embedding-generator
spec:
  selector:
    app: audio-embedding-generator
  ports:
    - name: http
      port: 5000
      targetPort: 5000

---
# Horizontal Pod Autoscaler for server
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: audio-embedding-generator-hpa
  namespace: smovee
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: audio-embedding-generator
  minReplicas: 1
  maxReplicas: 4
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 95
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 95
