apiVersion: nginxpm-operator.io/v1
kind: ProxyHost
metadata:
  name: smovee-server
  namespace: smovee
spec:
  domainNames:
    - smovee.app

  cachingEnabled: true

  forward:
    scheme: http
    service:
      name: smovee-server
    advancedConfig: |
      ignore_invalid_headers off;
      client_max_body_size 0;
      proxy_buffering off;
      proxy_request_buffering off;

  ssl:
    autoCertificateRequest: true
