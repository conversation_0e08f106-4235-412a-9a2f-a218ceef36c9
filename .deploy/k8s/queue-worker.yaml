---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-worker
  namespace: smovee
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: queue-worker
  template:
    metadata:
      labels:
        app: queue-worker
    spec:
      terminationGracePeriodSeconds: 60
      containers:
        - name: queue-worker
          image: ghcr.io/paradoxe35/smovee:v0.0.1
          imagePullPolicy: Always
          command: ["/app/entrypoints/queue-worker.sh"]

          envFrom:
            - configMapRef:
                name: smovee-config
            - secretRef:
                name: smovee-secrets

          volumeMounts:
            - name: upload-data
              mountPath: /app/storage/app

          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: "1000m"
              memory: 2Gi

          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "queue:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "queue:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

      volumes:
        - name: upload-data
          persistentVolumeClaim:
            claimName: upload-data

---
# Horizontal Pod Autoscaler for queue-worker
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: queue-worker-hpa
  namespace: smovee
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: queue-worker
  minReplicas: 1
  maxReplicas: 3
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 95
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 95
