# Database StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: smovee-database
  namespace: smovee
spec:
  serviceName: smovee-database
  replicas: 1
  podManagementPolicy: OrderedReady
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: smovee-database
  template:
    metadata:
      labels:
        app: smovee-database
    spec:
      terminationGracePeriodSeconds: 120
      containers:
        - name: postgres
          image: pgvector/pgvector:0.8.0-pg15
          ports:
            - name: postgres
              containerPort: 5432

          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: smovee-secrets
                  key: DB_PASSWORD
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: smovee-secrets
                  key: DB_USERNAME
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: smovee-secrets
                  key: DB_DATABASE

          volumeMounts:
            - name: postgres-data
              mountPath: /var/lib/postgresql/data

          resources:
            requests:
              cpu: 1000m
              memory: 2Gi
            limits:
              cpu: "2"
              memory: 4Gi
          livenessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - $(POSTGRES_USER)
                - -d
                - $(POSTGRES_DB)
            initialDelaySeconds: 300
            periodSeconds: 300
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - $(POSTGRES_USER)
                - -d
                - $(POSTGRES_DB)
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          startupProbe:
            exec:
              command:
                - pg_isready
                - -U
                - $(POSTGRES_USER)
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30

  volumeClaimTemplates:
    - metadata:
        name: postgres-data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: nfs-client
        resources:
          requests:
            storage: 50Gi

---
# Database Service (Headless service for StatefulSet)
apiVersion: v1
kind: Service
metadata:
  name: smovee-database
  namespace: smovee
  labels:
    app: smovee-database
spec:
  selector:
    app: smovee-database
  ports:
    - name: postgres
      port: 5432
      targetPort: 5432
  # For production StatefulSet, we use a headless service (no clusterIP)
  clusterIP: None
