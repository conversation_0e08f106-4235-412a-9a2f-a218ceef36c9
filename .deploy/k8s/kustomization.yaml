apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: smovee

resources:
  - namespace.yaml
  - volumes.yaml
  - postgres.yaml
  - redis.yaml
  - meilisearch.yaml
  - audio-embedding-generator.yaml
  - server.yaml
  - hostproxy.yaml

  # Use either `queue-worker.yaml` or `horizon-worker.yaml`, but not both.
  - queue-worker.yaml

  - scheduler-worker.yaml
  - pulse-worker.yaml

configMapGenerator:
  - name: smovee-config
    envs:
      - ".env.config"

secretGenerator:
  - name: smovee-secrets
    envs:
      - ".env.secrets"

images:
  - name: ghcr.io/paradoxe35/smovee
    newTag: v0.0.1

patches:
  # Patch for PVCs
  - patch: |-
      apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: not-important
      spec:
        storageClassName: nfs-client
    target:
      kind: PersistentVolumeClaim

  # JSON 6902 patch for StatefulSets with volumeClaimTemplates
  - patch: |-
      - op: replace
        path: /spec/volumeClaimTemplates/0/spec/storageClassName
        value: nfs-client
    target:
      group: apps
      version: v1
      kind: StatefulSet
    options:
      allowNameChange: true
