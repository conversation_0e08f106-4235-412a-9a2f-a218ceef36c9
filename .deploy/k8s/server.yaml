---
# Smovee Server Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: smovee-server
  namespace: smovee
spec:
  replicas: 1 # Multiple replicas for high availability
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: smovee-server
  template:
    metadata:
      labels:
        app: smovee-server
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - smovee-server
                topologyKey: "kubernetes.io/hostname"
      containers:
        - name: server
          image: ghcr.io/paradoxe35/smovee:v0.0.1
          imagePullPolicy: Always
          command: ["/app/entrypoints/server.sh"]
          ports:
            - name: http
              containerPort: 8000

          envFrom:
            - configMapRef:
                name: smovee-config
            - secretRef:
                name: smovee-secrets
          volumeMounts:
            - name: upload-data
              mountPath: /app/storage/app

          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: "1000m"
              memory: 2Gi
          livenessProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

      volumes:
        - name: upload-data
          persistentVolumeClaim:
            claimName: upload-data

---
# Smovee Server Service
apiVersion: v1
kind: Service
metadata:
  name: smovee-server
  namespace: smovee
  labels:
    app: smovee-server
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "80"
spec:
  selector:
    app: smovee-server
  ports:
    - name: http
      port: 80
      targetPort: 8000
  type: NodePort

---
# Horizontal Pod Autoscaler for server
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: smovee-server-hpa
  namespace: smovee
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: smovee-server
  minReplicas: 1
  maxReplicas: 3
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 90
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 90
