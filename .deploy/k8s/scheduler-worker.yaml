---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-worker
  namespace: smovee
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: scheduler-worker
  template:
    metadata:
      labels:
        app: scheduler-worker
    spec:
      containers:
        - name: scheduler-worker
          image: ghcr.io/paradoxe35/smovee:v0.0.1
          imagePullPolicy: Always
          command: ["/app/entrypoints/scheduler-worker.sh"]

          envFrom:
            - configMapRef:
                name: smovee-config
            - secretRef:
                name: smovee-secrets

          volumeMounts:
            - name: upload-data
              mountPath: /app/storage/app

          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: "1000m"
              memory: 1Gi

          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "schedule:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "schedule:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

      volumes:
        - name: upload-data
          persistentVolumeClaim:
            claimName: upload-data
