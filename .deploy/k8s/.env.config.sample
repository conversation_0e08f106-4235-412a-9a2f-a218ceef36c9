################  APP ####################

APP_NAME=Smovee
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8000
ASSET_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=cache
# APP_MAINTENANCE_STORE=database


VITE_APP_NAME="Smovee"

############### AUTO GENERATE GENRES ###############

DEFAULT_GENRES=true

################ OCTANE ############################

OCTANE_SERVER=frankenphp
OCTANE_HTTPS=false

################ CONTAINER #########################

IN_CONTAINER=true

################ PHP DEV CLI ########################

PHP_CLI_SERVER_WORKERS=4

################ BCRYPT ###########################

BCRYPT_ROUNDS=12

################ LOGGING #########################

LOG_CHANNEL=stderr
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

################ DATABASE ########################

DB_CONNECTION=pgsql

################# SESSION #########################

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

################# BROADCASTING ###################

BROADCAST_CONNECTION=null

################# FILESYSTEM #####################

FILESYSTEM_DISK=local

################# QUEUE ###########################

QUEUE_CONNECTION=redis

################# CACHE ###########################

CACHE_STORE=redis

################# SCOUT ###########################

SCOUT_DRIVER=meilisearch

################# MEILISEARCH ###################

MEILISEARCH_HOST=http://meilisearch:7700

################# REDIS #########################

REDIS_CLIENT=predis
REDIS_HOST=smovee-redis
REDIS_PASSWORD=null
REDIS_PORT=6379

################# PULSE #########################

PULSE_INGEST_DRIVER=redis

################# SERVICES #####################

SERVICE_MAEG_URL=http://audio-embedding-generator:5000/model/predict

################# AUDIO ##############

HLS_ENABLED=false

################## METRICS ####################

METRICS_ACCESS_RESTRICTED=true
METRICS_ALLOWED_IPS=127.0.0.1