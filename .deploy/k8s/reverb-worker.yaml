---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: reverb-worker
  namespace: smovee
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: reverb-worker
  template:
    metadata:
      labels:
        app: reverb-worker
    spec:
      containers:
        - name: reverb-worker
          image: ghcr.io/paradoxe35/smovee:v0.0.1
          imagePullPolicy: Always
          command: ["/app/entrypoints/reverb-worker.sh"]
          ports:
            - name: http
              containerPort: 8080

          envFrom:
            - configMapRef:
                name: smovee-config
            - secretRef:
                name: smovee-secrets
          env:
            - name: REVERB_SCALING_ENABLED
              value: "true"

            - name: REVERB_SERVER_HOST
              value: 0.0.0.0

            - name: REVERB_SERVER_PORT
              value: "8080"

          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: "1000m"
              memory: 2Gi

          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

---
# Horizontal Pod Autoscaler for reverb-worker
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: reverb-worker-hpa
  namespace: smovee
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: reverb-worker
  minReplicas: 1
  maxReplicas: 3
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 95
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 95
