---
# Source: meilisearch/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: meilisearch
  namespace: smovee
  labels:
    app.kubernetes.io/name: meilisearch
    app.kubernetes.io/instance: meilisearch
    app.kubernetes.io/version: "v1.13.0"
    app.kubernetes.io/component: search-engine
    app.kubernetes.io/part-of: smovee

---
apiVersion: v1
kind: Service
metadata:
  name: meilisearch
  namespace: smovee
  labels:
    app.kubernetes.io/name: meilisearch
    app.kubernetes.io/instance: meilisearch
    app.kubernetes.io/version: "v1.13.0"
    app.kubernetes.io/component: search-engine
    app.kubernetes.io/part-of: smovee
spec:
  type: ClusterIP
  ports:
    - port: 7700
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: meilisearch
    app.kubernetes.io/instance: meilisearch
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: meilisearch
  namespace: smovee
  labels:
    app.kubernetes.io/name: meilisearch
    app.kubernetes.io/instance: meilisearch
    app.kubernetes.io/version: "v1.13.0"
    app.kubernetes.io/component: search-engine
    app.kubernetes.io/part-of: smovee
spec:
  replicas: 1
  serviceName: meilisearch
  selector:
    matchLabels:
      app.kubernetes.io/name: meilisearch
      app.kubernetes.io/instance: meilisearch
  template:
    metadata:
      labels:
        app.kubernetes.io/name: meilisearch
        app.kubernetes.io/instance: meilisearch
        app.kubernetes.io/version: "v1.13.0"
        app.kubernetes.io/component: search-engine
        app.kubernetes.io/part-of: meilisearch
      annotations:
        checksum/config: 0133559b9f57be88ab0b3257f4eb864804629131dc65524c56d984c75d57503e
    spec:
      serviceAccountName: meilisearch
      securityContext:
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000

      containers:
        - name: meilisearch
          image: "getmeili/meilisearch:v1.13.0"
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
          volumeMounts:
            - name: meilisearch-tmp
              mountPath: /tmp
            - name: meilisearch-data
              mountPath: /meili_data

          env:
            - name: MEILI_MASTER_KEY
              valueFrom:
                secretKeyRef:
                  name: smovee-secrets
                  key: MEILISEARCH_KEY

          ports:
            - name: http
              containerPort: 7700
              protocol: TCP
          startupProbe:
            httpGet:
              path: /health
              port: http
            periodSeconds: 1
            initialDelaySeconds: 1
            failureThreshold: 60
          livenessProbe:
            httpGet:
              path: /health
              port: http
            periodSeconds: 10
            initialDelaySeconds: 0
          readinessProbe:
            httpGet:
              path: /health
              port: http
            periodSeconds: 10
            initialDelaySeconds: 0
          resources: {}

      volumes:
        - name: meilisearch-tmp
          emptyDir: {}

  volumeClaimTemplates:
    - metadata:
        name: meilisearch-data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: nfs-client
        resources:
          requests:
            storage: 8Gi
