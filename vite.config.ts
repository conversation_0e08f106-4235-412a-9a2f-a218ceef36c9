// @ts-check
import { defineConfig } from "vite";
import { VitePWA } from "vite-plugin-pwa";
import laravel from "laravel-vite-plugin";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
    plugins: [
        react(),
        laravel({
            input: [
                "resources/scripts/main.tsx",
                "resources/scripts/styles/index.css",
            ],
            refresh: ["app/View/Components/**", "routes/**"],
            ssr: "resources/scripts/ssr.tsx",
        }),
        // @ts-ignore
        VitePWA({
            buildBase: "/build/",
            outDir: "public/build",

            registerType: "autoUpdate",
            injectRegister: "script-defer",

            scope: "/",
            base: "/",

            pwaAssets: {
                disabled: false,
                config: true,
            },

            manifest: {
                name: "Smovee",
                short_name: "Smovee",
                description:
                    "Displacement does not silence creativity. It amplifies resilience.",
                theme_color: "#1f2937",
                scope: "/",
                id: "/app",
                start_url: "/app",
                orientation: "portrait",
                display: "standalone",
            },

            workbox: {
                globPatterns: [
                    "**/*.{js,css,html,ico,jpg,png,svg,woff,woff2,ttf,eot}",
                ],
                cleanupOutdatedCaches: true,
                clientsClaim: true,
                maximumFileSizeToCacheInBytes: 3000000,
                navigateFallback: null,
                navigateFallbackDenylist: [
                    /^\/docs\/api/,
                    /^\/dash/,
                    /^\/vendor\/telescope/,
                    /^\/telescope/,
                    /^\/pulse/,
                    /^\/horizon/,
                ],
            },

            devOptions: {
                enabled: false,
                navigateFallback: "index.html",
                suppressWarnings: true,
                type: "module",
            },
        }),
    ],
    server: {
        hmr: {
            host: "localhost",
        },
    },
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "resources/scripts"),
            "@components": path.resolve(
                __dirname,
                "resources/scripts/components"
            ),
            "@pages": path.resolve(__dirname, "resources/views/pages"),
            "@images": path.resolve(__dirname, "resources/images"),
        },
    },
    ssr: {
        noExternal: ["react-icons"],
    },
});
