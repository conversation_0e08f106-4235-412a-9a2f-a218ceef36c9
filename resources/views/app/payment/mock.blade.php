<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Payment</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 min-h-screen flex items-center justify-center p-10">
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        @if(isset($vote_count))
            {{-- Vote Payment --}}
            <div class="text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Vote Payment</h1>
                <p class="text-gray-600">Support {{ $artist->name }} in {{ $competition->name }}</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-2">Vote Details</h2>
                <div class="text-blue-700 space-y-1">
                    <div class="flex justify-between">
                        <span>Artist:</span>
                        <span class="font-medium">{{ $artist->name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Competition:</span>
                        <span class="font-medium">{{ $competition->name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Number of votes:</span>
                        <span class="font-medium">{{ $vote_count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Price per vote:</span>
                        <span class="font-medium">${{ number_format($competition->vote_price ?? 1.00, 2) }}</span>
                    </div>
                </div>
            </div>
        @else
            {{-- Subscription Payment --}}
            <div class="text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Annual Subscription</h1>
                <p class="text-gray-600">Subscribe to unlock unlimited voting and support your favorite artists.</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-2">Subscription Benefits</h2>
                <ul class="text-blue-700 space-y-1 pl-5 list-disc">
                    <li>Unlimited voting in all competitions</li>
                    <li>Support your favorite artists</li>
                    <li>Access to exclusive content</li>
                    <li>Early access to new features</li>
                </ul>
            </div>
        @endif

        <div class="border-t border-b border-gray-200 py-4 mb-6">
            <div class="flex justify-between mb-2">
                <span class="text-gray-600">Transaction Reference:</span>
                <span class="font-medium">{{ $tx_ref }}</span>
            </div>
            <div class="flex justify-between mb-2">
                <span class="text-gray-600">User:</span>
                <span class="font-medium">{{ $voter->name }}</span>
            </div>
            @if(isset($vote_count))
                <div class="flex justify-between mb-2">
                    <span class="text-gray-600">Payment Type:</span>
                    <span class="font-medium">Vote Payment</span>
                </div>
                <div class="flex justify-between mb-2">
                    <span class="text-gray-600">Votes:</span>
                    <span class="font-medium">{{ $vote_count }} vote{{ $vote_count != 1 ? 's' : '' }}</span>
                </div>
            @else
                <div class="flex justify-between mb-2">
                    <span class="text-gray-600">Subscription Type:</span>
                    <span class="font-medium">Annual Membership</span>
                </div>
                <div class="flex justify-between mb-2">
                    <span class="text-gray-600">Duration:</span>
                    <span class="font-medium">1 Year</span>
                </div>
            @endif
            <div class="flex justify-between">
                <span class="text-gray-600">Amount:</span>
                <span class="font-bold text-green-600">${{ number_format($amount, 2) }}</span>
            </div>
        </div>

        <form action="{{ route('app.voting.payment.mock.process') }}" method="POST" class="space-y-4">
            @csrf
            <input type="hidden" name="tx_ref" value="{{ $tx_ref }}">
            <input type="hidden" name="voter_id" value="{{ $voter->id }}">
            <input type="hidden" name="artist_id" value="{{ $artist->id }}">
            <input type="hidden" name="competition_id" value="{{ $competition->id }}">
            <input type="hidden" name="amount" value="{{ $amount }}">
            @if(isset($vote_count))
                <input type="hidden" name="vote_count" value="{{ $vote_count }}">
            @else
                <input type="hidden" name="subscription_type" value="annual">
                <input type="hidden" name="duration" value="365">
            @endif

            <div class="space-y-2">
                <div class="flex justify-between mb-2">
                    <label for="subscription_plan" class="block text-sm font-medium text-gray-700">Subscription
                        Plan</label>
                    <span class="text-sm text-green-600 font-medium">Best Value!</span>
                </div>
                <select id="subscription_plan" name="subscription_plan"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md bg-white" disabled>
                    <option value="annual" selected>Annual Plan - ${{ number_format($amount, 2) }}</option>
                    <option value="monthly">Monthly Plan - $2.99/month</option>
                </select>
                <p class="text-xs text-gray-500">This is a mock subscription, no real payment will be processed.</p>
            </div>

            <div class="space-y-2">
                <label for="card_number" class="block text-sm font-medium text-gray-700">Card Number</label>
                <input type="text" id="card_number" name="card_number" value="4111 1111 1111 1111"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label for="expiry" class="block text-sm font-medium text-gray-700">Expiry Date</label>
                    <input type="text" id="expiry" name="expiry" value="12/25"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div class="space-y-2">
                    <label for="cvv" class="block text-sm font-medium text-gray-700">CVV</label>
                    <input type="text" id="cvv" name="cvv" value="123"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
            </div>

            <div class="pt-4">
                <button type="submit"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md transition duration-200">
                    @if(isset($vote_count))
                        Pay Now - ${{ number_format($amount, 2) }}
                    @else
                        Subscribe Now - ${{ number_format($amount, 2) }}/year
                    @endif
                </button>
                <p class="text-xs text-center text-gray-500 mt-2">
                    @if(isset($vote_count))
                        By proceeding, you agree to our Terms of Service and Privacy Policy.
                    @else
                        By subscribing, you agree to our Terms of Service and Privacy Policy.
                    @endif
                </p>
            </div>
        </form>

        <div class="mt-6 text-center">
            <a href="{{ route('app.voting.artist', ['competition' => $competition->id, 'artist' => $artist->id]) }}"
                class="text-blue-600 hover:text-blue-800 text-sm">
                Cancel and return to artist page
            </a>
        </div>
    </div>
</body>

</html>