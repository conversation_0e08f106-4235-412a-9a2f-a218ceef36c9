import { AppHead, AppLayout } from "@/components/layouts";
import {
    ShowCompetitions,
    ShowDescription,
    ShowItems,
    ShowProfile,
} from "@/components/pages/artists";
import { ArtistShowPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";

function ArtistShowPage() {
    const { props } = usePage<ArtistShowPageProps>();

    return (
        <>
            <AppHead title={props.artist.name} />

            <div className="w-full">
                <ShowProfile />

                <ShowCompetitions />

                <ShowItems />

                <ShowDescription />
            </div>
        </>
    );
}

ArtistShowPage.layout = (page: any) => <AppLayout children={page} />;

export default ArtistShowPage;
