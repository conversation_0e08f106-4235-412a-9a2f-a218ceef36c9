import { AppHead, AppLayout } from "@/components/layouts";
import { ShowMore, UserCard } from "@/components/lib";
import { ArticleLook } from "@/components/lib/ui";
import { useSyncModelState } from "@/hooks";
import { ArtistsPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { useState } from "react";

function ArtistsPage() {
    const { props } = usePage<ArtistsPageProps>();
    const [data, setData] = useState(props.artists.data || []);

    // Sync model state
    useSyncModelState(setData, props.artists.data);

    return (
        <>
            <AppHead title="Artists" />

            <div className="w-full">
                <h1 className="text-xl lg:text-2xl mb-3">Artists</h1>

                <ArticleLook className="h-max" type="card" cols={6}>
                    {data.map((user) => {
                        return (
                            <UserCard layout="fill" user={user} key={user.id} />
                        );
                    })}
                </ArticleLook>

                <ShowMore
                    pagination={props.artists}
                    onSuccess={() =>
                        setData((ps) => ps.concat(props.artists.data))
                    }
                />
            </div>
        </>
    );
}

ArtistsPage.layout = (page: any) => <AppLayout children={page} />;

export default ArtistsPage;
