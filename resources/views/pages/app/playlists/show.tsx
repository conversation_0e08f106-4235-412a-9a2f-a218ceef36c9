import { AppHead, AppLayout } from "@/components/layouts";
import { GradientWrapper } from "@/components/lib/ui";
import { ShowPlaylistProfile } from "@/components/pages/playlists";
import { TopChartArticles } from "@/components/pages/discover";
import { PlaylistShowPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";

function PlaylistShowPage() {
    const { props } = usePage<PlaylistShowPageProps>();
    const playlist = props.playlist;
    const articles = props.articles;

    const image = playlist.image || playlist.fallback_image;

    return (
        <>
            <AppHead title={playlist.name} />

            <div className="w-full">
                <ShowPlaylistProfile />

                <div className="py-7">
                    {/* <h3 className="text-xl">
                        Audio{articles.length > 1 ? `s` : ""}
                    </h3> */}

                    <div className="mt-5">
                        <TopChartArticles articles={articles} />
                    </div>
                </div>

                {playlist.description && (
                    <GradientWrapper direction="bottom-top" image={image}>
                        <div className="font-normal text-sm opacity-80 w-full lg:w-4/5 mt-3">
                            <p className="capitalize break-words overflow-hidden">
                                {playlist.description}
                            </p>
                        </div>
                    </GradientWrapper>
                )}
            </div>
        </>
    );
}

PlaylistShowPage.layout = (page: any) => <AppLayout children={page} />;

export default PlaylistShowPage;
