import { AppHead, AppLayout } from "@/components/layouts";
import { PlayButton } from "@/components/lib";
import { GradientWrapper } from "@/components/lib/ui";
import { GenreItems } from "@/components/pages/genre";
import { usePlayer } from "@/components/player";
import { useZiggy } from "@/hooks";
import { Article } from "@/types/models";
import { GenreShowPageProps } from "@/types/pages";
import { fetchApi } from "@/utils";
import { usePage } from "@inertiajs/react";
import { useCallback, useState } from "react";

function GenresShowPage() {
    const { props } = usePage<GenreShowPageProps>();

    const { genre } = props;

    return (
        <>
            <AppHead title={props.genre.name} />

            <div className="w-full">
                {/* Header */}
                <GradientWrapper image={genre.image}>
                    {/* Genre title */}
                    <div className="flex space-x-2 flex-wrap items-center mt-6">
                        <h1 className="text-xl lg:text-4xl font-semibold">
                            {genre.name}
                        </h1>

                        <div className="p-1 bg-slate-300/40 text-[0.65rem] rounded">
                            {genre.category}
                        </div>
                    </div>

                    <div className="flex items-center mt-4 space-x-4">
                        {/* Play button */}
                        <PlayGenre />
                    </div>
                </GradientWrapper>

                {/* Show Items */}
                <GenreItems />
            </div>
        </>
    );
}

function PlayGenre() {
    const route = useZiggy();
    const { props } = usePage<GenreShowPageProps>();
    const [loading, setLoading] = useState(false);
    const player = usePlayer();

    const onPlayClick = useCallback(() => {
        setLoading(true);

        fetchApi<Article[]>(
            route("app.search.genres.articles", { genre: props.genre.id })
        )
            .then((articles) => {
                player?.guessPlaylist({ type: "articles", data: articles });
            })
            .finally(() => setLoading(false));
    }, [route]);

    return <PlayButton onClick={onPlayClick} loading={loading} />;
}

GenresShowPage.layout = (page: any) => <AppLayout children={page} />;

export default GenresShowPage;
