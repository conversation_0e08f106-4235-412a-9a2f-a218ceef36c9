import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import { UserListItem } from "@/components/lib";
import { Button, GradientWrapper } from "@/components/lib/ui";
import { LeaderboardPageProps } from "@/types/pages";
import { cn } from "@/utils";
import { Link, usePage } from "@inertiajs/react";
import { useZiggy } from "@/hooks";

const LeaderboardPage = () => {
    const { props } = usePage<LeaderboardPageProps>();
    const { competition, leaderboard } = props;
    const route = useZiggy();

    const image = competition.image;

    return (
        <>
            <AppHead title={`${competition.name} - Leaderboard`} />
            <div className="w-full">
                <GradientWrapper image={image}>
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold mb-2">
                            {competition.name}
                        </h1>
                        <p className="text-gray-600 dark:text-gray-300 mb-4 break-words overflow-hidden">
                            {competition.description}
                        </p>

                        <div className="flex justify-center space-x-4">
                            <Link href={route("app.voting.index")}>
                                <Button variant="light" className="py-1 px-3">
                                    Back to Awards
                                </Button>
                            </Link>

                            <Link
                                href={route("app.voting.entry.form", {
                                    competition: competition.id,
                                })}
                            >
                                <Button variant="primary" className="py-1 px-3">
                                    Enter Competition
                                </Button>
                            </Link>
                        </div>
                    </div>
                </GradientWrapper>

                <AppContentWrapper>
                    <div className="my-8">
                        <h2 className="text-2xl font-bold mb-4">Leaderboard</h2>

                        {/* Competition Status */}
                        {(() => {
                            const startDate = new Date(
                                competition.start_date
                            ).toLocaleDateString();
                            const endDate = new Date(
                                competition.end_date
                            ).toLocaleDateString();

                            if (competition.is_in_entry_phase) {
                                return (
                                    <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-6">
                                        <h3 className="text-yellow-800 dark:text-yellow-300 font-semibold mb-2">
                                            Competition Entry Phase
                                        </h3>
                                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                                            This competition is currently
                                            accepting entries but voting hasn't
                                            started yet. Voting will begin on{" "}
                                            <strong>{startDate}</strong>.
                                        </p>
                                    </div>
                                );
                            } else if (competition.is_in_voting_phase) {
                                return (
                                    <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4 mb-6">
                                        <h3 className="text-green-800 dark:text-green-300 font-semibold mb-2">
                                            Competition Active - Voting Open
                                        </h3>
                                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                                            Voting is now open! Cast your votes
                                            for your favorite artists.
                                            Competition ends on{" "}
                                            <strong>{endDate}</strong>.
                                        </p>
                                    </div>
                                );
                            }
                            return null;
                        })()}

                        {/* Ranking explanation */}
                        <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                            <h3 className="text-blue-800 dark:text-blue-300 font-semibold mb-2">
                                How Rankings Work
                            </h3>
                            <p className="text-gray-700 dark:text-gray-300 text-sm">
                                Artists are ranked using a combined score:{" "}
                                <strong>75% votes</strong> and{" "}
                                <strong>25% monthly listeners</strong>. This
                                ensures both community support and artist
                                engagement are considered.
                            </p>
                        </div>

                        <div className="space-y-3 mb-6">
                            {leaderboard.data.map((artist, index) => (
                                <ArtistListItem
                                    key={artist.id}
                                    artist={artist}
                                    index={index}
                                />
                            ))}
                        </div>

                        {leaderboard.meta.last_page > 1 && (
                            <div className="flex justify-center mt-6">
                                <Link
                                    href={route("app.voting.leaderboard", {
                                        competition: competition.id,
                                        page: leaderboard.meta.current_page + 1,
                                    })}
                                >
                                    <Button
                                        variant="light"
                                        disabled={
                                            leaderboard.meta.current_page >=
                                            leaderboard.meta.last_page
                                        }
                                    >
                                        Load More
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </div>
                </AppContentWrapper>
            </div>
        </>
    );
};

function ArtistListItem({ artist, index }: any) {
    const { competition } = usePage<LeaderboardPageProps>().props;
    const route = useZiggy();

    // Get ranking colors for top 3
    const getRankingColor = (index: number) => {
        switch (index) {
            case 0:
                return "text-yellow-500"; // Gold
            case 1:
                return "text-gray-400"; // Silver
            case 2:
                return "text-amber-700"; // Bronze
            default:
                return "text-gray-600 dark:text-gray-400";
        }
    };

    return (
        <div
            key={artist.id}
            className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
        >
            {/* Ranking number and artist info */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
                {/* Ranking number */}
                <div
                    className={cn(
                        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm bg-gray-100 dark:bg-gray-700",
                        getRankingColor(index)
                    )}
                >
                    {index + 1}
                </div>

                {/* Artist info */}
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <UserListItem
                        user={artist}
                        className="flex-1 min-w-0"
                        link={route("app.voting.artist", {
                            competition: competition.id,
                            artist: artist.id,
                        })}
                    />
                </div>
            </div>

            {/* Stats and button */}
            <div className="flex items-center justify-between ml-2 sm:ml-0 sm:justify-end gap-4 flex-shrink-0">
                {/* Artist stats */}
                <div className="text-right sm:text-center">
                    {/* Vote count */}
                    <div
                        className={cn(
                            "font-bold text-base",
                            getRankingColor(index)
                        )}
                    >
                        {artist.votes_count}{" "}
                        {artist.votes_count === 1 ? "vote" : "votes"}
                    </div>

                    {/* Monthly listeners */}
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {artist.monthly_listeners_count || 0} monthly listeners
                    </div>

                    {/* Composite score indicator for top performers */}
                    {index < 3 && artist.composite_score && (
                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            Score:{" "}
                            {Math.round(artist.composite_score * 10) / 10}
                        </div>
                    )}
                </div>

                {/* Vote button - only show if voting is active */}
                {competition.is_in_voting_phase ? (
                    <Link
                        href={route("app.voting.artist", {
                            competition: competition.id,
                            artist: artist.id,
                        })}
                    >
                        <Button
                            variant="primary"
                            className="text-xs py-1.5 px-3 min-w-[60px] justify-center"
                        >
                            Vote
                        </Button>
                    </Link>
                ) : (
                    <div className="text-xs text-gray-500 dark:text-gray-400 min-w-[60px] text-center">
                        Voting Soon
                    </div>
                )}
            </div>
        </div>
    );
}

LeaderboardPage.layout = (page: any) => <AppLayout children={page} />;

export default LeaderboardPage;
