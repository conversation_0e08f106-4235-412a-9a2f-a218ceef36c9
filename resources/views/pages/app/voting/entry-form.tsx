import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import { Button, GradientWrapper } from "@/components/lib/ui";
import LegalAgreementModal from "@/components/legal-agreement-modal";
import { EntryFormPageProps } from "@/types/pages";
import { cn } from "@/utils";
import { Link, useForm, usePage } from "@inertiajs/react";
import { useZiggy } from "@/hooks";
import { useState } from "react";

const EntryFormPage = () => {
    const { props } = usePage<EntryFormPageProps>();
    const { competition, meetsRequirements } = props;
    const route = useZiggy();

    const [showLegalModal, setShowLegalModal] = useState(false);
    const [legalDocuments, setLegalDocuments] = useState<any>(null);
    const [loadingDocuments, setLoadingDocuments] = useState(false);

    const { processing, post } = useForm({
        terms_agreed: true,
        privacy_agreed: true,
        rules_agreed: true,
    });

    // Fetch legal documents when modal is opened
    const fetchLegalDocuments = async () => {
        if (legalDocuments) return; // Already loaded

        setLoadingDocuments(true);
        try {
            const response = await fetch(route("app.legal.documents"));
            if (response.ok) {
                const documents = await response.json();
                setLegalDocuments(documents);
            } else {
                throw new Error("Failed to fetch legal documents");
            }
        } catch (error) {
            console.error("Error fetching legal documents:", error);
            alert("Failed to load legal documents. Please contact support.");
        } finally {
            setLoadingDocuments(false);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Fetch legal documents and show modal
        fetchLegalDocuments().then(() => {
            setShowLegalModal(true);
        });
    };

    const handleLegalAgreement = async () => {
        // Submit the form with legal agreements
        post(
            route("app.voting.entry.submit", { competition: competition.id }),
            {
                onSuccess: () => {
                    setShowLegalModal(false);
                },
                onError: () => {
                    setShowLegalModal(false);
                },
            }
        );
    };

    const image = competition.image;

    return (
        <>
            <AppHead title={`${competition.name} - Entry Form`} />

            <div className="w-full">
                <GradientWrapper image={image}>
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold mb-2">
                            {competition.name}
                        </h1>
                        <p className="text-gray-600 dark:text-gray-300 mb-4 break-words overflow-hidden">
                            {competition.description}
                        </p>

                        <Link
                            href={route("app.voting.leaderboard", {
                                competition: competition.id,
                            })}
                        >
                            <Button
                                variant="light"
                                className="text-xs py-1 px-3"
                            >
                                Back to Leaderboard
                            </Button>
                        </Link>
                    </div>
                </GradientWrapper>

                <AppContentWrapper>
                    <div className="max-w-2xl mx-auto">
                        <h2 className="text-2xl font-bold mb-6">
                            Competition Entry Request
                        </h2>

                        <div className="bg-white dark:bg-gray-800 shadow-md dark:shadow-none p-6 rounded-lg mb-8">
                            <h3 className="text-xl font-semibold mb-4">
                                Requirements
                            </h3>
                            <ul className="list-disc pl-6 mb-6 space-y-2">
                                <li
                                    className={cn(
                                        competition.requirements?.min_followers
                                            ? "text-gray-800 dark:text-white"
                                            : "text-gray-500 dark:text-gray-400"
                                    )}
                                >
                                    {competition.requirements?.min_followers ||
                                        250}{" "}
                                    followers
                                </li>
                                <li
                                    className={cn(
                                        competition.requirements
                                            ?.min_monthly_listeners
                                            ? "text-gray-800 dark:text-white"
                                            : "text-gray-500 dark:text-gray-400"
                                    )}
                                >
                                    {competition.requirements
                                        ?.min_monthly_listeners || 750}{" "}
                                    monthly listeners
                                </li>
                                <li
                                    className={cn(
                                        competition.requirements?.min_tracks
                                            ? "text-gray-800 dark:text-white"
                                            : "text-gray-500 dark:text-gray-400"
                                    )}
                                >
                                    At least{" "}
                                    {competition.requirements?.min_tracks || 3}{" "}
                                    published tracks
                                </li>
                            </ul>

                            {meetsRequirements ? (
                                <div className="bg-green-50 dark:bg-green-900/30 border border-green-500 p-4 rounded-md mb-6">
                                    <p className="text-green-600 dark:text-green-400 font-medium">
                                        You meet all the requirements for this
                                        competition!
                                    </p>
                                    {competition.auto_approve && (
                                        <p className="text-green-600 dark:text-green-400 text-sm mt-2">
                                            🎉 Your entry will be automatically
                                            approved upon submission.
                                        </p>
                                    )}
                                </div>
                            ) : (
                                <div className="bg-red-50 dark:bg-red-900/30 border border-red-500 p-4 rounded-md mb-6">
                                    <p className="text-red-600 dark:text-red-400 font-medium">
                                        You don't meet all the requirements for
                                        this competition yet.
                                    </p>
                                    <p className="text-gray-700 dark:text-gray-300 mt-2">
                                        You can still submit your entry, but it
                                        {competition.auto_approve
                                            ? " will require manual approval since you don't meet all requirements."
                                            : " may not be approved until you meet all requirements."}
                                    </p>
                                </div>
                            )}

                            <form onSubmit={handleSubmit}>
                                <div className="flex justify-end">
                                    <Button
                                        type="submit"
                                        variant="primary"
                                        disabled={processing}
                                    >
                                        Submit Entry Request
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </AppContentWrapper>

                {/* Legal Agreement Modal */}
                {legalDocuments &&
                    legalDocuments.termsOfService &&
                    legalDocuments.privacyPolicy &&
                    legalDocuments.competitionRules && (
                        <LegalAgreementModal
                            isOpen={showLegalModal}
                            onClose={() => setShowLegalModal(false)}
                            onAgree={handleLegalAgreement}
                            termsOfService={legalDocuments.termsOfService}
                            privacyPolicy={legalDocuments.privacyPolicy}
                            competitionRules={legalDocuments.competitionRules}
                            competitionId={competition.id}
                            processing={processing}
                        />
                    )}

                {/* Loading state for legal documents */}
                {loadingDocuments && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                            <div className="flex items-center space-x-3">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                                <span className="text-gray-900 dark:text-white">
                                    Loading legal documents...
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

EntryFormPage.layout = (page: any) => <AppLayout children={page} />;

export default EntryFormPage;
