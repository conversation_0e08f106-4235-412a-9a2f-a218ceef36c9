import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import { UserListItem } from "@/components/lib";
import { ArticleLook } from "@/components/lib/ui";
import { VotingPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { CompetitionCard } from "@/components/pages/voting";

const VotingPage = () => {
    const { props } = usePage<VotingPageProps>();
    const { competitions, similar_votes, recently_voted } = props;

    return (
        <>
            <AppHead title="Awards" />

            <AppContentWrapper>
                <div className="mb-8">
                    <h1 className="text-2xl font-bold mb-4">
                        Active Award Competitions
                    </h1>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {competitions.map((competition) => (
                            <CompetitionCard
                                key={competition.id}
                                competition={competition}
                            />
                        ))}
                    </div>
                </div>

                {similar_votes.length > 0 && (
                    <div className="mb-8">
                        <h2 className="text-xl font-semibold mb-4">
                            Similar to Your Votes
                        </h2>
                        <ArticleLook
                            type="list"
                            className="mb-6 gap-6 lg:gap-0 lg:space-y-6"
                        >
                            {similar_votes.map((artist) => (
                                <div
                                    key={artist.id}
                                    className="flex items-center justify-between"
                                >
                                    <UserListItem
                                        user={artist}
                                        className="article-item-list flex-1"
                                    />
                                    {/* <Button
                                        variant="primary"
                                        className="ml-4 text-xs py-1 px-3"
                                    >
                                        Vote
                                    </Button> */}
                                </div>
                            ))}
                        </ArticleLook>
                    </div>
                )}

                {recently_voted.length > 0 && (
                    <div className="mb-8">
                        <h2 className="text-xl font-semibold mb-4">
                            Recently Voted
                        </h2>
                        <ArticleLook
                            type="list"
                            className="mb-6 gap-6 lg:gap-0 lg:space-y-6"
                        >
                            {recently_voted.map((artist) => (
                                <div
                                    key={artist.id}
                                    className="flex items-center justify-between"
                                >
                                    <UserListItem
                                        user={artist}
                                        className="article-item-list flex-1"
                                    />
                                    {/* <Button
                                        variant="primary"
                                        className="ml-4 text-xs py-1 px-3"
                                    >
                                        Vote Again
                                    </Button> */}
                                </div>
                            ))}
                        </ArticleLook>
                    </div>
                )}
            </AppContentWrapper>
        </>
    );
};

VotingPage.layout = (page: any) => <AppLayout children={page} />;

export default VotingPage;
