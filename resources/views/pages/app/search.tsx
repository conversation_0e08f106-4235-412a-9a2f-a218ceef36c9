import { AppHead, AppLayout } from "@/components/layouts";
import { ArticleLook, InputField } from "@/components/lib/ui";
import debounce from "lodash/debounce";
import { useCallback, useMemo, useState } from "react";
import { router, usePage } from "@inertiajs/react";
import { useSyncModelState, useZiggy } from "@/hooks";
import { SearchPageProps } from "@/types/pages";
import { CategoryItem, ShowMore } from "@/components/lib";
import { SearchResult } from "@/components/pages/search";

function SearchPage() {
    const route = useZiggy();
    const { props, url } = usePage<SearchPageProps>();
    const [genres, setGenres] = useState(props.genres.data || []);

    // Sync model state
    useSyncModelState(setGenres, props.genres.data);

    const onChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        router.get(
            route("app.search"),
            { q: e.target.value.trim() },
            { replace: true, preserveState: true }
        );
    }, []);

    const query = useMemo(() => {
        const queryString = url.substring(url.indexOf("?"), url.length);
        const urlParams = new URLSearchParams(queryString);

        return urlParams.get("q") || undefined;
    }, [url]);

    return (
        <>
            <AppHead />
            <div className="w-full">
                <InputField
                    type="search"
                    autoFocus
                    defaultValue={query}
                    placeholder="What do you want to listen to ?"
                    className="w-full lg:w-1/2"
                    onChange={debounce(onChange, 700)}
                />
                <div className="my-5" />

                <div className="w-full">
                    {!query && (
                        <>
                            <h1 className="font-normal text-xl mb-4">
                                Genres available
                            </h1>

                            <ArticleLook type="card" cols={6}>
                                {genres.map((genre) => {
                                    return (
                                        <CategoryItem
                                            key={genre.id}
                                            genre={genre}
                                        />
                                    );
                                })}
                            </ArticleLook>

                            <ShowMore
                                pagination={props.genres}
                                onSuccess={() =>
                                    setGenres((ps) =>
                                        ps.concat(props.genres.data)
                                    )
                                }
                            />
                        </>
                    )}

                    {query && props.search && <SearchResult query={query} />}
                </div>
            </div>
        </>
    );
}

SearchPage.layout = (page: any) => <AppLayout children={page} />;

export default SearchPage;
