import { AppHead, AppLayout } from "@/components/layouts";
import { ShowMore } from "@/components/lib";
import { DiscoverChannelItems } from "@/components/pages/discover";
import { useSyncModelState } from "@/hooks";
import { ChannelsPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { useState } from "react";

function ChannelsPage() {
    const { props } = usePage<ChannelsPageProps>();
    const [data, setData] = useState(props.channels.data || []);

    // Sync model state
    useSyncModelState(setData, props.channels.data);

    return (
        <>
            <AppHead title={"Albums"} />

            <div className="w-full">
                <h1 className="text-xl lg:text-2xl mb-3">Albums</h1>

                <DiscoverChannelItems cols={6} channels={data} />

                <ShowMore
                    pagination={props.channels}
                    onSuccess={() =>
                        setData((ps) => ps.concat(props.channels.data))
                    }
                />
            </div>
        </>
    );
}

ChannelsPage.layout = (page: any) => <AppLayout children={page} />;

export default ChannelsPage;
