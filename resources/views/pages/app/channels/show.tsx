import { AppHead, AppLayout } from "@/components/layouts";
import { ShowMore } from "@/components/lib";
import { GradientWrapper } from "@/components/lib/ui";
import { ShowChannelProfile } from "@/components/pages/channels";
import {
    DiscoverChannelItems,
    TopChartArticles,
} from "@/components/pages/discover";
import { useSyncModelState } from "@/hooks";
import { ChannelShowPageProps } from "@/types/pages";
import { getUrlParam } from "@/utils";
import { usePage } from "@inertiajs/react";
import { useEffect, useState } from "react";

function ChannelShowPage() {
    const { props } = usePage<ChannelShowPageProps>();
    const channel = props.channel;
    const artist = props.channel.user;
    const image = props.channel.image;
    const articles = props.articles;

    const [itemsChannel, setItemChannel] = useState(
        props.more_channels.data || []
    );

    useSyncModelState(setItemChannel, props.more_channels.data);

    useEffect(() => {
        const article = getUrlParam("article");

        if (article) {
            const element = document.querySelector(
                `[data-article-id="${article}"]`
            );

            element?.scrollIntoView({ behavior: "smooth", block: "center" });

            setTimeout(() => element?.classList.add("active-item"), 700);
            setTimeout(() => element?.classList.remove("active-item"), 2000);
        }
    }, []);

    return (
        <>
            <AppHead title={channel.name} />

            <div className="w-full">
                <ShowChannelProfile />

                <div className="py-7">
                    {/* <h3 className="text-xl">
                        Audio{articles.length > 1 ? `s` : ""}
                    </h3> */}

                    <div className="mt-5">
                        <TopChartArticles articles={articles} />
                    </div>
                </div>

                {itemsChannel.length > 0 && (
                    <div className="py-5">
                        <h3 className="text-xl">More by {artist?.name}</h3>

                        <div className="my-5">
                            <DiscoverChannelItems
                                cols={6}
                                channels={itemsChannel}
                            />
                        </div>

                        <ShowMore
                            pagination={props.more_channels}
                            autoLoad={false}
                            onSuccess={() =>
                                setItemChannel((ps) =>
                                    ps.concat(props.more_channels.data)
                                )
                            }
                        />
                    </div>
                )}

                {channel.description && (
                    <GradientWrapper direction="bottom-top" image={image}>
                        <div className="font-normal text-sm opacity-80 w-full lg:w-4/5 mt-3">
                            <p className="capitalize break-words overflow-hidden">
                                {channel.description}
                            </p>
                        </div>
                    </GradientWrapper>
                )}
            </div>
        </>
    );
}

ChannelShowPage.layout = (page: any) => <AppLayout children={page} />;

export default ChannelShowPage;
