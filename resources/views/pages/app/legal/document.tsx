import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import { LegalDocument } from "@/types/models";
import { SharedAppData } from "@/types/pages";
import { usePage } from "@inertiajs/react";

type LegalDocumentPageProps = SharedAppData & {
    document: LegalDocument & {
        content: string; // HTML content
    };
};

const LegalDocumentPage = () => {
    const { props } = usePage<LegalDocumentPageProps>();
    const { document } = props;

    const getDocumentTypeLabel = (type: string) => {
        switch (type) {
            case "terms_of_service":
                return "Terms of Service";
            case "privacy_policy":
                return "Privacy Policy";
            case "competition_rules":
                return "Competition Rules";
            default:
                return "Legal Document";
        }
    };

    return (
        <>
            <AppHead title={getDocumentTypeLabel(document.type)} />

            <div className="w-full">
                <AppContentWrapper>
                    <div className="max-w-4xl mx-auto">
                        {/* Header */}
                        <div className="mb-8">
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                                {document.title}
                            </h1>
                            <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                <span>Version {document.version}</span>
                                <span>•</span>
                                <span>
                                    Effective:{" "}
                                    {new Date(
                                        document.effective_date
                                    ).toLocaleDateString()}
                                </span>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="bg-white dark:bg-gray-800 shadow-md dark:shadow-none rounded-lg p-8">
                            <div
                                className="prose prose-lg dark:prose-invert max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: document.content,
                                }}
                            />
                        </div>

                        {/* Footer */}
                        <div className="mt-8 text-center text-sm text-gray-500 dark:text-gray-400">
                            <p>
                                This document was last updated on{" "}
                                {new Date(
                                    document.updated_at ||
                                        document.created_at ||
                                        ""
                                ).toLocaleDateString()}
                                .
                            </p>
                            <p className="mt-2">
                                For questions about this document, please
                                contact{" "}
                                <a
                                    href="mailto:<EMAIL>"
                                    className="text-primary hover:underline"
                                >
                                    <EMAIL>
                                </a>
                            </p>
                        </div>
                    </div>
                </AppContentWrapper>
            </div>
        </>
    );
};

LegalDocumentPage.layout = (page: any) => <AppLayout children={page} />;

export default LegalDocumentPage;
