import { AppHead, AppLayout } from "@/components/layouts";
import { <PERSON><PERSON><PERSON><PERSON>, ShowMore, UserCard } from "@/components/lib";
import { ArticleLook, ITab, Tabs } from "@/components/lib/ui";
import {
    DiscoverChannelItems,
    TopChartArticles,
} from "@/components/pages/discover";
import { usePlayer } from "@/components/player";
import { useSyncModelState, useZiggy } from "@/hooks";
import { Article } from "@/types/models";
import { FavoritesPageProps } from "@/types/pages";
import { fetchApi } from "@/utils";
import { usePage } from "@inertiajs/react";
import { useCallback, useState } from "react";

function FavoritesPage() {
    const player = usePlayer();

    const route = useZiggy();
    const { props } = usePage<FavoritesPageProps>();
    const { articles, channels, followings } = props;

    // Local states
    const [fetchingPlay, setFetchingPlay] = useState(false);
    const [articlesData, setArticlesData] = useState(articles.data || []);
    const [channelsData, setChannelsData] = useState(channels.data || []);
    const [followingsData, setFollowingsData] = useState(followings.data || []);

    // Sync models states
    useSyncModelState(setArticlesData, articles.data);
    useSyncModelState(setChannelsData, channels.data);
    useSyncModelState(setFollowingsData, followings.data);

    const tabs: ITab[] = [
        { title: `Songs (${articles.meta.total || 0})` },
        { title: `Albums | Channels (${channels.meta.total || 0})` },
        { title: `Followings (${followings.meta.total || 0})` },
    ];
    let defaultIndex: null | number = null;

    // Order of keys is important
    const menuKeys = ["articles", "channels", "followings"] as const;

    menuKeys.forEach((key, idx) => {
        const data = props[key];
        if (data.meta.total > 0) {
            defaultIndex === null && (defaultIndex = idx);
            tabs[idx].disabled = false;
        } else {
            tabs[idx].disabled = true;
        }
    });

    const playFavorites = useCallback(() => {
        if (!player) {
            return;
        }

        setFetchingPlay(true);
        fetchApi<Article[]>(route("app.favorites.articles", { limit: 100 }))
            .then((articles) => {
                player.guessPlaylist({
                    type: "articles",
                    data: articles,
                });
            })
            .finally(() => setFetchingPlay(false));
    }, [player]);

    return (
        <>
            <AppHead title="Favorites" />

            <div className="w-full">
                <div className="flex items-center space-x-3 mb-3">
                    <h1 className="text-xl lg:text-2xl">Favorites</h1>
                    <PlayButton
                        onClick={playFavorites}
                        loading={fetchingPlay}
                    />
                </div>

                <Tabs tabs={tabs} defaultIndex={defaultIndex || 0}>
                    {(Pannel) => {
                        return (
                            <div className="mt-5">
                                {/* Articles */}
                                <Pannel>
                                    <TopChartArticles articles={articlesData} />

                                    <ShowMore
                                        pagination={articles}
                                        onSuccess={() =>
                                            setArticlesData((ps) =>
                                                ps.concat(articles.data)
                                            )
                                        }
                                    />
                                </Pannel>
                                {/* Channels */}
                                <Pannel>
                                    <DiscoverChannelItems
                                        cols={6}
                                        channels={channelsData}
                                    />

                                    <ShowMore
                                        pagination={channels}
                                        onSuccess={() =>
                                            setChannelsData((ps) =>
                                                ps.concat(channels.data)
                                            )
                                        }
                                    />
                                </Pannel>
                                {/* Followings */}
                                <Pannel>
                                    <ArticleLook
                                        className="h-max"
                                        type="card"
                                        cols={6}
                                    >
                                        {followingsData.map((user) => {
                                            return (
                                                <UserCard
                                                    layout="fill"
                                                    user={user}
                                                    key={user.id}
                                                />
                                            );
                                        })}
                                    </ArticleLook>

                                    <ShowMore
                                        pagination={followings}
                                        onSuccess={() =>
                                            setFollowingsData((ps) =>
                                                ps.concat(followings.data)
                                            )
                                        }
                                    />
                                </Pannel>
                            </div>
                        );
                    }}
                </Tabs>
            </div>
        </>
    );
}

FavoritesPage.layout = (page: any) => <AppLayout children={page} />;

export default FavoritesPage;
