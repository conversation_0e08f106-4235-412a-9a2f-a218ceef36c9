import { AppContentWrapper, AppHead, AppLayout } from "@/components/layouts";
import {
    DiscoverArticleItems,
    DiscoverChannelItems,
    DiscoverPlaylistItems,
    TopChartArtists,
} from "@/components/pages/discover";
import {
    Article,
    Channel,
    Playlist,
    PlaylistGenerationStrategy,
} from "@/types/models";
import { DiscoverPageProps } from "@/types/pages";
import { cn } from "@/utils";
import { usePage } from "@inertiajs/react";
import { useMemo } from "react";

type LPlaylistGenerationStrategy = PlaylistGenerationStrategy | "default";

const strategyTitles: Record<LPlaylistGenerationStrategy, string> = {
    default: "Discover",
    following: "From People You Follow",
    top_listened: "Your Top Tracks",
    like_similarity: "Similar to Your Likes",
    recent_play_similarity: "Similar to Your Recent Plays",
    popular_genre: "Popular in Your Genre",
    popular_global: "Most Popular",
};

type Entry =
    | {
          title: string;
          type: "playlist";
          items: Playlist[];
      }
    | {
          title: string;
          type: "article";
          items: Article[];
      }
    | {
          title: string;
          type: "channel";
          items: Channel[];
      };

function DiscoverPage() {
    const { props } = usePage<DiscoverPageProps>();

    let playlists = Array.isArray(props.playlists) ? props.playlists : [];
    let articles: Article[] = [];
    let headArticles: Article[] = [];

    const topArtists = props.top_artists || [];
    const topCharts = props.top_charts || [];
    const recentlyPlayed = props.recently_played || [];

    switch (props.source) {
        case "global_popular_guest":
        case "global_popular_fallback": {
            if (!Array.isArray(props.playlists)) {
                const playlist = Object.values(props.playlists)[0];
                if ("articles" in playlist) {
                    articles = playlist.articles as Article[];

                    if (topArtists.length > 0) {
                        headArticles = articles.slice(0, 6);
                        articles = articles.slice(6);
                    }
                }
            }
            playlists = playlists.slice(1);
            break;
        }
    }

    const list = useMemo(() => {
        const itemsList: Record<string, Playlist[]> = {};
        playlists.forEach((playlist) => {
            let type: LPlaylistGenerationStrategy =
                playlist.generation_strategy || "default";

            const inDefaultList = [
                "popular_global",
                "following",
                "top_listened",
            ];

            if (inDefaultList.includes(type) || !strategyTitles[type]) {
                type = "default";
            }

            if (!itemsList[type]) {
                itemsList[type] = [];
            }
            itemsList[type].push(playlist);
        });

        const entries: Entry[] = Object.entries(itemsList).map(
            ([key, value]) => ({
                type: "playlist",
                title: strategyTitles[key as LPlaylistGenerationStrategy],
                items: value,
            })
        );

        // Order the entries, with more items first
        entries.sort((a, b) => {
            const aCount = "items" in a ? a.items.length : 0;
            const bCount = "items" in b ? b.items.length : 0;
            return bCount - aCount;
        });

        if (articles.length > 0) {
            entries.unshift({
                title: headArticles.length ? "Trending Now" : "Top Charts",
                type: "article",
                items: articles,
            });
        }

        if (headArticles.length > 0) {
            entries.unshift({
                title: "Top Charts",
                type: "article",
                items: headArticles,
            });
        }

        if (topCharts.length > 0) {
            entries.push({
                title: "Top Charts",
                type: "article",
                items: topCharts,
            });
        }

        if (recentlyPlayed.length > 0) {
            entries.push({
                title: "Recently Played",
                type: "channel",
                items: recentlyPlayed,
            });
        }

        return entries;
    }, [playlists, articles, recentlyPlayed, topCharts, headArticles]);

    return (
        <>
            <AppHead title="Discover" />

            <AppContentWrapper>
                <div className="w-full flex flex-col space-y-10">
                    {list.map((item, index) => {
                        if (index === 0 && topArtists.length > 0) {
                            return (
                                <div className="flex space-y-10 lg:space-y-0 lg:space-x-10 flex-col lg:flex-row">
                                    <DiscoverItem
                                        key={index}
                                        item={item}
                                        cols={3}
                                        className="w-full lg:w-3/5"
                                    />

                                    <div className="w-full lg:w-2/5">
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-300 -ml-[2px]">
                                            {"Top Artists"}
                                        </h3>

                                        <div className="mt-4">
                                            <TopChartArtists
                                                artists={topArtists}
                                                showList={true}
                                            />
                                        </div>
                                    </div>
                                </div>
                            );
                        }

                        return <DiscoverItem key={index} item={item} />;
                    })}
                </div>
            </AppContentWrapper>
        </>
    );
}

function DiscoverItem({
    item,
    className,
    cols = 6,
}: {
    item: Entry;
    className?: string;
    cols?: 6 | 3;
}) {
    return (
        <div className={cn("flex flex-col gap-4", className)}>
            <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-300">
                {item.title}
            </h1>

            <div className="w-full">
                {item.type === "channel" && (
                    <DiscoverChannelItems cols={cols} channels={item.items} />
                )}

                {item.type === "playlist" && (
                    <DiscoverPlaylistItems cols={cols} playlists={item.items} />
                )}

                {item.type === "article" && (
                    <DiscoverArticleItems cols={cols} articles={item.items} />
                )}
            </div>
        </div>
    );
}

DiscoverPage.layout = (page: any) => <AppLayout children={page} />;

export default DiscoverPage;
