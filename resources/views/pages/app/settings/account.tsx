import { AppHead, AppLayout } from "@/components/layouts";
import { useToasts } from "@/components/lib";
import {
    IoLockClosed,
    IoMusicalNotes,
    IoPersonCircle,
} from "@/components/lib/icons";
import { Tabs, ITab, Alert } from "@/components/lib/ui";
import {
    AccountForm,
    ArtistRequest,
    ChangePassword,
} from "@/components/pages/account";
import { useAuthUser } from "@/hooks";
import { Link } from "@inertiajs/react";

function AccountPage() {
    const { pushToast } = useToasts();
    const user = useAuthUser();

    const tabs: ITab[] = [
        {
            title: "Profile",
            Icon: IoPersonCircle,
        },
        {
            title: "Password",
            Icon: IoLockClosed,
        },
        {
            title: "Artist request",
            Icon: IoMusicalNotes,
            active: user?.role === "GUEST",
        },
    ];

    return (
        <>
            <AppHead title="My Account" />

            <div className="w-full lg:w-4/5 xl:w-2/3">
                <h1 className="text-xl lg:text-3xl mb-4">My Account</h1>
                {!user?.has_verified_email && (
                    <Alert
                        title="Email verification!"
                        message={
                            <>
                                Please consider verifying your account email
                                before taking any further action. <br />
                                <Link
                                    method="post"
                                    href="/email/verification-notification"
                                    className="underline font-medium cursor-pointer"
                                    as="button"
                                    onSuccess={() => {
                                        pushToast({
                                            title: "A new email verification link has been emailed to you!",
                                            duration: 20,
                                        });
                                    }}
                                >
                                    Resend email
                                </Link>{" "}
                                if you did not receive it
                            </>
                        }
                        type="warning"
                    />
                )}
                <Tabs tabs={tabs}>
                    {(Panel) => {
                        return (
                            <>
                                <Panel>
                                    <AccountForm />
                                </Panel>
                                <Panel>
                                    <ChangePassword />
                                </Panel>
                                {user?.role === "GUEST" && (
                                    <Panel>
                                        <ArtistRequest />
                                    </Panel>
                                )}
                            </>
                        );
                    }}
                </Tabs>
            </div>
        </>
    );
}

AccountPage.layout = (page: any) => <AppLayout minify children={page} />;

export default AccountPage;
