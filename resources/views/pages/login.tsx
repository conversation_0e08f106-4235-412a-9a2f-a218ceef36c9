import { AppHead, AppLayout } from "@/components/layouts";
import { useZiggy } from "@/hooks";
import { router } from "@inertiajs/react";
import { useEffect } from "react";

function LoginPage() {
    return (
        <>
            <AppHead title="Login" />
            <Redirect />
        </>
    );
}

function Redirect() {
    const route = useZiggy();

    useEffect(() => {
        router.replace({ url: route("app.discover") });
    }, []);

    return <></>;
}

LoginPage.layout = (page: any) => <AppLayout children={page} />;

export default LoginPage;
