import { AppLayout } from "@/components/layouts";
import { Button, InputField } from "@/components/lib/ui";
import { useResetPassword } from "@/hooks";
import { ResetPasswordPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";

function ResetPasswordPage() {
    const { props } = usePage<ResetPasswordPageProps>();
    const { data, errors, onChange, processing, handleSubmit } =
        useResetPassword(props.token, props.email);

    return (
        <div className="flex justify-center w-full">
            <div className="w-full tablet:w-1/2 2xl:w-2/5 overflow-x-hidden">
                <div className="flex items-center flex-col w-full mt-5">
                    <h3 className="text-4xl font-medium mb-7">
                        Reset Password
                    </h3>

                    <form
                        autoComplete="off"
                        className="flex flex-col space-y-3 w-full"
                        onSubmit={handleSubmit}
                    >
                        <InputField
                            label="Email address"
                            type="email"
                            name="email"
                            value={data.email}
                            onChange={onChange}
                            errors={errors}
                        />

                        <InputField
                            label="New Password"
                            type="password"
                            name="password"
                            value={data.password}
                            onChange={onChange}
                            errors={errors}
                        />

                        <InputField
                            label="Retape your password"
                            type="password"
                            name="password_confirmation"
                            value={data.password_confirmation}
                            onChange={onChange}
                            errors={errors}
                        />

                        <div>
                            <Button
                                variant="primary"
                                loading={processing}
                                type="submit"
                            >
                                Reset password
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

ResetPasswordPage.layout = (page: any) => (
    <AppLayout forceRenderChildren children={page} />
);

export default ResetPasswordPage;
