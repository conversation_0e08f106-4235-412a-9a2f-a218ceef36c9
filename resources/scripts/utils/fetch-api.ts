import { getCookie } from "./cookies";

export function fetchApi<T>(
    url: string,
    init?: RequestInit | undefined
): Promise<T> {
    const csrfToken = document
        .querySelector('meta[name="csrf-token"]')
        ?.getAttribute("content");

    return fetch(url, {
        ...(init ? init : {}),
        headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "X-XSRF-TOKEN": getCookie("XSRF-TOKEN"),
            "X-CSRF-TOKEN": csrfToken || "",
            ...(init?.headers ? init?.headers : {}),
        },
    }).then((res) => {
        if (!res.ok) {
            return Promise.reject(res);
        }
        return res.json();
    });
}
