import { Audio, File, Image } from "@/types/models";

export function undefinefy<T>(value: T) {
    return value ?? undefined;
}

export function asset(file: File | Image | Audio | string | null | undefined) {
    if (typeof file === "string") {
        return file;
    }

    return file?.url || undefined;
}

export function getMinAndSec(seconds: number): string {
    const min = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return (
        min.toString().padStart(2, "0") + ":" + secs.toString().padStart(2, "0")
    );
}

export function getAppIdentifier() {
    if (typeof document !== "undefined") {
        const app = document.getElementById("app");
        const pageData = app?.dataset.page;

        if (pageData) {
            try {
                const json = JSON.parse(pageData);
                return (
                    json?.props?.authUser?.id || json?.props?.identifier || ""
                );
            } catch (_) {}
        }
    }

    return "";
}

export const randomImages = (items: number) => {
    return Array.from(Array(items).keys()).map(
        (key) => `https://picsum.photos/id/${key}/500`
    );
};

const SI_SYMBOL = ["", "k", "M", "G", "T", "P", "E"];
export function abbreviateNumber(number: number) {
    // what tier? (determines SI symbol)
    const tier = (Math.log10(Math.abs(number)) / 3) | 0;

    // if zero, we don't need a suffix
    if (tier == 0) return number;

    // get suffix and determine scale
    const suffix = SI_SYMBOL[tier];
    const scale = Math.pow(10, tier * 3);

    // scale the number
    const scaled = number / scale;

    // format number and add suffix
    return scaled.toFixed(1) + suffix;
}

export function getUrlParam(key: string) {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);

    return urlParams.get(key);
}
