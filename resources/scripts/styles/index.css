@import "https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap";

@import "./base.css";
@import "./lib/fps.css";

@import "./lib/animation.css";
@import "./lib/keyframes.css";

:root {
    --font-heading: "brandonText", "brandonText Fallback";
}

body {
    @apply text-base font-normal not-italic font-brand tracking-normal;
}

#nprogress .bar {
    @apply !bg-primary-400;
}

@layer components {
    .app-sidebar:before {
        /* content: "";
        z-index: 50;
        @apply hidden md:block absolute w-[1px] right-0 h-full;
        @apply from-light-primary via-primary to-light-primary via-30%;
        @apply bg-gradient-to-b dark:from-dark-primary dark:via-primary dark:via-30% dark:to-dark-primary; */
    }

    .app-sidebar {
        /* @apply border-r border-gray-300/70 dark:border-gray-700; */
    }

    .app-topbar {
        @apply relative;
        &::before {
            content: "";
            z-index: 40;
            @apply absolute h-[1px] top-0 w-full;
            @apply from-light-primary via-primary via-40% to-light-primary;
            @apply bg-gradient-to-r dark:from-dark-primary dark:via-primary dark:via-40% dark:to-dark-primary;
        }
    }

    .app-home-page {
        @apply relative bg-no-repeat;
        background-image: radial-gradient(
            circle at top,
            theme("colors.primary.50"),
            theme("colors.white") 70%
        );
    }

    .article-item-figure {
        @apply relative;

        &::after {
            content: "";
            @apply min-h-[auto] h-auto w-full block pt-[100%];
        }
    }

    .article-item-list {
        @apply relative;

        &::before {
            content: counter(li);
            counter-increment: li;
            min-width: 2.25rem;
            font-weight: 600;
            -ms-flex-negative: 0;
            flex-shrink: 0;
            opacity: 0.5;
        }
    }

    .btn-play {
        @apply transition-all;

        transition: box-shadow 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
            transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
        &:hover {
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
            transform: translate3d(0, -1px, 0);
        }
    }

    .active-item {
        @apply bg-slate-500/10 transition-all;
    }

    input[type="range"].range {
        /* removing default appearance */
        -webkit-appearance: none;
        @apply bg-gray-300 dark:bg-gray-300/30 appearance-none
               rounded-md h-1 outline-none cursor-pointer w-full;
    }

    /* Thumb: webkit */
    input[type="range"].range::-webkit-slider-thumb {
        /* removing default appearance */
        -webkit-appearance: none;
        /* creating a custom design */
        transition: 0.2s ease-in-out;
        @apply h-[14px] w-[14px] bg-primary border-none rounded-full appearance-none;
    }

    /* Thumb: Firefox */
    input[type="range"].range::-moz-range-thumb {
        transition: 0.2s ease-in-out;
        @apply h-[14px] w-[14px] bg-primary border-none rounded-full appearance-none;
    }

    /* Hover, active & focus Thumb: Webkit */
    input[type="range"].range::-webkit-slider-thumb:hover {
        @apply shadow-primary-500/20 dark:shadow-primary-200/20;
        box-shadow: 0 0 0 10px var(--tw-shadow-color);
    }

    /* Hover, active & focus Thumb: Firfox */
    input[type="range"].range::-moz-range-thumb:hover {
        @apply shadow-primary-500/20 dark:shadow-primary-200/20;
        box-shadow: 0 0 0 10px var(--tw-shadow-color);
    }
}
