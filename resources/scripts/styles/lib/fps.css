.fps__wrapper {
    display: block;
    height: 100%;
    width: 100%;
    position: absolute;
    touch-action: none;
    transform: translate3d(0px, 0px, 0px);
    transition: all 1000ms ease 0s;
}
.fps__slider {
    position: relative;
    width: 100%;
    height: 100%;
}
.fps__dots {
    position: absolute;
    z-index: 100;
    top: 50%;
    opacity: 1;
    transform: translateY(-50%);
}
.fps__dots-right {
    right: 17px;
}
.fps__dots-bottom {
    top: unset;
    bottom: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 0 auto !important;
}
.fps__dots-bottom li {
    display: inline-block !important;
}
.fps__dots ul {
    list-style: none;
}
.fps__dots li {
    display: block;
    width: 14px;
    height: 13px;
    margin: 7px;
    position: relative;
}
.fps__dots a {
    display: block;
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    text-decoration: none;
}
.fps__dots a span {
    border-radius: 50%;
    position: absolute;
    z-index: 1;
    height: 4px;
    width: 4px;
    border: 0;
    background: #333;
    left: 50%;
    top: 50%;
    margin: -2px 0 0 -2px;
    transition: all 0.1s ease-in-out;
}
.fps__dots a.active span {
    height: 12px;
    width: 12px;
    margin: -6px 0 0 -6px;
    border-radius: 100%;
}

.fps__arrow-right,
.fps__arrow-down {
    margin: 0 auto;
    width: 24px;
    height: 20px;
    cursor: pointer;
    animation: bounce 3s infinite;
    transition: opacity 1.3s ease;
    opacity: 1;
}
.fps__arrow-right svg,
.fps__arrow-down svg {
    width: 100%;
    height: 100%;
}

.fps__arrow-down {
    position: absolute;
    bottom: 23px;
    left: 0;
    right: 0;
}

.fps__arrow-right {
    position: absolute;
    right: 23px;
    top: 50%;
}

.fps__arrow-right svg {
    transform: rotate(-0.25turn);
}

/* .fps__arrow-right path {
    transform: rotate(0.5turn);
} */
