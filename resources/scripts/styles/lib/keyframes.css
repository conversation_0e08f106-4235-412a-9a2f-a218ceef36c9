@keyframes hideTextLeft {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    to {
        transform: translate3d(calc(-100% - 551px), 0, 0);
        opacity: 1;
    }
}
@keyframes hideTextToRight {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    to {
        transform: translate3d(100%, 0, 0);
        opacity: 1;
    }
}
@keyframes revealTextFromRight {
    0% {
        transform: translate3d(100%, 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}
@keyframes revealTextFromLeft {
    0% {
        transform: translate3d(calc(-100% - 551px), 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}
@keyframes revealText {
    0% {
        transform: translate3d(0, 125%, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0%, 0);
        opacity: 1;
    }
}
@keyframes hideText {
    0% {
        transform: translate3d(0, 0%, 0);
        opacity: 1;
    }
    to {
        transform: translate3d(0, 125%, 0);
        opacity: 1;
    }
}
@keyframes bounce {
    0%,
    20%,
    60%,
    80%,
    to {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    50% {
        transform: translateY(5px);
    }
}
@keyframes floating {
    0%,
    to {
        transform: translate(0%, -50%);
    }
    25% {
        transform: translate(5px, calc(-50% + 15px));
    }
    50% {
        transform: translate(10px, calc(-50% + 5px));
    }
    75% {
        transform: translate(0%, calc(-50% + 15px));
    }
}
@keyframes rubberBand {
    0%,
    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1);
    }
    3% {
        -webkit-transform: scale3d(1.25, 0.75, 1);
        transform: scale3d(1.25, 0.75, 1);
    }
    20% {
        -webkit-transform: scale3d(0.75, 1.25, 1);
        transform: scale3d(0.75, 1.25, 1);
    }
    40% {
        -webkit-transform: scale3d(1.15, 0.85, 1);
        transform: scale3d(1.15, 0.85, 1);
    }
    55% {
        -webkit-transform: scale3d(0.95, 1.05, 1);
        transform: scale3d(0.95, 1.05, 1);
    }
    75% {
        -webkit-transform: scale3d(1.05, 0.95, 1);
        transform: scale3d(1.05, 0.95, 1);
    }
}
@keyframes heartbeat {
    50% {
        transform: scale(1.1);
    }
}
@keyframes rainbow {
    0% {
        background: #1abc9c;
    }
    10% {
        background: #2ecc71;
    }
    20% {
        background: #3498db;
    }
    30%,
    80% {
        background: #9b59b6;
    }
    40% {
        background: #e74c3c;
    }
    50% {
        background: #e67e22;
    }
    60% {
        background: #f1c40f;
    }
    70% {
        background: #2c3e50;
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}
