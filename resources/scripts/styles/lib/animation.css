.anim-wrapper {
    .logo-anime {
        animation-name: hideText;
        animation-duration: 1550ms;
        animation-delay: 0ms;
        opacity: 1;

        &.anime-left {
            animation-name: hideTextLeft;
        }
    }

    &.active .logo-anime {
        animation-timing-function: cubic-bezier(0.52, 0.01, 0.16, 1);
        animation-name: revealText;
        animation-duration: 1550ms;
        animation-fill-mode: forwards;
        animation-delay: 0ms;
        opacity: 0;

        &.anime-left {
            animation-name: revealTextFromLeft;
        }
    }
}
