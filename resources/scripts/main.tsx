import { createInertiaApp } from "@inertiajs/react";
import { hydrateRoot } from "react-dom/client";
import { Providers } from "./providers";

createInertiaApp({
    resolve: (name) => {
        const pages = import.meta.glob("../views/pages/**/*.tsx", {
            eager: true,
        });
        return pages[`../views/pages/${name}.tsx`];
    },
    setup({ el, App, props }) {
        hydrateRoot(
            el,
            <Providers>
                <App {...props} />
            </Providers>
        );
    },
});
