import { Fragment } from "react";
import { useRegisterSW } from "virtual:pwa-register/react";
import { Dialog, Transition } from "@headlessui/react";

// check for updates 10 minutes
const period = 10 * 60 * 1000;

function ReloadPrompt() {
    const {
        offlineReady: [offlineReady, setOfflineReady],
        needRefresh: [needRefresh, setNeedRefresh],
        updateServiceWorker,
    } = useRegisterSW({
        onRegistered(r) {
            console.log("SW Registered: " + r);
        },
        onRegisterError(error) {
            console.log("SW registration error", error);
        },

        onRegisteredSW(swUrl, r) {
            if (period <= 0) {
                return;
            }

            if (r?.active?.state === "activated") {
                registerPeriodicSync(period, swUrl, r);
            } else if (r?.installing) {
                r.installing.addEventListener("statechange", (e) => {
                    const sw = e.target as ServiceWorker;
                    if (sw.state === "activated") {
                        registerPeriodicSync(period, swUrl, r);
                    }
                });
            }
        },
    });

    const showDialog = needRefresh;

    const close = () => {
        setOfflineReady(false);
        setNeedRefresh(false);
    };

    const handleReload = () => {
        updateServiceWorker(true);
    };

    return (
        <Transition appear show={showDialog} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={close}>
                {/* Overlay */}
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div
                        className="fixed inset-0 bg-black/40"
                        aria-hidden="true"
                    />
                </Transition.Child>

                {/* Dialog Panel Container */}
                <div className="fixed inset-0 flex items-center justify-center p-4">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                            <Dialog.Title
                                as="h3"
                                className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100"
                            >
                                {offlineReady
                                    ? "Application Ready for Offline Use"
                                    : "Update Available"}
                            </Dialog.Title>

                            <div className="mt-2">
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {offlineReady
                                        ? "The application has been cached and is ready to work offline."
                                        : "New content is available. Please reload to apply the update."}
                                </p>
                            </div>

                            <div className="mt-5 sm:mt-6 flex flex-col sm:flex-row sm:justify-end sm:gap-x-3 gap-y-3">
                                {needRefresh && (
                                    <button
                                        type="button"
                                        className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-800 transition-colors sm:order-last"
                                        onClick={handleReload}
                                    >
                                        Reload
                                    </button>
                                )}
                                <button
                                    type="button"
                                    className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-800 transition-colors"
                                    onClick={close}
                                >
                                    Close
                                </button>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    );
}

export default ReloadPrompt;

/**
 * This function will register a periodic sync check every hour, you can modify the interval as needed.
 */
function registerPeriodicSync(
    period: number,
    swUrl: string,
    r: ServiceWorkerRegistration
) {
    if (period <= 0) return;

    setInterval(async () => {
        if ("onLine" in navigator && !navigator.onLine) return;

        const resp = await fetch(swUrl, {
            cache: "no-store",
            headers: {
                cache: "no-store",
                "cache-control": "no-cache",
            },
        });

        if (resp?.status === 200) await r.update();
    }, period);
}
