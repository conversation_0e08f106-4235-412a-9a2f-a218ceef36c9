import React, { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";

interface BeforeInstallPromptEvent extends Event {
    readonly platforms: string[];
    readonly userChoice: Promise<{
        outcome: "accepted" | "dismissed";
        platform: string;
    }>;
    prompt(): Promise<void>;
}

const isLikelyMobileDevice = (): boolean => {
    const hasTouch =
        navigator.maxTouchPoints > 0 ||
        window.matchMedia("(pointer: coarse)").matches;

    const isSmallScreen = window.screen.width < 768;

    return hasTouch && isSmallScreen;
};

const InstallPwaModal: React.FC = () => {
    const [deferredPrompt, setDeferredPrompt] =
        useState<BeforeInstallPromptEvent | null>(null);

    const [isModalOpen, setIsModalOpen] = useState(false);

    const closeModal = () => {
        setIsModalOpen(false);
    };

    useEffect(() => {
        const handleBeforeInstallPrompt = (e: Event) => {
            e.preventDefault();

            setDeferredPrompt(e as BeforeInstallPromptEvent);
            console.log("`beforeinstallprompt` event fired and stashed.");

            const isStandalone = window.matchMedia(
                "(display-mode: standalone)"
            ).matches;

            const isMobile = isLikelyMobileDevice();

            if (isStandalone) {
                // App is in standalone mode. Modal kept hidden.
                setIsModalOpen(false);
            } else if (isMobile) {
                // Device is mobile and not standalone. Showing install modal."
                setIsModalOpen(true);
            } else {
                // Device is desktop and not standalone. Modal suppressed.
                setIsModalOpen(false);
            }
        };

        const handleAppInstalled = () => {
            console.log("PWA was installed via `appinstalled` event.");
            closeModal();
            setDeferredPrompt(null);
        };

        if (window.matchMedia("(display-mode: standalone)").matches) {
            console.log("App is already running in standalone mode on mount.");
            setIsModalOpen(false);
        }

        window.addEventListener(
            "beforeinstallprompt",
            handleBeforeInstallPrompt
        );
        window.addEventListener("appinstalled", handleAppInstalled);

        return () => {
            window.removeEventListener(
                "beforeinstallprompt",
                handleBeforeInstallPrompt
            );
            window.removeEventListener("appinstalled", handleAppInstalled);
        };
    }, []);

    const handleInstallClick = async () => {
        if (!deferredPrompt) {
            console.log("Install prompt event not available.");
            return;
        }

        closeModal();

        deferredPrompt.prompt();

        try {
            const { outcome } = await deferredPrompt.userChoice;
            console.log(
                `User response to the browser install prompt: ${outcome}`
            );

            setDeferredPrompt(null);
        } catch (error) {
            console.error("Error handling install prompt:", error);

            setDeferredPrompt(null);
        }
    };

    if (!deferredPrompt) {
        return null;
    }

    return (
        <Transition appear show={isModalOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={closeModal}>
                {/* Overlay */}
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div
                        className="fixed inset-0 bg-black/30"
                        aria-hidden="true"
                    />
                </Transition.Child>

                {/* Dialog Panel Container */}
                <div className="fixed inset-0 flex items-center justify-center p-4">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                            <Dialog.Title
                                as="h3"
                                className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100"
                            >
                                Install Application
                            </Dialog.Title>
                            <div className="mt-2">
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Add this application to your home screen for
                                    easy access and offline use.
                                </p>
                            </div>

                            <div className="mt-5 sm:mt-6 flex flex-col sm:flex-row sm:justify-end sm:gap-x-3 gap-y-3">
                                <button
                                    type="button"
                                    className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-800 transition-colors sm:order-last"
                                    onClick={handleInstallClick}
                                >
                                    Install
                                </button>
                                <button
                                    type="button"
                                    className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-800 transition-colors"
                                    onClick={closeModal}
                                >
                                    Not Now
                                </button>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    );
};

export default InstallPwaModal;
