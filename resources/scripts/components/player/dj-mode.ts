import { dj<PERSON>ode<PERSON><PERSON>, playlist<PERSON>tom } from "./player-init";
import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { usePlayer } from "./hook";
import { useRefSync, useZiggy } from "@/hooks";
import { fetchApi } from "@/utils";
import { Article } from "@/types/models";

const PLAYLIST_LIMIT = 100;

/**
 * DJ Mode is a mode that allows the player to automatically fetch the next track
 * based on the current track and the playlist.
 *
 * @param {Object} options
 * @param {boolean} options.canUpdatePlayer - If true, the player will be updated with the new track.
 * @returns {Object}
 */
export function useDjMode({ canUpdatePlayer = false } = {}) {
    const player = usePlayer();
    const pending = useRef(false);

    const setPlaylist = useSetAtom(playlistAtom);
    const [djMode, setDjMode] = useAtom(djModeAtom);

    const route = useZiggy();

    const playlist = useMemo(() => player?.playlist || [], [player?.playlist]);
    const $playlist = useRefSync(playlist);

    const currentTrackIndex = player?.currentTrackIndex || 0;
    const currentTrack = player?.currentTrack;
    const playing = player?.isPlaying;

    const hasNext = useMemo(() => {
        if (playlist.length === 0) return false;
        return currentTrackIndex < playlist.length - 1;
    }, [playlist, currentTrackIndex]);

    const getDjNextTrack = useCallback((currentTrackId: string) => {
        const ids = $playlist.current
            .map((item) => item.id)
            .filter((id) => id !== currentTrackId);

        // make sure the current track is the last
        ids.push(currentTrackId);

        return fetchApi<Article>(route("player.next-dj-track"), {
            method: "POST",
            body: JSON.stringify({ played_article_ids: ids }),
        })
            .then((data) => {
                if (!ids.includes(data.id)) {
                    player?.actions.addTrack(data);
                }

                setTimeout(() => {
                    // If playlist has more that PLAYLIST_LIMIT items, then take only the last PLAYLIST_LIMIT items
                    if ($playlist.current.length > PLAYLIST_LIMIT) {
                        setPlaylist((items) => items.slice(-PLAYLIST_LIMIT));
                    }
                }, 10);
                return data;
            })
            .finally(() => {
                pending.current = false;
            });
    }, []);

    useEffect(() => {
        if (!canUpdatePlayer || !currentTrack || !djMode || !playing) {
            return;
        }

        if (!hasNext && pending.current === false) {
            pending.current = true;
            getDjNextTrack(currentTrack.id);
        }
    }, [canUpdatePlayer, hasNext, djMode, playing, currentTrack]);

    const toggleDjMode = useCallback(() => {
        setDjMode((v) => !v);
    }, []);

    return {
        hasNext,
        djMode,
        toggleDjMode,
    };
}
