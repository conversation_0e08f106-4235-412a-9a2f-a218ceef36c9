import {
    <PERSON><PERSON>,
    LineProgressIn<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@components/lib/ui";
import {
    IoPause,
    IoPlaySkipForwardSharp,
    IoPlaySkipBackSharp,
    IoHeartOutline,
    IoVolumeMedium,
    IoPlay,
    IoHeart,
    IoVolumeOff,
    IoChatboxEllipsesOutline,
    LuRepeat,
    LuRepeat1,
    LuShuffle,
    IoVolumeMute,
    SiDjango,
} from "@/components/lib/icons";
import { asset, cn } from "@/utils";
import { useItemLike, useModal, useZiggy } from "@/hooks";
import { Link } from "@inertiajs/react";
import { Article } from "@/types/models";
import { usePlayer, formatTime } from "./hook";
import { QueuePlaylist } from "../lib/queue-playlist";
import { Comments } from "../lib";
import { RecordPlaying } from "./record-playing";
import { useDjMode } from "./dj-mode";
import { MusicPlayerModalButton } from "./mobile-player";
import React from "react";

export function MusicPlayer() {
    const route = useZiggy();
    const { likeCall } = useItemLike();

    // Player
    const player = usePlayer();
    const currentTrack = player?.currentTrack;
    const trackUser = currentTrack?.user;
    const featurings = currentTrack?.featurings || [];

    const image =
        currentTrack?.image || currentTrack?.channel.image || trackUser?.image;

    let channelLik: string | null = null;
    if (currentTrack) {
        channelLik = route("app.channels.show", {
            channel: currentTrack.channel.id,
            article: currentTrack.id,
        });
    }

    // Enable DJ mode
    const { toggleDjMode, djMode } = useDjMode({ canUpdatePlayer: true });

    return (
        <>
            <RecordPlaying />

            <div
                className={
                    "py-3 hidden sm:flex justify-between items-center [&_button]:outline-none"
                }
            >
                <div className="flex items-end lg:items-center space-x-3">
                    {trackUser ? (
                        <Link href={channelLik || ""}>
                            <Avatar
                                imageTitle={currentTrack?.name}
                                src={asset(image)}
                                alt={currentTrack?.name}
                                className={cn(
                                    "bg-light-primary dark:bg-dark-primary cursor-pointer",
                                    "size-10 lg:size-14"
                                )}
                            />
                        </Link>
                    ) : (
                        <Avatar
                            imageTitle="-"
                            className={cn(
                                "bg-light-primary dark:bg-dark-primary cursor-pointer",
                                "size-10 lg:size-14"
                            )}
                        />
                    )}

                    <div className="hidden w-36 lg:block">
                        <h2
                            title={currentTrack?.name}
                            className={cn(
                                "font-bold text-sm md:text-base text-dark dark:text-light leading-[0.9rem]",
                                "whitespace-nowrap text-ellipsis overflow-x-hidden relative",
                                channelLik && [
                                    "hover:text-primary-600 dark:hover:text-primary",
                                ]
                            )}
                        >
                            {/* Music name */}
                            {channelLik ? (
                                <Link href={channelLik}>
                                    {currentTrack?.name}
                                </Link>
                            ) : (
                                <>{currentTrack?.name || "--"}</>
                            )}
                        </h2>

                        <p
                            title={trackUser?.name}
                            className={cn(
                                "text-xs select-none whitespace-nowrap text-ellipsis overflow-x-hidden"
                            )}
                        >
                            {/* Artist name */}
                            {trackUser ? (
                                <Link
                                    href={route("app.artists.show", {
                                        user: trackUser.id,
                                    })}
                                    className="hover:text-primary-600 dark:hover:text-primary"
                                >
                                    {trackUser?.name}
                                </Link>
                            ) : (
                                "--"
                            )}

                            {featurings.map((artist) => {
                                return (
                                    <React.Fragment key={artist.id}>
                                        <span>{", "}</span>
                                        <Link
                                            title={artist.name}
                                            className="hover:text-primary-600 dark:hover:text-primary"
                                            href={route("app.artists.show", {
                                                user: artist.id,
                                            })}
                                        >
                                            {artist.name}
                                        </Link>
                                    </React.Fragment>
                                );
                            })}
                        </p>
                    </div>
                </div>

                <AudioController />

                {/* Speaker and DJ Mode */}
                <div
                    className={cn(
                        "flex items-center space-x-3"
                        // "border-l border-gray-300/70 dark:border-gray-700 xs:border-none xs:pl-0"
                    )}
                >
                    <button
                        onClick={toggleDjMode}
                        className={cn(
                            "outline-none",
                            djMode
                                ? ["text-primary-600"]
                                : ["hover:text-dark dark:hover:text-light"]
                        )}
                    >
                        <SiDjango
                            title={
                                djMode ? "Disable DJ Mode" : "Enable DJ Mode"
                            }
                            className="h-5 w-5"
                        />
                    </button>

                    <div
                        className={cn(
                            "hidden items-center space-x-2 md:flex",
                            "border-r border-gray-300/70 dark:border-gray-700 pr-3"
                        )}
                    >
                        {/* Speacker icon */}
                        <button
                            className="outline-none"
                            onClick={player?.actions.toggleMute}
                            title={player?.isMuted ? "Unmute" : "Mute"}
                        >
                            {player?.volume === 0 || player?.isMuted ? (
                                player?.isMuted ? (
                                    <IoVolumeMute className="h-5 w-5" />
                                ) : (
                                    <IoVolumeOff className="h-5 w-5" />
                                )
                            ) : (
                                <IoVolumeMedium className="h-5 w-5" />
                            )}
                        </button>

                        {/* Volume progress */}
                        <LineProgressInputRange
                            min={0}
                            max={1}
                            disabled={!player}
                            value={player?.volume || 0}
                            onChange={player?.actions.updateVolume}
                            className={cn("max-w-16 min-w-16")}
                        />
                    </div>

                    {/* Like button */}
                    <button
                        title={currentTrack?.liked ? "Unlike" : "Like"}
                        disabled={!player}
                        className={cn(
                            "outline-none",
                            currentTrack?.liked && [
                                "text-primary-500 dark:text-primary-50",
                            ]
                        )}
                        onClick={() => {
                            if (currentTrack) {
                                likeCall(
                                    { type: "article", data: currentTrack },
                                    true
                                );
                            }
                        }}
                    >
                        {currentTrack?.liked ? (
                            <IoHeart className="h-5 w-5" />
                        ) : (
                            <IoHeartOutline className="h-5 w-5" />
                        )}
                    </button>

                    {/* Playlist */}
                    <QueuePlaylist />

                    {/* Comment */}
                    <CommentSt article={currentTrack || undefined} />
                </div>
            </div>

            {/* Mobile player */}
            <MusicPlayerModalButton className="flex sm:hidden w-full" />
            <PlayerProgress className="hidden sm:flex lg:hidden w-full" />
        </>
    );
}

function AudioController() {
    const player = usePlayer();
    const { djMode, hasNext: iHasNext } = useDjMode();

    const hasNext = djMode ? iHasNext : player?.actions.hasNext;
    const hasPrev = player?.actions.hasPrev;
    const repeatState = player?.repeat;
    const isShuffle = player?.shuffle;

    let repeatTitle = "";
    switch (repeatState) {
        case "none":
            repeatTitle = "Repeat";
            break;
        case "one":
            repeatTitle = "Repeat One";
            break;
        case "all":
            repeatTitle = "Repeat All";
            break;
    }

    return (
        <div
            className={cn(
                "flex flex-col items-center w-full max-w-xl",
                "-mr-2 xxs:-mr-7 sm:-mr-16 lg:-mr-0"
            )}
        >
            <div
                className={"flex items-center justify-center space-x-4 xs:py-2"}
            >
                {/* Shuffle button */}
                <button
                    title={isShuffle ? "Disable Shuffle" : "Enable Shuffle"}
                    disabled={!player}
                    className={cn(
                        "outline-none",
                        isShuffle && ["text-primary-600"],
                        !isShuffle && ["hover:text-dark dark:hover:text-light"]
                    )}
                    onClick={() => player?.actions.toggleShuffle()}
                >
                    <LuShuffle className="h-5 w-5" />
                </button>

                {/* Prev play button */}
                <button
                    disabled={!hasPrev}
                    title="Previous"
                    className="outline-none"
                    onClick={() => player?.actions.playPrevTrack()}
                >
                    <IoPlaySkipBackSharp
                        className={cn(
                            "text-dark dark:text-light text-xl cursor-pointer transition-colors",
                            hasPrev && [
                                "hover:text-dark/80 dark:hover:text-light/80",
                            ],
                            !hasPrev && ["opacity-70 cursor-default"]
                        )}
                    />
                </button>

                {/* Play/Pause button */}
                <button
                    className={cn(
                        "flex items-center justify-center h-8 w-8 bg-primary-500 dark:bg-primary cursor-pointer",
                        "rounded-full outline-none",
                        "disabled:opacity-70 disabled:cursor-default"
                    )}
                    title={player?.isPlaying ? "Pause" : "Play"}
                    onClick={() => player?.actions.togglePlay()}
                    disabled={player?.loading || !player?.currentTrack}
                >
                    {player?.loading && (
                        <SpinnerLoader size={16} variant={"light"} />
                    )}

                    {!player?.loading && (
                        <>
                            {player?.isPlaying ? (
                                <IoPause className="text-dark dark:text-light text-xl" />
                            ) : (
                                <IoPlay className="text-dark dark:text-light text-xl ml-1" />
                            )}
                        </>
                    )}
                </button>

                {/* Next play button */}
                <button
                    disabled={!hasNext}
                    title="Next"
                    className="outline-none"
                    onClick={() => player?.actions.playNextTrack()}
                >
                    <IoPlaySkipForwardSharp
                        className={cn(
                            "text-dark dark:text-light text-xl cursor-pointer transition-colors",
                            hasNext && [
                                "hover:text-dark/80 dark:hover:text-light/80",
                            ],
                            !hasNext && ["opacity-70 cursor-default"]
                        )}
                    />
                </button>

                {/* Repeat button */}
                <button
                    title={repeatTitle}
                    disabled={!player}
                    className={cn(
                        "outline-none",
                        repeatState !== "none" && ["text-primary-600"],
                        repeatState === "none" && [
                            "hover:text-dark dark:hover:text-light",
                        ]
                    )}
                    onClick={() => player?.actions.toggleRepeat()}
                >
                    {repeatState === "none" && <LuRepeat className="h-5 w-5" />}

                    {repeatState === "one" && <LuRepeat1 className="h-5 w-5" />}

                    {repeatState === "all" && <LuRepeat className="h-5 w-5" />}
                </button>
            </div>

            <PlayerProgress className="hidden lg:flex w-full" />
        </div>
    );
}

function PlayerProgress({ className }: { className?: string }) {
    const player = usePlayer({ listenToSeek: true });
    const audio = player?.currentTrack?.audio;
    const totalDuration = player?.duration || audio?.duration || 0;

    return (
        <div className={cn("flex items-center w-full space-x-3", className)}>
            <div className="block">
                {player?.formattedCurrentTime || "00:00"}
            </div>
            <LineProgressInputRange
                min={0}
                value={player?.currentTime || 0}
                max={totalDuration}
                disabled={!player}
                onReleased={(seek) => player?.actions.seekTo(seek)}
                className="rounded-md w-full"
            />
            <div className="block">{formatTime(totalDuration) || "00:00"}</div>
        </div>
    );
}

function CommentSt({ article }: { article?: Article }) {
    const modal = useModal();

    return (
        <>
            <button
                title="Comments"
                className={cn(
                    "outline-none",
                    !article ? "cursor-default" : undefined
                )}
                onClick={article ? modal.openModal : undefined}
            >
                <IoChatboxEllipsesOutline className="h-5 w-5" />
            </button>

            {/* Comment wrapper */}
            {article && (
                <Comments
                    key={article.id}
                    modal={modal}
                    item={{ type: "channel", data: article.channel }}
                />
            )}
        </>
    );
}
