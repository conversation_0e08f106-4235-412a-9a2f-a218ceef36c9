import { PropsWithChildren, useRef } from "react";
import {
    defaultPlayerActions,
    PlayerActionsContext,
    PlayerActionsType,
    PlayerInit,
} from "./player-init";

export function PlayerProvider({ children }: PropsWithChildren) {
    const playerActionsRef = useRef<PlayerActionsType>(defaultPlayerActions);

    return (
        <PlayerActionsContext.Provider value={playerActionsRef.current}>
            <PlayerInit ref={playerActionsRef} />
            {children}
        </PlayerActionsContext.Provider>
    );
}
