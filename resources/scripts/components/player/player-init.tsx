import { useRefSync, useZiggy } from "@/hooks";
import { Article } from "@/types/models";
import { asset, getAppIdentifier } from "@/utils";
import Hls from "hls.js";
import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import {
    createContext,
    forwardRef,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from "react";

// ##########################  UTILS  ##########################

const prefix = (v: string) => {
    let prefix = "app-store-cache-" + v;

    const identifier = getAppIdentifier();
    if (identifier) {
        prefix = identifier + "-" + prefix;
    }

    return prefix;
};

// ##########################  STATE  ##########################

const audioAtom = atom<HTMLAudioElement>(() => {
    const audio = typeof Audio !== "undefined" ? new Audio() : null;
    return audio!;
});

const currentTimeAtom = atomWithStorage(prefix("player-current-time"), 0);

export const isPlayingAtom = atom(false);
export const loadingAtom = atom(false);
export const errorAtom = atom<string | null>(null);

type SeekSubscriber = (seek: number) => void;
export const seekSubscribersAtom = atom<Set<SeekSubscriber>>(
    () => new Set<SeekSubscriber>()
);

type RepeatMode = "none" | "all" | "one";

export const playerStateAtom = atomWithStorage(prefix("player-state"), {
    volume: 1,
    isMuted: false,
    duration: 0,
    repeat: "none" as RepeatMode,
    shuffle: false,
    currentTrackIndex: 0,
});

export const playlistAtom = atomWithStorage<Article[]>(
    prefix("player-playlist"),
    []
);

export const playlistIdAtom = atomWithStorage<string | null>(
    prefix("player-playlist-id"),
    null
);

// ##########################  ADDITIONAL State  ###############

export const djModeAtom = atomWithStorage(prefix("player-dj-mode"), false);

// ##########################  Context  ##########################

export type PlayerActionsType = {
    hasNext: boolean;
    hasPrev: boolean;
    play: VoidFunction;
    pause: VoidFunction;
    togglePlay: VoidFunction;
    toggleShuffle: VoidFunction;
    toggleRepeat: VoidFunction;
    playNextTrack: VoidFunction;
    playPrevTrack: VoidFunction;
    toggleMute: VoidFunction;
    seekTo: (seconds: number) => void;
    updateVolume: (volume: number) => void;
    playTrack: (index: number) => void;
    addTrack: (track: Article) => void;
    removeTrack: (index: number) => void;
    setNewPlaylist: (playlist: Article[], trackIndex?: number) => void;
};

export const defaultPlayerActions: PlayerActionsType = {
    hasNext: false,
    hasPrev: false,
    play: () => {},
    pause: () => {},
    togglePlay: () => {},
    toggleMute: () => {},
    toggleShuffle: () => {},
    toggleRepeat: () => {},
    seekTo: () => {},
    updateVolume: () => {},
    playTrack: () => {},
    playNextTrack: () => {},
    playPrevTrack: () => {},
    addTrack: () => {},
    removeTrack: () => {},
    setNewPlaylist: () => {},
};

export const PlayerActionsContext = createContext<PlayerActionsType | null>(
    defaultPlayerActions
);

export const usePlayerActions = () => {
    const actions = useContext(PlayerActionsContext);
    if (!actions) {
        throw new Error(
            "usePlayerActions must be used within a PlayerProvider"
        );
    }
    return actions;
};

// ##########################  Init  ##########################

export const PlayerInit = forwardRef<PlayerActionsType>((_, ref) => {
    const route = useZiggy();
    const hls = useRef<Hls | null>(null);

    const audio = useAtomValue(audioAtom);
    const [_playlist, setPlaylist] = useAtom(playlistAtom);
    const shuffledIndices = useRef<number[]>([]);
    const [initialLoad, setInitialLoad] = useState(false);

    // Playback state
    const [currentTime, setCurrentTime] = useAtom(currentTimeAtom);
    const [isPlaying, setIsPlaying] = useAtom(isPlayingAtom);
    const $isPlaying = useRefSync(isPlaying);

    // Player state
    const [state, setState] = useAtom(playerStateAtom);
    const $state = useRefSync(state);

    const setLoading = useSetAtom(loadingAtom);
    const setError = useSetAtom(errorAtom);

    // additional state
    const [djMode, setDjMode] = useAtom(djModeAtom);

    // Seek state
    const $seekSubscribers = useRefSync(useAtomValue(seekSubscribersAtom));

    const playlist = useMemo(
        () => _playlist.filter((a) => a.audio),
        [_playlist]
    );

    const playlistIds = useMemo(() => {
        return playlist.map((a) => a.id).join("-");
    }, [playlist]);

    const currentTrack: Article | null = useMemo(() => {
        return playlist[state.currentTrackIndex];
    }, [state.currentTrackIndex, playlistIds]);

    const audioPlay = useCallback(() => {
        audio.play().catch((err) => {
            setError(err.message);
            console.error("Error playing audio:", err);
        });
    }, [audio]);

    const updateCurrentTime = useCallback((time: number) => {
        setCurrentTime(time);
        $seekSubscribers.current.forEach((cb) => cb(time));
    }, []);

    const hasNext = useMemo(() => {
        if (playlist.length === 0) return false;

        return (
            state.currentTrackIndex < playlist.length - 1 ||
            state.repeat !== "none"
        );
    }, [state.repeat, state.currentTrackIndex, playlist]);

    const hasPrev = useMemo(() => {
        return state.currentTrackIndex > 0;
    }, [state.currentTrackIndex]);

    useEffect(() => {
        if (state.shuffle) {
            setDjMode(false);
        }
    }, [state.shuffle]);

    useEffect(() => {
        if (djMode) {
            setState((prev) => ({
                ...prev,
                shuffle: false,
            }));
        }
    }, [djMode]);

    // Initialize audio
    useEffect(() => {
        if (!currentTrack || !currentTrack.audio) {
            setInitialLoad(true);
            return;
        }

        // Clean up previous HLS instance
        if (hls.current) {
            hls.current.destroy();
            hls.current = null;
        }

        const audioFile = currentTrack.audio;

        // Reset states
        setState((prev) => ({
            ...prev,
            duration: audioFile?.duration || 0,
        }));
        setError(null);
        setLoading(false);

        // Initialize HLS
        const hlsEnabled = audioFile?.hls_enabled && audioFile?.hls_directory;
        const hlsSupported = hlsEnabled && Hls.isSupported();

        if (hlsSupported) {
            hls.current = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
            });
            const hlsRef = hls.current;

            const streamUrl = route("hls", {
                file: audioFile.id,
                filename: "master.m3u8",
            });

            hlsRef.loadSource(streamUrl);
            hlsRef.attachMedia(audio);
            setLoading(true);

            hlsRef.on(Hls.Events.MANIFEST_PARSED, () => {
                setLoading(false);
                if ($isPlaying.current) {
                    audioPlay();
                }
            });

            hlsRef.on(Hls.Events.AUDIO_TRACK_LOADING, () => {
                setLoading(true);
            });

            hlsRef.on(Hls.Events.AUDIO_TRACK_LOADED, () => {
                setLoading(false);
            });

            hlsRef.on(Hls.Events.LEVEL_LOADING, () => {
                setLoading(true);
            });

            hlsRef.on(Hls.Events.LEVEL_LOADED, () => {
                setLoading(false);
            });

            hlsRef.on(Hls.Events.ERROR, (_, data) => {
                setLoading(false);
                if (data.fatal) {
                    switch (data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            // Try to recover network error
                            hlsRef.startLoad();
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            // Try to recover media error
                            hlsRef.recoverMediaError();
                            break;
                        default:
                            // Cannot recover
                            hlsRef.destroy();
                            audio.pause();
                            console.error(`HLS fatal error: ${data.details}`);
                            break;
                    }
                }
            });
        } else {
            // Fallback to audio file
            if (
                hlsEnabled &&
                audio.canPlayType("application/vnd.apple.mpegurl")
            ) {
                audio.src = route("hls", {
                    file: audioFile.id,
                    filename: "master.m3u8",
                });
            } else if (audioFile?.url) {
                audio.src = audioFile.url;
            }

            // Play audio
            if ($isPlaying.current) {
                audioPlay();
            }
        }

        setInitialLoad(true);

        // Clean up when component unmounts
        return () => {
            if (hls.current) {
                hls.current.destroy();
                hls.current = null;
            } else {
                audio?.pause();
            }
        };
    }, [currentTrack]);

    // Handle play state changes
    useEffect(() => {
        if (isPlaying) {
            audioPlay();
        } else {
            audio.pause();
        }
    }, [isPlaying]);

    // Set current time on initial load
    useEffect(() => {
        if (audio && currentTime && initialLoad) {
            audio.currentTime = currentTime;
        }
    }, [initialLoad]);

    const handleEnded = () => {
        const state = $state.current;
        if (
            state.repeat === "one" ||
            (state.repeat === "all" && playlist.length === 1)
        ) {
            // Repeat current track
            if (audio) {
                audio.currentTime = 0;
                updateCurrentTime(0);
                audioPlay();
            }
        } else if (
            state.repeat === "all" ||
            (state.shuffle && playlist.length > 1)
        ) {
            // Go to next track
            playNextTrack();
        } else if (state.currentTrackIndex < playlist.length - 1) {
            // Go to next track if not the last one
            playNextTrack();
        } else {
            // Stop at the end of playlist
            setIsPlaying(false);
        }
    };

    const $handleEnded = useRefSync(handleEnded);

    // Event handlers
    useEffect(() => {
        const handlePlay = () => {
            setIsPlaying(true);
        };

        const handlePause = () => {
            setIsPlaying(false);
        };

        const handleMetadata = () => {
            setState((prev) => ({
                ...prev,
                duration: isNaN(audio.duration) ? 0 : audio.duration,
            }));
        };

        const handleTimeUpdate = () => {
            const duration = $state.current.duration;
            updateCurrentTime(audio.currentTime);

            if (isNaN(duration) || duration === 0) {
                setState((prev) => ({
                    ...prev,
                    duration: isNaN(audio.duration) ? 0 : audio.duration,
                }));
            }
        };

        const handleEnded = () => {
            $handleEnded.current();
        };

        const handleLoadStart = () => {
            setLoading(true);
        };

        const handleLoadEnd = () => {
            setLoading(false);
        };

        const handleError = (err: ErrorEvent) => {
            setLoading(false);
            setError(err.message);
        };

        audio.addEventListener("play", handlePlay);
        audio.addEventListener("pause", handlePause);
        audio.addEventListener("timeupdate", handleTimeUpdate);
        audio.addEventListener("loadedmetadata", handleMetadata);
        audio.addEventListener("ended", handleEnded);
        audio.addEventListener("loadstart", handleLoadStart);
        audio.addEventListener("loadeddata", handleLoadEnd);
        audio.addEventListener("error", handleError);
        audio.addEventListener("abort", handleLoadEnd);

        return () => {
            audio.removeEventListener("play", handlePlay);
            audio.removeEventListener("pause", handlePause);
            audio.removeEventListener("timeupdate", handleTimeUpdate);
            audio.removeEventListener("loadedmetadata", handleMetadata);
            audio.removeEventListener("ended", handleEnded);
            audio.removeEventListener("loadstart", handleLoadStart);
            audio.removeEventListener("loadeddata", handleLoadEnd);
            audio.removeEventListener("error", handleError);
            audio.removeEventListener("abort", handleLoadEnd);
        };
    }, []);

    // Handle volume changes
    useEffect(() => {
        audio.volume = state.isMuted ? 0 : state.volume;
    }, [state.volume, state.isMuted]);

    // Generate shuffled playlist
    useEffect(() => {
        if (state.shuffle) {
            const indices = Array.from(
                { length: playlist.length },
                (_, i) => i
            );

            // Keep current track as first item
            const currentIndex = indices.indexOf(state.currentTrackIndex);
            if (currentIndex > -1) {
                indices.splice(currentIndex, 1);
                indices.unshift(state.currentTrackIndex);
            }

            // Fisher-Yates shuffle for the rest
            for (let i = 1; i < indices.length; i++) {
                const j = 1 + Math.floor(Math.random() * (indices.length - 1));
                [indices[i], indices[j]] = [indices[j], indices[i]];
            }

            shuffledIndices.current = indices;
        } else {
            shuffledIndices.current = [];
        }
    }, [state.shuffle, playlist.length, state.currentTrackIndex]);

    // Play/pause toggle
    const togglePlay = useCallback(() => {
        if (!currentTrack) return;
        setIsPlaying((v) => !v);
    }, [currentTrack]);

    // Play
    const play = useCallback(() => {
        if (!currentTrack) return;
        setIsPlaying(true);
    }, [currentTrack]);

    // Pause
    const pause = useCallback(() => {
        setIsPlaying(false);
    }, []);

    // Play specific track
    const playTrack = useCallback(
        (index: number) => {
            if (index === state.currentTrackIndex) {
                togglePlay();
            } else {
                updateCurrentTime(0);
                if (audio) {
                    audio.currentTime = 0;
                }
                setState((prev) => ({
                    ...prev,
                    duration: 0,
                    repeat: prev.repeat === "one" ? "all" : prev.repeat, // Reset repeat mode if it was set to "one"
                    currentTrackIndex: index,
                }));
                setIsPlaying(true);
            }
        },
        [
            state.currentTrackIndex,
            togglePlay,
            updateCurrentTime,
            audio,
            setState,
            setIsPlaying,
        ]
    );

    // Next track
    const playNextTrack = useCallback(() => {
        if (playlist.length <= 0 || !hasNext) return;

        let nextIndex;

        if (state.shuffle) {
            const currentShuffleIndex = shuffledIndices.current.indexOf(
                state.currentTrackIndex
            );
            const nextShuffleIndex =
                (currentShuffleIndex + 1) % shuffledIndices.current.length;
            nextIndex = shuffledIndices.current[nextShuffleIndex];
        } else {
            nextIndex = (state.currentTrackIndex + 1) % playlist.length;
        }

        audio.currentTime = 0;
        updateCurrentTime(0);
        setState((prev) => ({
            ...prev,
            duration: 0,
            repeat: prev.repeat === "one" ? "all" : prev.repeat, // Reset repeat mode if it was set to "one"
            currentTrackIndex: nextIndex,
        }));

        if (!isPlaying) {
            setIsPlaying(true);
        }
    }, [
        state.currentTrackIndex,
        playlist.length,
        state.shuffle,
        hasNext,
        isPlaying,
    ]);

    // Previous track
    const playPrevTrack = useCallback(() => {
        if (playlist.length <= 1 || !hasPrev) return;

        // If current time > 3 seconds, restart current track
        if (currentTime > 3) {
            audio.currentTime = 0;
            return;
        }

        let prevIndex;

        if (state.shuffle) {
            const currentShuffleIndex = shuffledIndices.current.indexOf(
                state.currentTrackIndex
            );
            const prevShuffleIndex =
                (currentShuffleIndex - 1 + shuffledIndices.current.length) %
                shuffledIndices.current.length;
            prevIndex = shuffledIndices.current[prevShuffleIndex];
        } else {
            prevIndex =
                (state.currentTrackIndex - 1 + playlist.length) %
                playlist.length;
        }

        audio.currentTime = 0;
        updateCurrentTime(0);
        setState((prev) => ({
            ...prev,
            duration: 0,
            repeat: prev.repeat === "one" ? "all" : prev.repeat, // Reset repeat mode if it was set to "one"
            currentTrackIndex: prevIndex,
        }));

        if (!isPlaying) {
            setIsPlaying(true);
        }
    }, [
        currentTime,
        state.currentTrackIndex,
        playlist.length,
        hasPrev,
        state.shuffle,
        isPlaying,
    ]);

    // Seek to
    const seekTo = useCallback((seconds: number) => {
        audio.currentTime = seconds;
    }, []);

    // Handle repeat mode
    const toggleRepeat = useCallback(() => {
        const modes = ["none", "all", "one"] as const;
        const currentIndex = modes.indexOf(state.repeat);
        const nextIndex = (currentIndex + 1) % modes.length;

        setState((prev) => ({
            ...prev,
            repeat: modes[nextIndex],
        }));
    }, [state.repeat, setState]);

    // Handle shuffle
    const toggleShuffle = useCallback(() => {
        setState((prev) => ({
            ...prev,
            shuffle: !prev.shuffle,
        }));
    }, []);

    // Volume
    const updateVolume = useCallback(
        (volume: number) => {
            // Clamp volume between 0 and 1
            const newVolume = Math.max(0, Math.min(1, volume));
            setState((prev) => ({
                ...prev,
                volume: newVolume,
                isMuted: newVolume > 0 ? false : prev.isMuted,
            }));
        },
        [audio]
    );

    // Toggle mute
    const toggleMute = useCallback(() => {
        setState((prev) => ({
            ...prev,
            isMuted: !prev.isMuted,
        }));
    }, []);

    // Add track to playlist
    const addTrack = useCallback((track: Article) => {
        setPlaylist((prev) => [...prev, track]);
    }, []);

    const setNewPlaylist = useCallback(
        (newPlaylist: Article[], trackIndex = 0) => {
            const validTrackIndex = Math.max(
                0,
                Math.min(trackIndex, newPlaylist.length - 1)
            );

            setState((prev) => ({
                ...prev,
                currentTrackIndex: newPlaylist.length > 0 ? validTrackIndex : 0,
                duration: 0,
            }));

            updateCurrentTime(0);

            if (audio) {
                audio.currentTime = 0;
            }
            // Set the new playlist
            setPlaylist(newPlaylist);
        },
        []
    );

    // Remove track from playlist
    const removeTrack = useCallback(
        (index: number) => {
            if (index === state.currentTrackIndex) {
                if (isPlaying) {
                    setIsPlaying(false);
                }

                if (index === playlist.length - 1) {
                    // If it's the last track, go to previous
                    setState((prev) => ({
                        ...prev,
                        currentTrackIndex: Math.max(0, index - 1),
                    }));
                }
            } else if (index < state.currentTrackIndex) {
                // If removed track is before current, adjust current index
                setState((prev) => ({
                    ...prev,
                    currentTrackIndex: state.currentTrackIndex - 1,
                }));
            }

            setPlaylist(playlist.filter((_, i) => i !== index));
        },
        [state.currentTrackIndex, playlist]
    );

    // Media key event listener
    useEffect(() => {
        const handleMediaKeyEvents = (event: KeyboardEvent) => {
            switch (event.key) {
                case "MediaPlayPause":
                    togglePlay();
                    break;
                case "MediaTrackNext":
                    playNextTrack();
                    break;
                case "MediaTrackPrevious":
                    playPrevTrack();
                    break;
                case " ": // Space key as alternative
                    if (event.target === document.body) {
                        event.preventDefault();
                        togglePlay();
                    }
                    break;
                case "ArrowRight": // Right arrow with modifier keys
                    if (
                        (event.ctrlKey || event.metaKey) &&
                        event.target === document.body
                    ) {
                        event.preventDefault();
                        playNextTrack();
                    }
                    break;
                case "ArrowLeft": // Left arrow with modifier keys
                    if (
                        (event.ctrlKey || event.metaKey) &&
                        event.target === document.body
                    ) {
                        event.preventDefault();
                        playPrevTrack();
                    }
                    break;
                default:
                    break;
            }
        };

        // Clean up
        document.addEventListener("keydown", handleMediaKeyEvents);
        return () => {
            document.removeEventListener("keydown", handleMediaKeyEvents);
        };
    }, [togglePlay, playNextTrack, playPrevTrack]);

    // Add support for the Media Session API (for mobile and desktop media controls)
    useEffect(() => {
        if (!currentTrack || typeof navigator.mediaSession === "undefined") {
            return;
        }

        // Set metadata
        const artist = currentTrack.user;
        const channel = currentTrack.channel;
        const image = currentTrack.image || channel.image || artist?.image;
        navigator.mediaSession.metadata = new MediaMetadata({
            title: currentTrack.name || "Unknown Title",
            artist: artist?.name || "Unknown Artist",
            album: channel.name || "",
            artwork: image
                ? [
                      {
                          src: asset(image) || "",
                          sizes: "256x256",
                          type: "image/jpeg",
                      },
                  ]
                : [],
        });

        const defaultSkipTime = 10; /* Time to skip in seconds by default */

        const actionHandlers: [string, (...args: any[]) => void][] = [
            ["play", () => audio.play()],
            ["pause", () => audio.pause()],
            [
                "seekbackward",
                (details: MediaSessionActionDetails) => {
                    const skipTime = details.seekOffset || defaultSkipTime;
                    const position = Math.max(audio.currentTime - skipTime, 0);
                    audio.currentTime = isFinite(position) ? position : 0;
                },
            ],
            [
                "seekforward",
                (details: MediaSessionActionDetails) => {
                    const skipTime = details.seekOffset || defaultSkipTime;
                    audio.currentTime = Math.min(
                        audio.currentTime + skipTime,
                        isNaN(audio.duration) ? 0 : audio.duration
                    );
                },
            ],
            [
                "seekto",
                (details: MediaSessionActionDetails) => {
                    if (details.seekTime === undefined) return;
                    if (details.fastSeek && "fastSeek" in audio) {
                        // Only use fast seek if supported.
                        audio.fastSeek(details.seekTime);
                        return;
                    }

                    audio.currentTime = details.seekTime;
                },
            ],
            [
                "previoustrack",
                () => {
                    playPrevTrack();
                },
            ],
            [
                "nexttrack",
                () => {
                    playNextTrack();
                },
            ],
        ];

        for (const [action, handler] of actionHandlers) {
            try {
                navigator.mediaSession.setActionHandler(action as any, handler);
            } catch (error) {
                console.log(
                    `The media session action "${action}" is not supported yet.`
                );
            }
        }

        // Update playback state
        navigator.mediaSession.playbackState = isPlaying ? "playing" : "paused";

        const onTimeUpdate = () => {
            if ("setPositionState" in navigator.mediaSession) {
                const position = Math.min(audio.currentTime, audio.duration);
                navigator.mediaSession.setPositionState({
                    duration: isFinite(audio.duration) ? audio.duration : 0,
                    position: isFinite(position) ? position : 0,
                    playbackRate: audio.playbackRate,
                });
            }
        };

        audio.addEventListener("timeupdate", onTimeUpdate);
        return () => {
            audio.removeEventListener("timeupdate", onTimeUpdate);

            for (const [action] of actionHandlers) {
                try {
                    navigator.mediaSession.setActionHandler(
                        action as any,
                        null
                    );
                } catch (error) {
                    console.warn(
                        `Could not clear media session action "${action}".`
                    );
                }
            }
        };
    }, [currentTrack, isPlaying, play, pause, playNextTrack, playPrevTrack]);

    // Update ref actions
    if (typeof ref === "object" && ref?.current) {
        ref.current.togglePlay = togglePlay;
        ref.current.play = play;
        ref.current.pause = pause;
        ref.current.playTrack = playTrack;
        ref.current.playNextTrack = playNextTrack;
        ref.current.playPrevTrack = playPrevTrack;
        ref.current.seekTo = seekTo;
        ref.current.updateVolume = updateVolume;
        ref.current.toggleRepeat = toggleRepeat;
        ref.current.toggleMute = toggleMute;
        ref.current.toggleShuffle = toggleShuffle;
        ref.current.addTrack = addTrack;
        ref.current.removeTrack = removeTrack;
        ref.current.setNewPlaylist = setNewPlaylist;
        ref.current.hasNext = hasNext;
        ref.current.hasPrev = hasPrev;
    }

    return null;
});

PlayerInit.displayName = "PlayerInit";
