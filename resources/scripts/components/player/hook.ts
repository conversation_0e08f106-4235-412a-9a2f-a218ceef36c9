import { useAtomValue, useSet<PERSON>tom } from "jotai";
import {
    isPlaying<PERSON>tom,
    loading<PERSON>tom,
    playerState<PERSON>tom,
    playlistAtom,
    playlistIdAtom,
    seekSubscribersAtom,
    usePlayerActions,
} from "./player-init";
import { useAuthUser, useZiggy } from "@/hooks";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Article } from "@/types/models";
import { fetchApi } from "@/utils";
import { ItemType } from "@/types";

// Format time (seconds -> mm:ss)
export const formatTime = (seconds: number) => {
    if (isNaN(seconds) || seconds < 0) return "00:00";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
};

type GuessPlaylist =
    | ItemType
    | {
          type: "articles";
          playlistId?: string;
          data: Article[];
      };

export type PlayerHook = ReturnType<typeof usePlayer>;

export function getTrackImage(currentTrack: Article | undefined) {
    return (
        currentTrack?.image ||
        currentTrack?.channel.image ||
        currentTrack?.user?.image
    );
}

export function usePlayer({ listenToSeek = false } = {}) {
    const state = useAtomValue(playerStateAtom);
    const isPlaying = useAtomValue(isPlayingAtom);
    const playlist = useAtomValue(playlistAtom);
    const seekSubscribers = useAtomValue(seekSubscribersAtom);
    const loading = useAtomValue(loadingAtom);
    const playlistId = useAtomValue(playlistIdAtom);

    // State
    const [currentTime, setCurrentTime] = useState(0);

    // Auth
    const authUser = useAuthUser();

    const currentTrack: Article | null = playlist[state.currentTrackIndex];

    useEffect(() => {
        if (!listenToSeek) return;
        const subscriber = (seek: number) => {
            setCurrentTime(seek);
        };

        seekSubscribers?.add(subscriber);
        return () => {
            seekSubscribers.delete(subscriber);
        };
    }, [listenToSeek]);

    const formattedCurrentTime = useMemo(() => {
        return formatTime(currentTime);
    }, [currentTime]);

    const returned = {
        playlist,
        currentTime,
        formattedCurrentTime,
        isPlaying,
        currentTrack,
        loading,
        playlistId,
        actions: usePlayerActions(),
        ...state,
        ...usePlayerWithModels(playlist),
    };

    return authUser ? returned : undefined;
}

function usePlayerWithModels(playlist: Article[]) {
    const actions = usePlayerActions();
    const state = useAtomValue(playerStateAtom);
    const setPlaylistId = useSetAtom(playlistIdAtom);
    const setPlaylist = useSetAtom(playlistAtom);

    // Routes
    const route = useZiggy();

    const jumpTo = useCallback(
        (article: Article) => {
            const songIndex = playlist.findIndex((s) => s.id === article.id);
            if (songIndex > -1) {
                actions.playTrack(songIndex);
            }
        },
        [playlist, state, actions]
    );

    // Network actions
    type FetchArticleParams = {
        type: "channel" | "playlist";
        parentId: string;
    };
    const getArticles = useCallback(
        (params: FetchArticleParams) => {
            const url = route(`app.${params.type}s.articles`, {
                [params.type]: params.parentId,
            });
            return fetchApi<Article[]>(url)
                .then((data) => {
                    if (Array.isArray(data) && data.length > 0) {
                        return data;
                    }
                    return [];
                })
                .catch((error) => {
                    console.error("Error fetching channel articles:", error);
                    return [];
                });
        },
        [route]
    );

    const playArticles = useCallback(
        (articles: Article[], activeArticle?: Article) => {
            if (!articles.length) return;
            const initialTrackIndex = articles.findIndex(
                (a) => a.id === activeArticle?.id
            );

            actions.setNewPlaylist(
                articles,
                activeArticle ? initialTrackIndex : 0
            );
            requestAnimationFrame(() => {
                actions.play();
            });
        },
        [jumpTo, actions]
    );

    const updateArticleInPlaylist = useCallback(
        (article: Article) => {
            setPlaylist((ps) => {
                return ps.map((v) => (v.id === article.id ? article : v));
            });
        },
        [setPlaylist]
    );

    const guessPlaylist = useCallback(
        async (item: GuessPlaylist) => {
            setPlaylistId(null);

            switch (item.type) {
                case "article":
                    playArticles([item.data]);
                    break;

                case "articles": {
                    if (item.playlistId) {
                        setPlaylistId(item.playlistId);
                    }
                    playArticles(item.data);
                    break;
                }
                case "playlist":
                case "channel": {
                    setPlaylistId(item.data.id);

                    const articles = await getArticles({
                        type: item.type,
                        parentId: item.data.id,
                    });
                    if (articles.length > 0) {
                        const activeArticle =
                            "activeArticle" in item
                                ? item.activeArticle
                                : undefined;

                        playArticles(articles, activeArticle);
                    }
                    break;
                }
            }
        },
        [getArticles, playArticles]
    );

    return {
        jumpTo,
        updateArticleInPlaylist,
        playArticles,
        guessPlaylist,
    };
}
