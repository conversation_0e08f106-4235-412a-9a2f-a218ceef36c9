import { useRef, useEffect, RefObject, useCallback } from "react";

// Default threshold in pixels
const DEFAULT_SWIPE_CLOSE_THRESHOLD = 75;
// Selectors for common interactive elements that shouldn't trigger the swipe-down easily when inside the scrollable area
const INTERACTIVE_ELEMENTS_SELECTOR =
    'button, a, input, select, textarea, [role="button"], [role="link"], [role="tab"], [tabindex]:not([tabindex="-1"])';

interface UseSwipeToCloseOptions {
    /** The callback function to call when a swipe-down gesture is detected. */
    onClose: () => void;
    /** <PERSON>olean indicating if the element the swipe is attached to is currently open/active. */
    isOpen: boolean;
    /** Ref pointing to the container element that has the scrollbar (e.g., the main content area). */
    scrollableRef: RefObject<HTMLElement>;
    /** The minimum vertical distance (in pixels) to swipe down to trigger onClose. Defaults to 75. */
    threshold?: number;
    /** Set to true to temporarily disable the swipe detection. */
    disabled?: boolean;
}

/**
 * A React hook to enable "swipe down to close" functionality on an element,
 * differentiating between swipes intended to close and swipes intended to scroll content.
 *
 * @param options - Configuration options for the swipe behavior.
 * @returns A ref object that should be attached to the main element that listens for swipes (e.g., the modal panel).
 */
export function useSwipeToClose<T extends HTMLElement>({
    onClose,
    isOpen,
    scrollableRef,
    threshold = DEFAULT_SWIPE_CLOSE_THRESHOLD,
    disabled = false,
}: UseSwipeToCloseOptions): RefObject<T> {
    const swipeElementRef = useRef<T>(null);
    const touchStartY = useRef(0);
    const isDragging = useRef(false);
    const isAttemptingClose = useRef(false);

    // Use useCallback to stabilize handlers if onClose isn't stable
    const stableOnClose = useCallback(onClose, [onClose]);

    useEffect(() => {
        const swipeElement = swipeElementRef.current;
        const scrollableElement = scrollableRef.current;

        if (!isOpen || !swipeElement || disabled) {
            // Reset state if closed or disabled
            isDragging.current = false;
            isAttemptingClose.current = false;
            touchStartY.current = 0;
            return;
        }

        const handleTouchStart = (e: TouchEvent) => {
            if (disabled) return;

            const target = e.target as HTMLElement;
            // Check scroll position *only if* the touch starts *inside* the scrollable element
            const canAttemptCloseBasedOnScroll =
                !scrollableElement || // No scrollable element defined
                !scrollableElement.contains(target) || // Touch outside scrollable area
                scrollableElement.scrollTop <= 0; // Touch inside scrollable area, but it's scrolled to top

            // Check if the touch started on an interactive element *inside* the scrollable area
            const startedOnInteractiveInsideScrollable =
                scrollableElement?.contains(target) &&
                target.closest(INTERACTIVE_ELEMENTS_SELECTOR);

            if (
                canAttemptCloseBasedOnScroll &&
                !startedOnInteractiveInsideScrollable
            ) {
                touchStartY.current = e.touches[0].clientY;
                isDragging.current = true;
                isAttemptingClose.current = false; // Reset attempt flag
            } else {
                // Touch started within scrollable content not at the top,
                // or on an interactive element inside it. Likely a scroll/interaction attempt.
                isDragging.current = false;
            }
        };

        const handleTouchMove = (e: TouchEvent) => {
            if (!isDragging.current || disabled) return;

            const currentY = e.touches[0].clientY;
            const deltaY = currentY - touchStartY.current;

            // Only act if swiping down significantly
            if (deltaY > 5) {
                // Add a small buffer to prevent accidental prevention on tiny movements
                // Prevent default scroll behavior ONLY when actively dragging down to close
                e.preventDefault();
                isAttemptingClose.current = true; // Mark as attempting to close

                // Optional: Add visual feedback via transform (requires careful handling with transitions)
                // swipeElement.style.transform = `translateY(${Math.max(0, deltaY)}px)`;
                // swipeElement.style.transition = 'none';
            } else if (deltaY < 0 && isAttemptingClose.current) {
                // If user starts dragging down then drags back up, maybe cancel the 'close attempt' state
                // This prevents closing if they swipe down then immediately up before touchend
                isAttemptingClose.current = false;
                isDragging.current = false; // Cancel the whole drag-to-close gesture for this touch
                // Reset visual feedback if applied
                // swipeElement.style.transform = '';
                // swipeElement.style.transition = '';
            } else if (deltaY <= 0) {
                // Dragging up from the start, definitely not a close attempt
                isDragging.current = false;
            }
        };

        const handleTouchEnd = (e: TouchEvent) => {
            // Always reset potential visual feedback on touch end/cancel
            // if (swipeElement) {
            //    swipeElement.style.transform = '';
            //    swipeElement.style.transition = '';
            // }

            if (!isDragging.current || !isAttemptingClose.current || disabled) {
                isDragging.current = false;
                isAttemptingClose.current = false;
                return; // Not a valid drag-to-close attempt
            }

            const currentY = e.changedTouches[0].clientY;
            const deltaY = currentY - touchStartY.current;

            if (deltaY > threshold) {
                stableOnClose(); // Close the element
            }

            // Reset state for the next touch
            isDragging.current = false;
            isAttemptingClose.current = false;
            touchStartY.current = 0;
        };

        // Use passive: false for touchmove ONLY because we call preventDefault
        swipeElement.addEventListener("touchstart", handleTouchStart, {
            passive: true,
        });
        swipeElement.addEventListener("touchmove", handleTouchMove, {
            passive: false,
        });
        swipeElement.addEventListener("touchend", handleTouchEnd, {
            passive: true,
        });
        swipeElement.addEventListener("touchcancel", handleTouchEnd, {
            passive: true,
        }); // Handle cancellation

        return () => {
            swipeElement.removeEventListener("touchstart", handleTouchStart);
            swipeElement.removeEventListener("touchmove", handleTouchMove);
            swipeElement.removeEventListener("touchend", handleTouchEnd);
            swipeElement.removeEventListener("touchcancel", handleTouchEnd);
            // Ensure transform is cleared on cleanup if visual feedback was used
            // swipeElement.style.transform = '';
            // swipeElement.style.transition = '';
        };
        // stableOnClose is used instead of onClose directly
    }, [isOpen, scrollableRef, threshold, disabled, stableOnClose]);

    return swipeElementRef;
}
