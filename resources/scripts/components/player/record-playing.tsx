import debounce from "lodash/debounce";
import { usePlayer } from "./hook";
import { fetchApi } from "@/utils";
import { useEffect, useRef } from "react";
import { useZiggy } from "@/hooks";

const sendArticlePlayRefAPI = debounce(function (url: string) {
    fetchApi(url, { method: "put" });
}, 1000);

export function RecordPlaying() {
    const player = usePlayer({ listenToSeek: true });
    const route = useZiggy();

    const seekedTime = useRef(0);
    const canRecord = useRef(false);

    useEffect(() => {
        if (!player) return;

        if (player.currentTrack) {
            seekedTime.current = 0;
            canRecord.current = true;
        }
    }, [player?.currentTrack?.id]);

    useEffect(() => {
        if (!player || !player.currentTrack) return;

        seekedTime.current += 1;
    }, [Math.floor(player?.currentTime || 0)]);

    /**
     * Reset the seeked time when the audio is repeated
     */
    useEffect(() => {
        if (!player || !player.currentTrack) return;
        // If the audio is repeated, reset the seeked time
        const repeatTime = Math.floor(player.duration * 0.4) + player.duration;
        if (seekedTime.current > repeatTime) {
            seekedTime.current = 0;
            canRecord.current = true;
        }
    }, [player?.currentTime]);

    useEffect(() => {
        if (!player || !player.currentTrack) return;

        if (player.currentTime < player.duration && canRecord.current) {
            // Record the play after 40% of the audio is played
            if (seekedTime.current >= Math.floor(player.duration * 0.4)) {
                canRecord.current = false;
                sendArticlePlayRefAPI(
                    route("app.plays.article", {
                        article: player.currentTrack.id,
                    })
                );
            }
        }
    }, [player?.currentTime, player?.duration]);

    return null;
}
