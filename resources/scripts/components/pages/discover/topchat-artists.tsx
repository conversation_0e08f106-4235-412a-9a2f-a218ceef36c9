import { UserListItem } from "@/components/lib";
import { ArticleLook } from "@/components/lib/ui";
import { User } from "@/types/models";
import { cn } from "@/utils";

type Props = {
    artists: User[];
    showList?: boolean;
};

export function TopChartArtists({ artists, showList }: Props) {
    return (
        <ArticleLook type="list" className="mb-6 gap-6 lg:gap-0 lg:space-y-6">
            {artists.map((artist) => {
                return (
                    <UserListItem
                        key={artist.id}
                        user={artist}
                        className={cn(
                            showList && [
                                "article-item-list",
                                // "before:content-none lg:before:content-[counter(li)]",
                            ]
                        )}
                    />
                );
            })}
        </ArticleLook>
    );
}
