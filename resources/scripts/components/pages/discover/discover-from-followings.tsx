import { ItemArticle } from "@/components/lib";
import { ArticleLook } from "@/components/lib/ui/article-loop";
import { Article, Channel, Playlist } from "@/types/models";

type DiscoverChannelItemsProps = {
    cols?: 3 | 6;
    channels: Channel[];
};

type DiscoverArticleItemsProps = {
    cols?: 3 | 6;
    articles: Article[];
};

type DiscoverPlaylistItemsProps = {
    cols?: 3 | 6;
    playlists: Playlist[];
};

export function DiscoverChannelItems({
    cols = 3,
    channels,
}: DiscoverChannelItemsProps) {
    return (
        <ArticleLook type="card" cols={cols}>
            {channels.map((channel) => {
                return (
                    <ItemArticle
                        key={channel.id}
                        viewType="card"
                        type="channel"
                        data={channel}
                    />
                );
            })}
        </ArticleLook>
    );
}

export function DiscoverArticleItems({
    cols = 3,
    articles,
}: DiscoverArticleItemsProps) {
    return (
        <ArticleLook type="card" cols={cols}>
            {articles.map((article) => {
                return (
                    <ItemArticle
                        key={article.id}
                        viewType="card"
                        type="article"
                        data={article}
                    />
                );
            })}
        </ArticleLook>
    );
}

export function DiscoverPlaylistItems({
    cols = 3,
    playlists,
}: DiscoverPlaylistItemsProps) {
    return (
        <ArticleLook type="card" cols={cols}>
            {playlists.map((playlist) => {
                return (
                    <ItemArticle
                        key={playlist.id}
                        viewType="card"
                        type="playlist"
                        data={playlist}
                    />
                );
            })}
        </ArticleLook>
    );
}
