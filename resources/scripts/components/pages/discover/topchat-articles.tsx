import { ItemArticle } from "@/components/lib";
import { ArticleLook } from "@/components/lib/ui";
import { Article } from "@/types/models";
import { cn } from "@/utils";

type Props = {
    articles: Article[];
    showListCount?: boolean;
    hoverBorder?: boolean;
};

export function TopChartArticles({
    articles,
    hoverBorder = true,
    showListCount = true,
}: Props) {
    return (
        <ArticleLook
            type="list"
            className={cn("mb-6 space-y-3", !showListCount && "space-y-4")}
        >
            {articles.map((article, i) => {
                return (
                    <ItemArticle
                        key={article.id}
                        viewType="list"
                        type="article"
                        data={article}
                        showLikes={true}
                        className={cn(
                            hoverBorder && [
                                "border-b border-b-transparent hover:border-b-gray-300/50 dark:hover:border-b-gray-600/30 pb-3 transition-colors duration-300",
                            ],
                            !showListCount && "before:hidden",
                            i === articles.length - 1 && ["border-b-0"]
                        )}
                    />
                );
            })}
        </ArticleLook>
    );
}
