import { useIPage } from "@/hooks";
import { IoMusicalNotes, IoHeadset, IoRadio } from "react-icons/io5";

type FeatureCardProps = {
    icon: React.ReactNode;
    title: string;
    description: string;
};

function FeatureCard({ icon, title, description }: FeatureCardProps) {
    return (
        <div className="bg-white rounded-lg p-4 lg:p-6 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100">
            <div className="text-primary text-2xl lg:text-3xl mb-2 lg:mb-3">{icon}</div>
            <h3 className="text-base lg:text-lg font-bold mb-1 lg:mb-2">{title}</h3>
            <p className="text-gray-600 text-sm lg:text-base">{description}</p>
        </div>
    );
}

const features = [
    {
        icon: <IoMusicalNotes className="animate-pulse" />,
        title: "Global Music Library",
        description: "Access diverse songs from displaced artists worldwide.",
    },
    {
        icon: <IoHeadset />,
        title: "High-Quality Audio",
        description: "Experience premium sound with advanced technology.",
    },
    {
        icon: <IoRadio />,
        title: "Curated Playlists",
        description: "Discover music tailored to your taste and culture.",
    },
];

export function FeaturesSection() {
    const { props } = useIPage();
    // const route = useZiggy();

    return (
        <div className="w-full h-full flex flex-col relative bg-gradient-to-b from-white to-primary-50 overflow-y-auto">
            <div className="w-full max-w-6xl mx-auto px-3 lg:px-4 py-4 lg:py-8 xl:py-12 space-y-4 lg:space-y-6 xl:space-y-10 min-h-0 flex-1">
                {/* Mission Statement Section */}
                <div className="text-center">
                    <h2 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold mb-2 sm:mb-3 lg:mb-6">
                        Empowering Displaced Artists
                    </h2>

                    <div className="max-w-4xl mx-auto space-y-2 sm:space-y-3 lg:space-y-4">
                        <p className="text-xs sm:text-sm lg:text-base xl:text-lg text-gray-700 leading-relaxed">
                            Millions of individuals worldwide are forcibly
                            displaced due to conflict, persecution, and
                            environmental crises. Among them are talented
                            artists whose voices often go unheard due to limited
                            access to platforms, networks, and opportunities.
                        </p>

                        <p className="text-xs sm:text-sm lg:text-base xl:text-lg text-gray-700 leading-relaxed">
                            Despite these challenges, music and creative
                            expression remain powerful tools for storytelling,
                            healing, and community building.
                        </p>
                    </div>
                </div>

                {/* Features Grid */}
                <div>
                    <h3 className="text-xl lg:text-2xl xl:text-3xl font-bold text-center mb-4 lg:mb-6">
                        Why Choose {props.appName}?
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
                        {features.map((feature, index) => (
                            <FeatureCard
                                key={index}
                                icon={feature.icon}
                                title={feature.title}
                                description={feature.description}
                            />
                        ))}
                    </div>
                </div>

                {/* About Section */}
                <div className="rounded-lg lg:rounded-xl p-4 lg:p-6 xl:p-8 shadow-sm border border-gray-100">
                    <h3 className="text-xl lg:text-2xl xl:text-3xl font-bold text-center mb-3 lg:mb-4 xl:mb-6">
                        About {props.appName}
                    </h3>

                    <p className="text-sm lg:text-base xl:text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto text-center">
                        {props.appName} is a civic engagement project that aims
                        to provide a digital space for refugee, internally
                        displaced (IDP), and migrant artists to showcase and
                        promote their stories, sounds, and music. This
                        initiative offers displaced individuals a platform to
                        share their talents, preserve their creative heritage,
                        and gain financial and social support through a
                        structured community of practice.
                    </p>
                </div>
            </div>
        </div>
    );
}
