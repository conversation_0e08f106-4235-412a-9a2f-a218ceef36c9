import { useSwiperSlide } from "@/components/lib";
import { useZiggy } from "@/hooks";
import image from "@images/alexunder-hess.png";
import { IoArrowForward } from "react-icons/io5";

export function TryNowSection() {
    const { controller } = useSwiperSlide();
    const route = useZiggy();

    return (
        <div className="w-full h-full flex flex-row bg-gradient-to-br from-white via-primary-50 to-white">
            <div className="md:w-1/2 w-full px-8 md:px-16">
                <div className="flex items-start flex-col justify-center h-full">
                    <div className="w-full max-w-md">
                        <h2 className="font-bold text-3xl md:text-4xl lg:text-5xl mb-6 text-gray-800">
                            <span className="block mb-2 animate-[revealTextFromLeft_1s_ease-in-out_0.3s_both]">
                                Your Rhythm
                            </span>
                            <span className="block text-primary animate-[revealTextFromLeft_1s_ease-in-out_0.6s_both]">
                                Your World
                            </span>
                        </h2>

                        <p className="text-gray-600 text-lg mb-8 animate-[revealTextFromLeft_1s_ease-in-out_0.9s_both]">
                            Experience music like never before with our premium
                            streaming service. Discover new artists, create
                            custom playlists, and enjoy high-quality audio
                            anywhere.
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 animate-[revealTextFromLeft_1s_ease-in-out_1.2s_both]">
                            <a
                                href={route("app.discover")}
                                className="bg-primary hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 group"
                            >
                                Try it now
                                <IoArrowForward className="group-hover:translate-x-1 transition-transform" />
                            </a>

                            <a
                                href="#features"
                                onClick={(e) => {
                                    e.preventDefault();
                                    controller?.current?.jumpTo(2);
                                }}
                                className="border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center"
                            >
                                Learn More
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div className="hidden md:flex md:w-1/2 w-full justify-center items-center">
                <div className="relative w-full max-w-lg">
                    {/* Decorative elements */}
                    <div className="absolute -top-10 -left-10 w-32 h-32 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
                    <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>

                    {/* Image with animation and styling */}
                    <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-500 border-4 border-white">
                        <img
                            src={image}
                            alt="Music Experience"
                            className="w-full h-auto object-cover"
                        />

                        {/* Overlay gradient */}
                        <div className="absolute inset-0 bg-gradient-to-t from-primary-600/30 to-transparent opacity-60"></div>
                    </div>
                </div>
            </div>
        </div>
    );
}
