import { useIPage } from "@/hooks";
import { IoMusicalNotes, IoHeadset, IoRadio } from "react-icons/io5";

type FeatureCardProps = {
    icon: React.ReactNode;
    title: string;
    description: string;
};

function FeatureCard({ icon, title, description }: FeatureCardProps) {
    return (
        <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
            <div className="text-primary text-4xl mb-4">{icon}</div>
            <h3 className="text-xl font-bold mb-2">{title}</h3>
            <p className="text-gray-600">{description}</p>
        </div>
    );
}

const features = [
    {
        icon: <IoMusicalNotes className="animate-pulse" />,
        title: "Unlimited Music",
        description: "Access millions of songs from artists around the world.",
    },
    {
        icon: <IoHeadset />,
        title: "Premium Sound",
        description:
            "Experience high-quality audio with our advanced sound technology.",
    },
    {
        icon: <IoRadio />,
        title: "Personalized Playlists",
        description: "Discover new music tailored to your unique taste.",
    },
    // {
    //     icon: <IoPlayCircle />,
    //     title: "Offline Listening",
    //     description:
    //         "Download your favorite tracks and listen anywhere, anytime.",
    // },
];

export function FeaturesSection() {
    const { props } = useIPage();
    // const route = useZiggy();

    return (
        <div className="w-full h-full flex flex-col relative bg-gradient-to-b from-white to-primary-50">
            <div className="w-full max-w-7xl mx-auto px-4 py-16">
                {/* Mission Statement Section */}
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Empowering Displaced Artists
                    </h2>

                    <div className="max-w-4xl mx-auto space-y-6">
                        <p className="text-lg text-gray-700 leading-relaxed">
                            Millions of individuals worldwide are forcibly
                            displaced due to conflict, persecution, and
                            environmental crises. Among them are talented
                            artists whose voices often go unheard due to limited
                            access to platforms, networks, and opportunities.
                        </p>

                        <p className="text-lg text-gray-700 leading-relaxed">
                            Despite these challenges, music and creative
                            expression remain powerful tools for storytelling,
                            healing, and community building.
                        </p>
                    </div>
                </div>

                {/* Features Grid */}
                <div className="mb-10">
                    <h3 className="text-2xl md:text-3xl font-bold text-center mb-8">
                        Why Choose {props.appName}?
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <FeatureCard
                                key={index}
                                icon={feature.icon}
                                title={feature.title}
                                description={feature.description}
                            />
                        ))}
                    </div>
                </div>

                {/* About Section */}
                <div className="rounded-2xl p-8 shadow-sm border border-gray-100 mb-12">
                    <h3 className="text-2xl md:text-3xl font-bold text-center mb-6">
                        About {props.appName}
                    </h3>

                    <p className="text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto text-center">
                        {props.appName} is a civic engagement project that aims
                        to provide a digital space for refugee, internally
                        displaced (IDP), and migrant artists to showcase and
                        promote their stories, sounds, and music. This
                        initiative offers displaced individuals a platform to
                        share their talents, preserve their creative heritage,
                        and gain financial and social support through a
                        structured community of practice.
                    </p>

                    {/* <div className="text-center">
                        <a
                            href={route("app.discover")}
                            className="bg-primary hover:bg-primary-600 text-white font-bold py-2 px- rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg"
                        >
                            Start Listening Now
                        </a>
                    </div> */}
                </div>

                {/* Call to Action */}
            </div>
        </div>
    );
}
