import { Comments, PlayButton } from "@/components/lib";
import {
    IoChatboxEllipsesOutline,
    IoCloudDownloadOutline,
    IoHeart,
    IoHeartOutline,
} from "@/components/lib/icons";
import { Avatar, GradientWrapper } from "@/components/lib/ui";
import { usePlayer } from "@/components/player";
import { useItemLike, useModal, useZiggy } from "@/hooks";
import { ChannelShowPageProps } from "@/types/pages";
import { abbreviateNumber, asset, cn } from "@/utils";
import { Link, usePage } from "@inertiajs/react";
import { useCallback } from "react";

export function ShowChannelProfile() {
    const modal = useModal();

    // Inertia
    const route = useZiggy();

    //
    const { likeCall } = useItemLike();

    // Player
    const player = usePlayer();

    const { props } = usePage<ChannelShowPageProps>();
    const channel = props.channel;
    const artist = props.channel.user;
    const image = props.channel.image;
    const articles = props.articles;

    // Active player's playing
    const playing = player?.isPlaying;
    const channelTrack = player?.currentTrack?.channel;

    const channelPlaying = channelTrack?.id === channel.id && playing;

    const artistLink = route("app.artists.show", { user: artist?.id || "" });

    const onPlayClick = useCallback(() => {
        if (channelPlaying) {
            player?.actions.pause();
            return;
        }

        player?.guessPlaylist({ type: "articles", data: articles });
    }, [channel, player, channelPlaying, articles]);

    return (
        <div className="border-b border-gray-300/70 dark:border-gray-700">
            <GradientWrapper image={image}>
                {/* Channel title */}
                <div className="flex space-x-2 items-center mt-6 mb-4 max-w-full">
                    <h1 className="text-xl lg:text-3xl font-semibold break-words max-w-[80%]">
                        {channel.name}
                    </h1>

                    <div className="p-1 bg-slate-300/40 text-[0.65rem] rounded">
                        {channel.type}
                    </div>
                </div>

                {/* Artist name */}
                <div className="flex space-x-2 my-4 items-center">
                    {artist && (
                        <Link href={artistLink}>
                            <Avatar
                                imageTitle={artist?.name}
                                size={30}
                                src={asset(artist?.image)}
                                alt={artist?.name}
                                className="bg-light-primary-700 dark:bg-dark-primary-background"
                            />
                        </Link>
                    )}

                    <h2
                        title={artist?.name}
                        className={cn(
                            "text-xs font-semibold",
                            artistLink && [
                                "hover:text-primary-600 dark:hover:text-primary",
                            ]
                        )}
                    >
                        {/* Music name */}
                        {artist && (
                            <Link href={artistLink}>{artist?.name}</Link>
                        )}
                    </h2>

                    {channel?.created_at && (
                        <>
                            <span>⊛</span>
                            <span className="text-xs font-semibold hidden sm:inline">
                                {new Date(
                                    channel?.created_at
                                ).toLocaleDateString("en-US", {
                                    year: "numeric",
                                    month: "long",
                                    day: "numeric",
                                })}
                            </span>

                            <span className="text-xs font-semibold inline sm:hidden">
                                {new Date(channel?.created_at).getFullYear()}
                            </span>
                        </>
                    )}
                </div>

                {/* Profile actions */}
                <div className="flex items-center mt-4 space-x-4">
                    {/* Play button */}
                    <PlayButton
                        onClick={onPlayClick}
                        playing={channelPlaying}
                    />

                    {/* Like button */}
                    <div className="opacity-90 text-sm items-center flex space-x-1">
                        <span>
                            {abbreviateNumber(channel.likes_count || 0)}
                        </span>
                        <button
                            type="button"
                            className={cn(
                                channel.liked && [
                                    "text-primary dark:text-primary-50",
                                ]
                            )}
                            onClick={() =>
                                channel &&
                                likeCall({ type: "channel", data: channel })
                            }
                        >
                            {channel.liked ? (
                                <IoHeart className="h-5 w-5" />
                            ) : (
                                <IoHeartOutline className="h-5 w-5" />
                            )}
                        </button>
                    </div>

                    {/* Download */}
                    <div className="opacity-90 text-sm flex items-center space-x-1">
                        <span>
                            {abbreviateNumber(channel.downloads_count || 0)}
                        </span>
                        <a
                            href={route("app.channels.download", {
                                channel: channel.id,
                            })}
                        >
                            <IoCloudDownloadOutline className="h-5 w-5" />
                        </a>
                    </div>

                    {/* Comment button */}
                    <div className="opacity-90 text-sm flex items-center space-x-1">
                        <span>
                            {abbreviateNumber(channel.comments_count || 0)}
                        </span>
                        <button type="button" onClick={modal.openModal}>
                            <IoChatboxEllipsesOutline className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </GradientWrapper>

            {/* Comment wrapper */}
            <Comments modal={modal} item={{ type: "channel", data: channel }} />
        </div>
    );
}
