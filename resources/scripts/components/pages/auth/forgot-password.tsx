import { <PERSON><PERSON>, Input<PERSON>ield, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON> } from "@/components/lib/ui";
import { IModalHook, useForgotPassword } from "@/hooks";

export function ForgotPasswordForm(props: IModalHook) {
    const { data, setData, processing, handleSubmit, errors } =
        useForgotPassword(props.closeModal);

    return (
        <Modal
            isOpen={props.isOpen}
            closeModal={props.closeModal}
            className="w-full xl:w-[70%] flex justify-center"
        >
            <ModalBody onClose={props.closeModal}>
                <h3 className="text-2xl text-dark dark:text-light/90 pb-7 text-center">
                    Having trouble signing in?
                </h3>

                <form method="post" onSubmit={handleSubmit} className="w-full">
                    <InputField
                        type="email"
                        placeholder="Enter your acount email"
                        name="email"
                        value={data.email}
                        errors={errors}
                        onChange={(e) => setData("email", e.target.value)}
                    />

                    <p className="text-xs text-dark dark:text-light/90 mb-4">
                        Forgot your password? No problem. Just let us know your
                        email address and we will email you a password reset
                        link that will allow you to choose a new one.
                    </p>

                    <Button
                        loading={processing}
                        type="submit"
                        variant="primary"
                    >
                        Email Password Reset Link
                    </Button>
                </form>
            </ModalBody>
        </Modal>
    );
}
