import { IoArrowBack } from "@/components/lib/icons";
import { Button, InputField } from "@/components/lib/ui";
import { useRegistration } from "@/hooks";
import { Social } from "./auth-form";
import { cn } from "@/utils";

export function RegisterForm({ loginPage }: { loginPage?: () => void }) {
    const { onRegistration, onChange, errors, processing, data } =
        useRegistration();

    return (
        <div className="flex items-center flex-col w-full mt-5">
            <h3 className="text-4xl font-medium">Harmony Awaits</h3>
            <p className="my-4 text-dark/75 dark:text-light/75 text-center">
                Register Now to Experience Limitless Melodic Adventures!
            </p>

            <form
                autoComplete="off"
                className="flex flex-col w-full"
                onSubmit={onRegistration}
            >
                <InputField
                    type="text"
                    placeholder="Name"
                    errors={errors}
                    name="name"
                    value={data.name}
                    onChange={onChange}
                />

                <InputField
                    type="email"
                    placeholder="Email address"
                    errors={errors}
                    name="email"
                    value={data.email}
                    onChange={onChange}
                />

                <div className="flex flex-col mt-4">
                    <InputField
                        type="password"
                        placeholder="Password"
                        name="password"
                        errors={errors}
                        value={data.password}
                        onChange={onChange}
                    />

                    <InputField
                        placeholder="Retape your password"
                        type="password"
                        name="password_confirmation"
                        errors={errors}
                        value={data.password_confirmation}
                        onChange={onChange}
                    />
                </div>

                <div className="w-full flex justify-between items-center">
                    <button
                        onClick={loginPage}
                        type="button"
                        className="text-primary hover:underline cursor-pointer"
                    >
                        <IoArrowBack />
                    </button>
                    <Button
                        loading={processing}
                        disabled={processing}
                        type="submit"
                        className={cn(
                            "justify-self-center px-3 py-2 border border-primary/60 dark:border-primary-600",
                            "font-medium py-3 px-4 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
                        )}
                    >
                        Create my account
                    </Button>
                </div>
            </form>

            <Social className="mt-6" />
        </div>
    );
}
