import { LoginForm } from "./login";
import { RegisterForm } from "./register";
import { useState } from "react";
import { cn } from "@/utils";
import { useIPage, useZiggy } from "@/hooks";

export function AuthForm() {
    const [form, setForm] = useState<"login" | "register">("login");

    return (
        <div className="flex justify-center w-full">
            {/* Register login */}
            <div className="w-full lg:w-2/3 2xl:w-2/5 overflow-x-hidden max-w-lg">
                <div
                    className={cn(
                        "w-[200%] transition-[transform] duration-500 flex justify-between",
                        form === "register" && ["-translate-x-[50%]"]
                    )}
                >
                    {/* login form */}
                    <div className="w-[50%] px-3">
                        <LoginForm registerPage={() => setForm("register")} />
                    </div>

                    {/* Register form */}
                    <div
                        className={cn(
                            "w-[50%] px-3 transition-[visibility] ease-out duration-700",
                            form === "login" && ["invisible"]
                        )}
                    >
                        <RegisterForm loginPage={() => setForm("login")} />
                    </div>
                </div>
            </div>
        </div>
    );
}

export function Social({ className }: { className?: string }) {
    const page = useIPage();
    const route = useZiggy();

    const sso = page.props.sso;
    const googleEnabled = sso?.google?.enabled;

    const googleButton = (
        <a
            href={route("socialite.redirect", { provider: "google" })}
            className="flex items-center justify-center bg-white hover:bg-gray-50 dark:bg-dark-primary-700 dark:hover:bg-dark-primary text-gray-800 dark:text-white font-medium py-3 px-4 rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm transition-all duration-200 hover:shadow-md"
        >
            <img
                src="https://accounts.scdn.co/sso/images/new-google-icon.72fd940a229bc94cf9484a3320b3dccb.svg"
                alt="Google"
                height={24}
                width={24}
                className="mr-3"
            />
            <span>Continue with Google</span>
        </a>
    );

    return (
        <div className={cn("flex flex-col space-y-4 w-full", className)}>
            <div className="flex items-center my-2">
                <div className="flex-grow h-px bg-gray-300 dark:bg-gray-700"></div>
                <span className="px-4 text-sm text-gray-500 dark:text-gray-400">
                    or continue with
                </span>
                <div className="flex-grow h-px bg-gray-300 dark:bg-gray-700"></div>
            </div>
            {googleEnabled && googleButton}
        </div>
    );
}
