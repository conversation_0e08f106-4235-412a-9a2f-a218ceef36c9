import {
    IoArrowForwardCircleOutline,
    IoArrowForwardSharp,
} from "@/components/lib/icons";
import { Button, InputField } from "@/components/lib/ui";
import { useLogin, useModal } from "@/hooks";
import { ForgotPasswordForm } from "./forgot-password";
import { Social } from "./auth-form";
import { cn } from "@/utils";

export function LoginForm({ registerPage }: { registerPage?: () => void }) {
    const { onChange, data, onLogin, processing, errors } = useLogin();
    const modal = useModal();

    return (
        <>
            <div className="flex items-center flex-col w-full mt-5">
                <h3 className="text-4xl font-medium text-center">
                    Melody Awaits
                </h3>
                <p className="my-4 text-dark/75 dark:text-light/75 text-center">
                    Login to Dive into a Melodic Wonderland!
                </p>
                <form
                    onSubmit={onLogin}
                    autoComplete="off"
                    className="flex flex-col space-y-3 w-full"
                >
                    <InputField
                        placeholder="Email address"
                        name="email"
                        type="email"
                        value={data.email}
                        onChange={onChange}
                        errors={errors}
                    />

                    <InputField
                        placeholder="Password"
                        name="password"
                        type="password"
                        value={data.password}
                        onChange={onChange}
                        errors={errors}
                    />

                    <div className="w-full flex justify-between items-center">
                        <button
                            type="button"
                            className="text-primary-600 dark:text-primary text-sm cursor-pointer hover:underline"
                            onClick={modal.openModal}
                        >
                            Forgot Password?
                        </button>

                        <Button
                            loading={processing}
                            disabled={processing}
                            type="submit"
                            spinnerLoaderSize={20}
                            className={cn(
                                "inline-flex items-center",
                                "justify-self-center px-3 py-2 border-none",
                                "dark:bg-dark-primary-700 bg-white hover:bg-gray-50",
                                "font-medium py-3 px-4 rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm transition-all duration-200 hover:shadow-md"
                            )}
                        >
                            <span>Login</span>
                            <IoArrowForwardCircleOutline className="text-primary-600 dark:text-primary-500 w-5 h-5 mt-1" />
                        </Button>
                    </div>
                </form>

                <div className="w-full tablet:w-2/5 text-center mt-10">
                    <button
                        onClick={registerPage}
                        className="text-primary-600 dark:text-primary text-sm cursor-pointer hover:underline inline-flex items-center space-x-1"
                    >
                        <span>Create new account</span> <IoArrowForwardSharp />
                    </button>
                </div>

                <Social className="mt-6" />
            </div>

            {/* Reset password form modal */}
            <ForgotPasswordForm {...modal} />
        </>
    );
}
