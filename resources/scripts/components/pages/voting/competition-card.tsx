import { Button } from "@/components/lib/ui";
import { useZiggy } from "@/hooks";
import { Competition } from "@/types/models";
import { asset, cn } from "@/utils";
import { Link } from "@inertiajs/react";

type CompetitionCardProps = {
    competition: Competition;
    className?: string;
};

export function CompetitionCard({
    competition,
    className,
}: CompetitionCardProps) {
    const route = useZiggy();
    const image =
        competition.image?.url || asset("images/default-competition.jpg");

    // Format dates
    const startDate = new Date(competition.start_date).toLocaleDateString();
    const endDate = new Date(competition.end_date).toLocaleDateString();

    return (
        <div
            className={cn(
                "bg-white dark:bg-gray-800/50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col",
                className
            )}
        >
            <div
                className="h-40 bg-cover bg-center"
                style={{ backgroundImage: `url(${image})` }}
            >
                <div className="h-full w-full bg-gradient-to-t from-gray-900 to-transparent flex items-end p-4">
                    <div>
                        <h3 className="text-xl font-bold text-white">
                            {competition.name}
                        </h3>
                        <p className="text-gray-300 text-sm">
                            Stage {competition.stage}
                        </p>
                    </div>
                </div>
            </div>

            <div className="p-4 flex flex-col flex-grow justify-between">
                {competition.description && (
                    <p className="text-gray-700 dark:text-gray-300 text-sm mb-3 line-clamp-2 break-words">
                        {competition.description}
                    </p>
                )}

                <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <span>Starts: {startDate}</span>
                    <span>Ends: {endDate}</span>
                </div>

                <div className="flex flex-col">
                    {/* Competition Status */}
                    {(() => {
                        if (competition.is_in_entry_phase) {
                            return (
                                <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded p-2 mb-3">
                                    <p className="text-yellow-800 dark:text-yellow-300 text-xs font-medium">
                                        Entry Phase - Voting starts {startDate}
                                    </p>
                                </div>
                            );
                        } else if (competition.is_in_voting_phase) {
                            return (
                                <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded p-2 mb-3">
                                    <p className="text-green-800 dark:text-green-300 text-xs font-medium">
                                        Voting Active - Ends {endDate}
                                    </p>
                                </div>
                            );
                        }
                        return null;
                    })()}

                    <div className="flex justify-between">
                        <Link
                            href={route("app.voting.leaderboard", {
                                competition: competition.id,
                            })}
                        >
                            <Button
                                variant="light"
                                className="text-xs py-1 px-3"
                            >
                                {"View Leaderboard"}
                            </Button>
                        </Link>

                        <Link
                            href={route("app.voting.entry.form", {
                                competition: competition.id,
                            })}
                        >
                            <Button
                                variant="primary"
                                className="text-xs py-1 px-3"
                            >
                                {"Enter Competition"}
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}
