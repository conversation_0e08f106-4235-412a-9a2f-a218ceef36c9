import { GradientWrapper } from "@/components/lib/ui";
import { ArtistShowPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";

export function ShowDescription() {
    const { props } = usePage<ArtistShowPageProps>();
    const artist = props.artist;
    const image = props.artist.image;

    return (
        <GradientWrapper image={image} direction="bottom-top">
            {/* Show descriptions */}
            <div className="font-medium text-base z-10 mb-1">
                <p>{artist.monthly_plays} monthly plays</p>
            </div>

            {!!artist.monthly_listeners && (
                <div className="font-medium text-xs opacity-80 z-10 mb-1">
                    <p>{artist.monthly_listeners} monthly listeners</p>
                </div>
            )}

            {artist.description && (
                <div className="font-normal text-sm opacity-80 z-10 w-full break-words lg:w-4/5">
                    <p>Bio: {artist.description}</p>
                </div>
            )}
        </GradientWrapper>
    );
}
