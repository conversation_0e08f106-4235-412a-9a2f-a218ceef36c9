import { ArtistShowPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { DiscoverChannelItems, TopChartArticles } from "../discover";
import { useState } from "react";
import { useSyncModelState } from "@/hooks";
import { ShowMore } from "@/components/lib";

export function ShowItems() {
    const { props } = usePage<ArtistShowPageProps>();
    const articles = props.topchart_articles || [];

    const [itemsChannel, setItemChannel] = useState(props.channels.data || []);
    const [itemsFeaturings, setItemFeaturings] = useState(
        props.featurings.data || []
    );

    // Sync model state
    useSyncModelState(setItemChannel, props.channels.data);

    useSyncModelState(setItemFeaturings, props.featurings.data);

    return (
        <div className="flex flex-col-reverse  xl:flex-row xl:space-x-10 my-20">
            <div className="w-full xl:w-3/5">
                {/* Channels */}
                {itemsChannel.length > 0 && (
                    <h3 className="text-xl">{"Albums | Channels"}</h3>
                )}
                <div className="my-5">
                    <DiscoverChannelItems channels={itemsChannel} />
                </div>
                <ShowMore
                    pagination={props.channels}
                    autoLoad={false}
                    onSuccess={() =>
                        setItemChannel((ps) => ps.concat(props.channels.data))
                    }
                />

                {/* Appears On */}
                {itemsFeaturings.length > 0 && (
                    <>
                        <h3 className="text-xl">{"Appears On"}</h3>

                        <div className="my-5">
                            <DiscoverChannelItems channels={itemsFeaturings} />
                        </div>
                    </>
                )}
                <ShowMore
                    pagination={props.featurings}
                    autoLoad={false}
                    onSuccess={() =>
                        setItemFeaturings((ps) =>
                            ps.concat(props.featurings.data)
                        )
                    }
                />
            </div>
            <div>
                <hr className="xl:hidden my-8 dark:opacity-20" />
            </div>

            <div className="w-full xl:w-2/5">
                {articles.length > 0 && (
                    <h3 className="text-xl ml-8 xl:ml-0">{"Popular"}</h3>
                )}
                <div className="mt-5">
                    <TopChartArticles articles={articles} hoverBorder={false} />
                </div>
            </div>
        </div>
    );
}
