import { Button } from "@/components/lib/ui";
import { useZiggy } from "@/hooks";
import { ArtistShowPageProps } from "@/types/pages";
import { Link, usePage } from "@inertiajs/react";

export function ShowCompetitions() {
    const { props } = usePage<ArtistShowPageProps>();
    const { artist } = props;
    const route = useZiggy();

    // Don't render if no active competitions
    if (
        !artist.active_competitions ||
        artist.active_competitions.length === 0
    ) {
        return null;
    }

    return (
        <div className="bg-white dark:bg-gray-800/50 shadow-md dark:shadow-none rounded-lg p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
                <span className="mr-2">🏆</span>
                Active Competitions
            </h3>

            <div className="space-y-4">
                {artist.active_competitions.map((competition) => (
                    <div
                        key={competition.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700/30"
                    >
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <div className="flex-1">
                                <h4 className="font-semibold text-lg mb-2">
                                    {competition.name}
                                </h4>

                                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                                    <div className="flex items-center">
                                        <span className="font-medium mr-1">
                                            Category:
                                        </span>
                                        <span>
                                            {competition.type ===
                                            "refugees_only"
                                                ? "On The Move Talents Awards"
                                                : "All Artists"}
                                        </span>
                                    </div>

                                    <div className="flex items-center">
                                        <span className="font-medium mr-1">
                                            Stage:
                                        </span>
                                        <span>Stage {competition.stage}</span>
                                    </div>

                                    <div className="flex items-center">
                                        <span className="font-medium mr-1">
                                            Status:
                                        </span>
                                        <span
                                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                competition.phase_status ===
                                                "voting_phase"
                                                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                                    : competition.phase_status ===
                                                      "entry_phase"
                                                    ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                                    : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"
                                            }`}
                                        >
                                            {competition.phase_status ===
                                                "voting_phase" && "Voting Open"}
                                            {competition.phase_status ===
                                                "entry_phase" && "Entry Phase"}
                                            {competition.phase_status ===
                                                "ended" && "Ended"}
                                            {competition.phase_status ===
                                                "inactive" && "Inactive"}
                                            {![
                                                "voting_phase",
                                                "entry_phase",
                                                "ended",
                                                "inactive",
                                            ].includes(
                                                competition.phase_status
                                            ) && "Unknown"}
                                        </span>
                                    </div>
                                </div>

                                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                    Entered on{" "}
                                    {new Date(
                                        competition.entry_date
                                    ).toLocaleDateString()}
                                </div>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-2">
                                <Link
                                    href={route("app.voting.leaderboard", {
                                        competition: competition.id,
                                    })}
                                >
                                    <Button
                                        variant="light"
                                        className="text-xs py-2 px-4 w-full sm:w-auto"
                                    >
                                        View Leaderboard
                                    </Button>
                                </Link>

                                {competition.phase_status ===
                                    "voting_phase" && (
                                    <Link
                                        href={route("app.voting.artist", {
                                            competition: competition.id,
                                            artist: artist.id,
                                        })}
                                    >
                                        <Button
                                            variant="primary"
                                            className="text-xs py-2 px-4 w-full sm:w-auto"
                                        >
                                            Vote for {artist.name}
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    Support {artist.name} by voting in their active
                    competitions!
                </p>
            </div>
        </div>
    );
}
