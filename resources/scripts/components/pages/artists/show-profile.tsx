import { PlayButton } from "@/components/lib";
import { LuVerified } from "@/components/lib/icons";
import { Avatar, Button, GradientWrapper } from "@/components/lib/ui";
import { usePlayer } from "@/components/player";
import { useZiggy } from "@/hooks";
import { Article } from "@/types/models";
import { ArtistShowPageProps } from "@/types/pages";
import { asset, cn, fetchApi } from "@/utils";
import { Link, usePage } from "@inertiajs/react";
import { useCallback, useState } from "react";

export function ShowProfile() {
    // Local states
    const [fetchingPlay, setFetchingPlay] = useState(false);

    // Player
    const player = usePlayer();

    // Route
    const route = useZiggy();

    // Inertia hooks
    const { props } = usePage<ArtistShowPageProps>();
    const artist = props.artist;
    const image = props.artist.image;

    // Active player's playing
    const playing = !!player?.isPlaying;
    const userTrack = player?.currentTrack?.user;

    const artistPlaying = userTrack?.id === artist.id && playing;
    const isAuthArtist = props.authUser?.id === artist.id;
    const followers_count = +(artist.followers_count || 0);

    const playArtistTopChartArticles = useCallback(() => {
        if (fetchingPlay || !player) {
            return;
        }

        if (artistPlaying) {
            player?.actions.pause();
            return;
        }

        setFetchingPlay(true);
        fetchApi<Article[]>(
            route("app.artists.top-charts", { user: artist.id })
        )
            .then((articles) => {
                player.guessPlaylist({
                    type: "articles",
                    data: articles,
                });
            })
            .finally(() => setFetchingPlay(false));
    }, [fetchingPlay, player, artistPlaying]);

    return (
        <GradientWrapper image={image}>
            {/* Verified badge */}
            <div
                className={cn(
                    "font-semibold text-xs flex space-x-1 items-center",
                    "absolute left-7 sm:left-auto sm:right-7 top-4"
                )}
            >
                <LuVerified className="w-6 h-6 text-primary" />
                <span>Verified Artist</span>
            </div>

            {/* Profile details */}
            <div className="flex space-x-4 items-center mt-8 sm:mt-6">
                <Avatar
                    imageTitle={artist.name}
                    src={asset(image)}
                    className={cn(
                        "bg-light-primary-700 dark:bg-dark-primary-background",
                        "size-20 sm:size-28 lg:size-32"
                    )}
                />
                <div className="flex flex-col space-y-2 max-w-full">
                    <h1 className="text-xl lg:text-4xl font-semibold break-words max-w-full">
                        {artist.name}
                    </h1>

                    <span className="font-medium text-xs opacity-80">
                        {followers_count} Follower
                        {followers_count > 1 ? "s" : ""}
                    </span>
                </div>
            </div>

            {/* Profile actions */}
            <div className="flex items-center space-x-6 mt-5">
                <div>
                    <Link
                        href={
                            isAuthArtist
                                ? ""
                                : route("app.followings.toggle", {
                                      user: artist.id,
                                  })
                        }
                        as="div"
                        preserveScroll
                        method={isAuthArtist ? "get" : "put"}
                        disabled={isAuthArtist}
                    >
                        <Button
                            type="submit"
                            disabled={isAuthArtist}
                            className={cn(
                                "justify-self-center px-3 py-2",
                                "border border-primary dark:border-gray-400/60",
                                "block min-w-[7rem] hover:shadow dark:hover:shadow-white/20"
                            )}
                        >
                            {isAuthArtist ? (
                                "---"
                            ) : (
                                <>{props.followed ? "Following" : "Follow"}</>
                            )}
                        </Button>
                    </Link>
                </div>

                <div>
                    <PlayButton
                        onClick={playArtistTopChartArticles}
                        loading={fetchingPlay}
                        playing={artistPlaying}
                    />
                </div>
            </div>
        </GradientWrapper>
    );
}
