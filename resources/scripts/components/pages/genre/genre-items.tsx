import { ITab, Tabs } from "@/components/lib/ui";
import { GenreShowPageProps } from "@/types/pages";
import { usePage } from "@inertiajs/react";
import { useState } from "react";
import { DiscoverChannelItems, TopChartArticles } from "../discover";
import { ShowMore } from "@/components/lib";
import { useSyncModelState } from "@/hooks";

export function GenreItems() {
    const { props } = usePage<GenreShowPageProps>();
    const { channels, articles } = props;

    const [channelsData, setChannelData] = useState(channels.data);
    const [articlesData, setArticlesData] = useState(articles.data);

    // Sync model state
    useSyncModelState(setChannelData, channels.data);
    useSyncModelState(setArticlesData, articles.data);

    const tabs: ITab[] = [
        { title: `Albums | Channels (${channels.meta.total || 0})` },
        { title: `Songs (${articles.meta.total || 0})` },
    ];

    let defaultIndex: null | number = null;

    // Order of keys is important
    const seackKeys = ["channels", "articles"] as const;

    seackKeys.forEach((key, idx) => {
        const data = props[key];
        if (data.meta.total > 0) {
            defaultIndex === null && (defaultIndex = idx);
            tabs[idx].disabled = false;
        } else {
            tabs[idx].disabled = true;
        }
    });

    return (
        <div className="w-full">
            <Tabs tabs={tabs} defaultIndex={defaultIndex || 0}>
                {(Panel) => {
                    return (
                        <div className="mt-4">
                            {/* Channels or Albums */}
                            <Panel>
                                <DiscoverChannelItems
                                    cols={6}
                                    channels={channelsData || []}
                                />

                                <ShowMore
                                    pagination={channels}
                                    onSuccess={() =>
                                        setChannelData((ps) =>
                                            ps.concat(channels.data)
                                        )
                                    }
                                />
                            </Panel>

                            {/* Articles */}
                            <Panel>
                                <TopChartArticles
                                    articles={articlesData || []}
                                />

                                <ShowMore
                                    pagination={articles}
                                    onSuccess={() =>
                                        setArticlesData((ps) =>
                                            ps.concat(articles.data)
                                        )
                                    }
                                />
                            </Panel>
                        </div>
                    );
                }}
            </Tabs>
        </div>
    );
}
