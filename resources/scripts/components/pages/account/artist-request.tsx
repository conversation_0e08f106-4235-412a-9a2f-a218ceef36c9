import { <PERSON><PERSON>, <PERSON><PERSON>, InputField, SelectInput } from "@/components/lib/ui";
import { useSettingsArtistRequest } from "@/hooks";
import { AccountPageProps } from "@/types/pages";
import { cn } from "@/utils";
import { usePage } from "@inertiajs/react";
import capitalize from "lodash/capitalize";

export function AritstRequest() {
    const { props } = usePage<AccountPageProps>();

    return (
        <div className="my-5">
            {props.can.create_artist_request ? (
                <Form />
            ) : (
                <Alert
                    title="Your Artist Request Sent Successfully!"
                    message={`
                        We are pleased to inform you that your request to become our artist has been sent successfully!
                        Our team has received your request and will review it carefully.
                    `}
                    type="default"
                />
            )}
        </div>
    );
}

function Form() {
    const { props } = usePage<AccountPageProps>();
    const { data, processing, onChange, onSubmit, errors } =
        useSettingsArtistRequest();

    return (
        <form
            className={cn(
                "relative",
                !props.authUser?.has_verified_email && ["opacity-70"]
            )}
            onSubmit={onSubmit}
        >
            {!props.authUser?.has_verified_email && (
                <div className="absolute inset-0 w-full h-full z-10" />
            )}
            {/* Artist request role */}
            <div className="flex flex-col space-y-2 w-full lg:w-2/3 mb-4">
                <label htmlFor="artist_type" className="capitalize">
                    Artist request type
                </label>

                <SelectInput
                    name="artist_type"
                    errors={errors}
                    value={data.artist_type}
                    onChange={onChange}
                    defaultValue=""
                >
                    <option value="" disabled>
                        Type
                    </option>
                    {props.artist_roles.map((role) => {
                        return (
                            <option key={role} value={role}>
                                {capitalize(role)}
                            </option>
                        );
                    })}
                </SelectInput>
            </div>

            {/* Description */}
            <InputField
                type="textarea"
                label="Describe your request"
                name="description"
                errors={errors}
                value={data.description}
                onChange={onChange}
            />

            <Button type="submit" loading={processing} variant="primary">
                Send the request
            </Button>
        </form>
    );
}
