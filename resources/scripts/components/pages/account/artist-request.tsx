import { But<PERSON>, InputField, SelectInput } from "@/components/lib/ui";
import { useSettingsArtistRequest } from "@/hooks";
import { AccountPageProps } from "@/types/pages";
import { cn } from "@/utils";
import { usePage } from "@inertiajs/react";
import capitalize from "lodash/capitalize";
import { useState } from "react";
import { IoClose, IoMusicalNotes, IoShareSocial } from "@/components/lib/icons";

export function ArtistRequest() {
    const { props } = usePage<AccountPageProps>();

    return (
        <div className="max-w-4xl mx-auto">
            {props.can.create_artist_request ? (
                <Form />
            ) : (
                <div className="bg-gradient-to-r from-primary-50 to-green-50 dark:from-dark-primary-700 dark:to-green-900/20 rounded-2xl p-8 border border-primary-200 dark:border-primary-800">
                    <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center">
                            <IoMusicalNotes className="w-6 h-6 text-white" />
                        </div>
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                                Request Submitted Successfully!
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Your artist application is under review
                            </p>
                        </div>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        We are pleased to inform you that your request to become our artist has been sent successfully!
                        Our team has received your request and will review it carefully. You'll receive an email notification
                        once your application has been processed.
                    </p>
                </div>
            )}
        </div>
    );
}

function Form() {
    const { props } = usePage<AccountPageProps>();
    const { data, processing, onChange, onSubmit, errors, onSocialLinkChange, removeSocialLink } =
        useSettingsArtistRequest();
    const [newSocialPlatform, setNewSocialPlatform] = useState("");
    const [newSocialUrl, setNewSocialUrl] = useState("");

    const addSocialLink = () => {
        if (newSocialPlatform.trim() && newSocialUrl.trim()) {
            onSocialLinkChange(newSocialPlatform.trim(), newSocialUrl.trim());
            setNewSocialPlatform("");
            setNewSocialUrl("");
        }
    };

    const isFormDisabled = !props.authUser?.has_verified_email;

    return (
        <div className="bg-white dark:bg-dark-primary-700 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-600">
            {/* Header */}
            <div className="px-8 py-6 border-b border-gray-200 dark:border-gray-600">
                <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center">
                        <IoMusicalNotes className="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                            Artist Application
                        </h2>
                        <p className="text-gray-600 dark:text-gray-300">
                            Join our community of talented artists
                        </p>
                    </div>
                </div>
            </div>

            {/* Form */}
            <form
                className={cn(
                    "relative p-8 space-y-8",
                    isFormDisabled && ["opacity-70"]
                )}
                onSubmit={onSubmit}
            >
                {isFormDisabled && (
                    <div className="absolute inset-0 w-full h-full z-10 bg-gray-50/50 dark:bg-gray-900/50 rounded-2xl flex items-center justify-center">
                        <div className="bg-white dark:bg-dark-primary-700 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-600 max-w-md text-center">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                Email Verification Required
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Please verify your email address before submitting an artist request.
                            </p>
                        </div>
                    </div>
                )}

                {/* Artist Type Section */}
                <div className="space-y-4">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <span className="text-primary-600 dark:text-primary-400 font-semibold text-sm">1</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Artist Type
                        </h3>
                    </div>

                    <div className="ml-11">
                        <SelectInput
                            name="artist_type"
                            errors={errors as Record<string, string>}
                            value={data.artist_type}
                            onChange={onChange}
                            className="w-full max-w-md"
                        >
                            <option value="" disabled>
                                Select your artist type
                            </option>
                            {props.artist_roles.map((role) => (
                                <option key={role} value={role}>
                                    {capitalize(role)}
                                </option>
                            ))}
                        </SelectInput>
                    </div>
                </div>

                {/* Verification Section */}
                <div className="space-y-4">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <span className="text-primary-600 dark:text-primary-400 font-semibold text-sm">2</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Verification Links
                        </h3>
                    </div>

                    <div className="ml-11 space-y-4">
                        <InputField
                            type="url"
                            label="YouTube Channel (Optional)"
                            name="youtube_link"
                            placeholder="https://youtube.com/@yourusername"
                            errors={errors as Record<string, string>}
                            value={data.youtube_link}
                            onChange={onChange}
                            wrapperClassName="max-w-2xl"
                        />

                        {/* Social Links */}
                        <div className="space-y-4">
                            <h4 className="text-md font-medium text-gray-900 dark:text-white flex items-center gap-2">
                                <IoShareSocial className="w-4 h-4" />
                                Social Media Links (Optional)
                            </h4>

                            {/* Existing Social Links */}
                            {Object.entries(data.social_links).length > 0 && (
                                <div className="space-y-2">
                                    {Object.entries(data.social_links).map(([platform, url]) => (
                                        <div key={platform} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                            <div className="flex-1">
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                                                    {platform}
                                                </span>
                                                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                                    {url}
                                                </p>
                                            </div>
                                            <button
                                                type="button"
                                                onClick={() => removeSocialLink(platform)}
                                                className="p-1 text-red-500 hover:text-red-700 transition-colors"
                                            >
                                                <IoClose className="w-4 h-4" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* Add New Social Link */}
                            <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <input
                                        type="text"
                                        placeholder="Platform (e.g., Instagram, Twitter)"
                                        value={newSocialPlatform}
                                        onChange={(e) => setNewSocialPlatform(e.target.value)}
                                        className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                    />
                                    <input
                                        type="url"
                                        placeholder="https://..."
                                        value={newSocialUrl}
                                        onChange={(e) => setNewSocialUrl(e.target.value)}
                                        className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                    />
                                </div>
                                <button
                                    type="button"
                                    onClick={addSocialLink}
                                    disabled={!newSocialPlatform.trim() || !newSocialUrl.trim()}
                                    className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    <span className="text-lg">+</span>
                                    Add Social Link
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Description Section */}
                <div className="space-y-4">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <span className="text-primary-600 dark:text-primary-400 font-semibold text-sm">3</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Tell Us About Yourself
                        </h3>
                    </div>

                    <div className="ml-11">
                        <InputField
                            type="textarea"
                            label="Why do you want to become an artist on Smovee?"
                            name="description"
                            placeholder="Share your musical journey, experience, and what you hope to achieve on our platform. Tell us about your style, influences, and what makes your music unique. (Minimum 20 characters)"
                            errors={errors as Record<string, string>}
                            value={data.description}
                            onChange={onChange}
                            wrapperClassName="max-w-4xl"
                        />
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                            {data.description.length}/5000 characters
                        </p>
                    </div>
                </div>

                {/* Submit Section */}
                <div className="pt-6 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                            <p>By submitting this request, you agree to our terms and conditions.</p>
                            <p className="mt-1">We'll review your application and get back to you within 3-5 business days.</p>
                        </div>
                        <Button
                            type="submit"
                            loading={processing}
                            variant="primary"
                            className="px-8 py-3 text-base font-semibold"
                        >
                            {processing ? "Submitting..." : "Submit Application"}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}
