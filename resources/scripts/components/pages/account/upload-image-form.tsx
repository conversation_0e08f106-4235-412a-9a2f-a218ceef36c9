import { Avatar, Button, Modal, ModalBody } from "@/components/lib/ui";
import { IModalHook, useAuthUser, useSettingsAccountImage } from "@/hooks";
import { asset } from "@/utils";

export function UploadmageForm(props: IModalHook) {
    const user = useAuthUser();
    const { errors, setData, data, processing, handleSubmit } =
        useSettingsAccountImage(props.closeModal);

    return (
        <Modal
            isOpen={props.isOpen}
            closeModal={props.closeModal}
            className="w-full flex justify-center"
        >
            <ModalBody onClose={props.closeModal}>
                <h3 className="text-2xl text-dark dark:text-light/90 pb-7 text-center">
                    Update profile image
                </h3>

                <form
                    onSubmit={handleSubmit}
                    className="flex flex-col items-center space-y-10"
                >
                    <div className="flex flex-col items-center space-y-2">
                        <Avatar
                            imageTitle={user?.name}
                            size={150}
                            src={asset(user?.image)}
                            hasInput
                            onChange={(file) => setData("image", file)}
                            className="border"
                        />
                        {errors["image"] && (
                            <div className="text-red-500 text-center">
                                {errors["image"]}
                            </div>
                        )}
                    </div>

                    <Button
                        loading={processing}
                        disabled={!data.image}
                        type="submit"
                        variant="primary"
                    >
                        Update profile
                    </Button>
                </form>
            </ModalBody>
        </Modal>
    );
}
