import { <PERSON><PERSON>, <PERSON><PERSON>, InputField } from "@/components/lib/ui";
import { useAuthUser, useModal, useSettingsAccountProfile } from "@/hooks";
import { asset, cn } from "@/utils";
import { UploadmageForm } from "./upload-image-form";

export function AccountForm() {
    const user = useAuthUser();
    const { data, onChange, errors, processing, handleSubmit } =
        useSettingsAccountProfile();

    return (
        <div className="my-5">
            <UserInfo />

            <form
                className={cn(
                    "relative",
                    !user?.has_verified_email && ["opacity-70"]
                )}
                autoComplete="off"
                onSubmit={handleSubmit}
            >
                {!user?.has_verified_email && (
                    <div className="absolute inset-0 w-full h-full z-10" />
                )}

                <div className="flex mb-4 lg:space-x-2 flex-col lg:flex-row">
                    <InputField
                        type="text"
                        name="name"
                        label="name"
                        value={data.name}
                        onChange={onChange}
                        errors={errors}
                    />

                    <InputField
                        type="email"
                        name="email"
                        label="email"
                        value={data.email}
                        onChange={onChange}
                        errors={errors}
                    />
                </div>

                <div className="flex mb-4 lg:space-x-2 flex-col lg:flex-row">
                    <InputField
                        type="tel"
                        name="phone"
                        label="phone"
                        value={data.phone}
                        onChange={onChange}
                        errors={errors}
                    />
                    <InputField
                        type="text"
                        label="Country Name"
                        name="country_name"
                        value={data.country_name}
                        onChange={onChange}
                        errors={errors}
                    />
                </div>

                <div className="mb-3">
                    <InputField
                        type="textarea"
                        label="Description"
                        name="description"
                        value={data.description}
                        onChange={onChange}
                        errors={errors}
                    />
                </div>

                <Button
                    type="submit"
                    variant="primary"
                    loading={processing}
                    disabled={!user?.has_verified_email}
                >
                    Update profile
                </Button>
            </form>
        </div>
    );
}

function UserInfo() {
    const user = useAuthUser();
    const modal = useModal();

    return (
        <>
            <div
                className={cn(
                    "w-full mb-4 flex flex-row space-x-4",
                    "items-center"
                )}
            >
                <Avatar
                    imageTitle={user?.name}
                    size={90}
                    src={asset(user?.image)}
                    hasEditableClick
                    onEditableClick={modal.openModal}
                />

                <div className="text-sm flex space-x-1">
                    <span>{user?.followings_count || 0} followings</span>
                    {user?.role !== "GUEST" && (
                        <>
                            <span>⊛</span>
                            <span>{user?.followers_count || 0} followers</span>
                        </>
                    )}
                </div>
            </div>
            <UploadmageForm {...modal} />
        </>
    );
}
