import { <PERSON><PERSON>, <PERSON><PERSON>, InputField } from "@/components/lib/ui";
import { useAuthUser, useModal, useSettingsAccountProfile } from "@/hooks";
import { asset, cn } from "@/utils";
import { UploadmageForm } from "./upload-image-form";
import { IoPersonCircle, IoCall, IoDocument } from "@/components/lib/icons";

export function AccountForm() {
    const user = useAuthUser();
    const { data, onChange, errors, processing, handleSubmit } =
        useSettingsAccountProfile();

    const isFormDisabled = !user?.has_verified_email;

    return (
        <div className="w-full my-5">
            <UserInfo />

            <form
                className={cn(
                    "relative space-y-6 mt-6",
                    isFormDisabled && ["opacity-70"]
                )}
                autoComplete="off"
                onSubmit={handleSubmit}
            >
                {isFormDisabled && (
                    <div className="absolute inset-0 w-full h-full z-10 bg-gray-50/80 dark:bg-gray-900/80 rounded-lg flex items-center justify-center">
                        <div className="bg-white dark:bg-dark-primary-700 p-4 rounded-lg shadow-md border border-gray-200 dark:border-gray-600 max-w-sm text-center">
                            <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">
                                Email Verification Required
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                Please verify your email address before updating
                                your profile.
                            </p>
                        </div>
                    </div>
                )}

                {/* Basic Information Section */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-2">
                        <IoPersonCircle className="w-5 h-5" />
                        Basic Information
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <InputField
                            type="text"
                            name="name"
                            label="Full Name"
                            value={data.name}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Enter your full name"
                        />

                        <InputField
                            type="email"
                            name="email"
                            label="Email Address"
                            value={data.email}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Enter your email address"
                        />
                    </div>
                </div>

                {/* Contact Information Section */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-2">
                        <IoCall className="w-5 h-5" />
                        Contact Information
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <InputField
                            type="tel"
                            name="phone"
                            label="Phone Number"
                            value={data.phone}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Enter your phone number"
                        />
                        <InputField
                            type="text"
                            label="Country"
                            name="country_name"
                            value={data.country_name}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Enter your country"
                        />
                    </div>
                </div>

                {/* About Section */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-2">
                        <IoDocument className="w-5 h-5" />
                        About You
                    </h3>

                    <InputField
                        type="textarea"
                        label="Description"
                        name="description"
                        value={data.description}
                        onChange={onChange}
                        errors={errors as Record<string, string>}
                        placeholder="Tell us about yourself, your interests, and what you do..."
                        wrapperClassName="max-w-4xl"
                    />
                    {data.description && (
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            {data.description.length} characters
                        </p>
                    )}
                </div>

                {/* Submit Section */}
                <div className="pt-6 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                            <p>
                                Your profile information will be visible to
                                other users.
                            </p>
                        </div>
                        <Button
                            type="submit"
                            variant="primary"
                            loading={processing}
                            disabled={isFormDisabled}
                            className="px-6 py-2 text-base font-medium"
                        >
                            {processing ? "Updating..." : "Update Profile"}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}

function UserInfo() {
    const user = useAuthUser();
    const modal = useModal();

    return (
        <>
            <div
                className={cn(
                    "w-full mb-4 flex flex-row space-x-4",
                    "items-center"
                )}
            >
                <Avatar
                    imageTitle={user?.name}
                    size={90}
                    src={asset(user?.image)}
                    hasEditableClick
                    onEditableClick={modal.openModal}
                />

                <div className="text-sm flex space-x-1">
                    <span>{user?.followings_count || 0} followings</span>
                    {user?.role !== "GUEST" && (
                        <>
                            <span>⊛</span>
                            <span>{user?.followers_count || 0} followers</span>
                        </>
                    )}
                </div>
            </div>
            <UploadmageForm {...modal} />
        </>
    );
}
