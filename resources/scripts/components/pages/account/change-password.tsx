import { But<PERSON>, InputField } from "@/components/lib/ui";
import { useSettingsAccountUpdatePassword } from "@/hooks";
import { IoLockClosed } from "@/components/lib/icons";

export function ChangePassword() {
    const { data, errors, processing, onChange, onSubmit } =
        useSettingsAccountUpdatePassword();

    return (
        <div className="w-full my-5">
            <div className="mb-6">
                <p className="text-gray-600 dark:text-gray-300">
                    Update your password to keep your account secure
                </p>
            </div>

            <form onSubmit={onSubmit} className="space-y-6">
                {/* Current Password Section */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-2">
                        <IoLockClosed className="w-5 h-5" />
                        Current Password
                    </h3>

                    <InputField
                        label="Enter your current password"
                        type="password"
                        name="current_password"
                        value={data.current_password}
                        onChange={onChange}
                        errors={errors as Record<string, string>}
                        placeholder="Enter your current password"
                        wrapperClassName="max-w-md"
                    />
                </div>

                {/* New Password Section */}
                <div className="space-y-4">
                    <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-2">
                        <IoLockClosed className="w-5 h-5" />
                        New Password
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 max-w-2xl">
                        <InputField
                            label="New Password"
                            type="password"
                            name="password"
                            value={data.password}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Enter new password"
                        />

                        <InputField
                            label="Confirm New Password"
                            type="password"
                            name="password_confirmation"
                            value={data.password_confirmation}
                            onChange={onChange}
                            errors={errors as Record<string, string>}
                            placeholder="Confirm new password"
                        />
                    </div>

                    <div className="text-sm text-gray-500 dark:text-gray-400 max-w-2xl">
                        <p>Password requirements:</p>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                            <li>At least 8 characters long</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Include at least one number</li>
                            <li>Include at least one special character</li>
                        </ul>
                    </div>
                </div>

                {/* Submit Section */}
                <div className="pt-6 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                            <p>Make sure to use a strong, unique password.</p>
                        </div>
                        <Button
                            type="submit"
                            loading={processing}
                            variant="primary"
                            className="px-6 py-2 text-base font-medium"
                        >
                            {processing ? "Updating..." : "Change Password"}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}
