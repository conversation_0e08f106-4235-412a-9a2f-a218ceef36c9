import { Button, InputField } from "@/components/lib/ui";
import { useSettingsAccountUpdatePassword } from "@/hooks";

export function ChangePassword() {
    const { data, errors, processing, onChange, onSubmit } =
        useSettingsAccountUpdatePassword();

    return (
        <div className="my-5">
            <form onSubmit={onSubmit}>
                <div className="flex mb-4 lg:space-x-2 flex-col lg:flex-row">
                    <InputField
                        label="Current Password"
                        type="password"
                        name="current_password"
                        value={data.current_password}
                        onChange={onChange}
                        errors={errors}
                    />
                </div>

                <div className="flex mb-4 lg:space-x-2 flex-col lg:flex-row">
                    <InputField
                        label="New Password"
                        type="password"
                        name="password"
                        value={data.password}
                        onChange={onChange}
                        errors={errors}
                    />

                    <InputField
                        label="Confirm Password"
                        type="password"
                        name="password_confirmation"
                        value={data.password_confirmation}
                        onChange={onChange}
                        errors={errors}
                    />
                </div>

                <Button type="submit" loading={processing} variant="primary">
                    Change password
                </Button>
            </form>
        </div>
    );
}
