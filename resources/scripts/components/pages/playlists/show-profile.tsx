import { PlayButton } from "@/components/lib";
import { IoHeart, IoHeartOutline } from "@/components/lib/icons";
import { Avatar, GradientWrapper } from "@/components/lib/ui";
import { usePlayer } from "@/components/player";
import { useAuthUser, useItemLike, useZiggy } from "@/hooks";
import { PlaylistShowPageProps } from "@/types/pages";
import { asset, cn } from "@/utils";
import { Link, usePage } from "@inertiajs/react";
import { useCallback } from "react";

export function ShowPlaylistProfile() {
    const player = usePlayer();
    const route = useZiggy();
    const auth = useAuthUser();

    const { likeCall } = useItemLike();

    const { props } = usePage<PlaylistShowPageProps>();
    const playlist = props.playlist;
    const articles = props.articles;

    const creator = props.playlist.user;
    const image = props.playlist.image || props.playlist.fallback_image;

    // Active player's playing
    const playing = player?.isPlaying;
    const playlistPlaying = Boolean(
        player?.playlistId && player.playlistId === playlist.id && playing
    );

    const creatorLink = route("app.artists.show", { user: creator?.id || "" });

    const onPlayClick = useCallback(() => {
        if (playlistPlaying) {
            player?.actions.pause();
            return;
        }

        player?.guessPlaylist({
            type: "articles",
            playlistId: playlist.id,
            data: articles,
        });
    }, [playlist, player, playlistPlaying, articles]);

    return (
        <div className="border-b border-gray-300/70 dark:border-gray-700">
            <GradientWrapper image={image}>
                {/* Playlist title */}
                <div className="flex space-x-2 items-center mt-6 mb-4 max-w-full">
                    <h1 className="text-xl lg:text-3xl font-semibold break-words max-w-[80%]">
                        {playlist.name}
                    </h1>

                    <div className="p-1 bg-slate-300/40 text-[0.65rem] rounded">
                        {"Playlist"}
                    </div>
                </div>

                {/* Creator name */}
                <div className={cn("flex space-x-2 my-4 items-center")}>
                    {creator &&
                        creator.id === auth?.id &&
                        playlist.is_system_generated && (
                            <h2 className="text-xs font-semibold">
                                {"Made for You"}
                            </h2>
                        )}

                    {creator && (
                        <Link
                            href={creatorLink}
                            className={cn(
                                creator && creator.id === auth?.id && ["hidden"]
                            )}
                        >
                            <Avatar
                                imageTitle={creator?.name}
                                size={30}
                                src={asset(creator?.image)}
                                alt={creator?.name}
                                className="bg-light-primary-700 dark:bg-dark-primary-background"
                            />
                        </Link>
                    )}

                    <h2
                        title={creator?.name}
                        className={cn(
                            "text-xs font-semibold",
                            creatorLink && [
                                "hover:text-primary-600 dark:hover:text-primary",
                            ],
                            creator && creator.id === auth?.id && ["hidden"]
                        )}
                    >
                        {/* Music name */}
                        {creator && (
                            <Link href={creatorLink}>{creator?.name}</Link>
                        )}
                    </h2>

                    {playlist?.created_at && (
                        <>
                            <span>⊛</span>
                            <span className="text-xs font-semibold hidden sm:inline">
                                {new Date(
                                    playlist?.created_at
                                ).toLocaleDateString("en-US", {
                                    year: "numeric",
                                    month: "long",
                                    day: "numeric",
                                })}
                            </span>

                            <span className="text-xs font-semibold inline sm:hidden">
                                {new Date(
                                    playlist?.created_at
                                ).toLocaleDateString("en-US", {
                                    year: "numeric",
                                    month: "long",
                                })}
                            </span>
                        </>
                    )}
                </div>

                {/* Profile actions */}
                <div className="flex items-center mt-4 space-x-4">
                    {/* Play button */}
                    <PlayButton
                        onClick={onPlayClick}
                        playing={playlistPlaying}
                    />

                    {/* Like button */}
                    {!playlist.is_system_generated && (
                        <div className="opacity-90 text-sm items-center flex space-x-1">
                            <button
                                type="button"
                                className={cn(
                                    playlist.liked && [
                                        "text-primary dark:text-primary-50",
                                    ]
                                )}
                                onClick={() => {
                                    if (playlist) {
                                        likeCall({
                                            type: "playlist",
                                            data: playlist,
                                        });
                                    }
                                }}
                            >
                                {playlist.liked ? (
                                    <IoHeart className="h-5 w-5" />
                                ) : (
                                    <IoHeartOutline className="h-5 w-5" />
                                )}
                            </button>
                        </div>
                    )}
                </div>
            </GradientWrapper>
        </div>
    );
}
