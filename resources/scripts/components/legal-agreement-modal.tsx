import { useState } from "react";
import { Button } from "./lib/ui";

interface LegalDocument {
    id: string;
    type: string;
    title: string;
    content: string;
    version: number;
}

interface LegalAgreementModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAgree: () => void;
    termsOfService: LegalDocument;
    privacyPolicy: LegalDocument;
    competitionRules: LegalDocument;
    competitionId: string;
    processing?: boolean;
}

export default function LegalAgreementModal({
    isOpen,
    onClose,
    onAgree,
    termsOfService,
    privacyPolicy,
    competitionRules,
    processing,
}: LegalAgreementModalProps) {
    const [activeTab, setActiveTab] = useState<"terms" | "privacy" | "rules">(
        "terms"
    );
    const [agreements, setAgreements] = useState({
        terms: false,
        privacy: false,
        rules: false,
    });

    const handleAgreementChange = (
        type: "terms" | "privacy" | "rules",
        checked: boolean
    ) => {
        setAgreements((prev) => ({ ...prev, [type]: checked }));
    };

    const allAgreed =
        agreements.terms && agreements.privacy && agreements.rules;

    const handleSubmit = () => {
        if (allAgreed) {
            // Set the form data and trigger the parent's onAgree callback
            onAgree();
        }
    };

    if (!isOpen) return null;

    const documents = {
        terms: termsOfService,
        privacy: privacyPolicy,
        rules: competitionRules,
    };

    const activeDocument = documents[activeTab];

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Legal Agreements Required
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                        Please review and agree to the following documents to
                        enter the competition.
                    </p>
                </div>

                {/* Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-700">
                    {[
                        {
                            key: "terms",
                            label: "Terms of Service",
                            agreed: agreements.terms,
                        },
                        {
                            key: "privacy",
                            label: "Privacy Policy",
                            agreed: agreements.privacy,
                        },
                        {
                            key: "rules",
                            label: "Competition Rules",
                            agreed: agreements.rules,
                        },
                    ].map(({ key, label, agreed }) => (
                        <button
                            key={key}
                            onClick={() =>
                                setActiveTab(key as typeof activeTab)
                            }
                            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                                activeTab === key
                                    ? "border-primary text-primary"
                                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                            }`}
                        >
                            {label}
                            {agreed && (
                                <span className="ml-2 text-green-500">✓</span>
                            )}
                        </button>
                    ))}
                </div>

                {/* Content */}
                <div className="flex-1 overflow-hidden flex flex-col">
                    <div className="p-6 flex-1 overflow-y-auto">
                        <div className="prose dark:prose-invert max-w-none">
                            <h3 className="text-xl font-semibold mb-4">
                                {activeDocument.title} (v
                                {activeDocument.version})
                            </h3>
                            <div
                                className="text-sm leading-relaxed prose prose-sm dark:prose-invert max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: activeDocument.content,
                                }}
                            />
                        </div>
                    </div>

                    {/* Agreement Checkbox */}
                    <div className="p-6 border-t border-gray-200 dark:border-gray-700">
                        <label className="flex items-start space-x-3">
                            <input
                                type="checkbox"
                                checked={agreements[activeTab]}
                                onChange={(e) =>
                                    handleAgreementChange(
                                        activeTab,
                                        e.target.checked
                                    )
                                }
                                className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            />
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                                I have read and agree to the{" "}
                                {activeDocument.title}
                            </span>
                        </label>
                    </div>
                </div>

                {/* Footer */}
                <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        {allAgreed ? (
                            <span className="text-green-600 dark:text-green-400 font-medium">
                                ✓ All agreements completed
                            </span>
                        ) : (
                            <span>
                                Please review and agree to all documents to
                                continue
                            </span>
                        )}
                    </div>
                    <div className="flex space-x-3">
                        <Button
                            variant="light"
                            onClick={onClose}
                            disabled={processing}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            onClick={handleSubmit}
                            disabled={!allAgreed || processing}
                            loading={processing}
                        >
                            Enter Competition
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}
