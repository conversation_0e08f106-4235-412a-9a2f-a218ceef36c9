import { cn } from "@/utils";
import debounce from "lodash/debounce";
import React, { useState } from "react";
import { PropsWithChildren, useEffect, useRef } from "react";

type SwiperController = {
    next: () => [number, number];
    prev: () => [number, number];
    jumpTo: (index: number) => [number, number];
    transitionEnd: (callback: () => void) => void;
    onPageChange: (callback: (page: [number, number]) => void) => void;
};

type SwiperControllerRef = React.MutableRefObject<
    SwiperController | undefined | null
>;

type FullPageSwiperProps = PropsWithChildren<{
    parentElement?: HTMLElement;
    mouseWheel?: boolean;
    direction?: "vertical" | "horizontal";
    dotPosition?: "left" | "right" | "bottom" | "top";
    showDots?: boolean;
    showArrowDown?: boolean;
}>;

type SwiperSlideType = {
    selected?: boolean;
    sliderIndex?: number;
    direction?: FullPageSwiperProps["direction"];
    width?: number;
    showArrowDown?: boolean;
    controller: SwiperControllerRef;
};

const SwiperSlideContext = React.createContext<SwiperSlideType>(
    undefined as any
);

export const FullPageSwiper = React.memo(
    ({
        children,
        mouseWheel = true,
        direction = "vertical",
        dotPosition,
        showDots = true,
        showArrowDown = false,
    }: FullPageSwiperProps) => {
        const el = useRef<HTMLDivElement>(null);
        const [selected, setSelected] = useState(0);
        const $controller = useRef<SwiperController>({} as SwiperController);

        const childsCount = React.Children.toArray(children).filter(
            (child) => React.isValidElement(child) && child.type === SwiperSlide
        ).length;

        useEffect(() => {
            if (!el.current) return;
            document.body.style.overflow = "hidden";
            if (direction === "horizontal") {
                el.current.style.overflow = "hidden";
            }
            return () => {
                document.body.style.overflow = "auto";
            };
        }, []);

        useEffect(() => {
            if (!el.current) return;
            const element = el.current!;
            const childs = childsCount - 1;
            const childIndex = new (class {
                private _v = 0;
                get v() {
                    return this._v;
                }
                set v(value: number) {
                    setSelected(value);
                    this._v = value;
                }
            })();

            let touchWheeled = false;
            let mouseWheeled = false;
            const DELTA_DIFF = 40;
            const TRANSITION_TIME = 1000;
            const TRANS_CB: (() => void)[] = [];
            const PAGE_EVENT_CB: ((page: [number, number]) => void)[] = [];

            const activeElement = () => {
                element
                    .querySelector(".slider-active")
                    ?.classList.remove("slider-active");

                const children = Array.from(element.children);
                children[childIndex.v].classList.add("slider-active");
            };

            activeElement();

            const transformUpdate = (client: number, pageSwitch = true) => {
                if (direction === "vertical") {
                    element.style.transform = `translate3d(0px, -${
                        client * childIndex.v
                    }px, 0px)`;

                    TRANS_CB.forEach(
                        (cb) => cb && window.setTimeout(cb, TRANSITION_TIME)
                    );

                    pageSwitch &&
                        PAGE_EVENT_CB.forEach(
                            (cb) => cb && cb([childIndex.v, childs])
                        );
                } else {
                    element.style.transform = `translate3d(-${
                        (client / (childs + 1)) * childIndex.v
                    }px, 0px, 0px)`;

                    TRANS_CB.forEach(
                        (cb) => cb && window.setTimeout(cb, TRANSITION_TIME)
                    );

                    pageSwitch &&
                        PAGE_EVENT_CB.forEach(
                            (cb) => cb && cb([childIndex.v, childs])
                        );
                }
            };

            const client = () =>
                direction === "vertical"
                    ? element.clientHeight
                    : element.clientWidth;

            $controller.current.next = () => {
                if (childIndex.v < childs) {
                    childIndex.v += 1;
                    transformUpdate(client());
                }
                return [childIndex.v, childs];
            };

            $controller.current.prev = () => {
                if (childIndex.v > 0) {
                    childIndex.v -= 1;
                    transformUpdate(client());
                }
                return [childIndex.v, childs];
            };

            $controller.current.jumpTo = (index: number) => {
                if (index <= childs) {
                    childIndex.v = index;
                    transformUpdate(client());
                }
                return [childIndex.v, childs];
            };

            $controller.current.transitionEnd = (callback) => {
                TRANS_CB.push(callback);
            };

            $controller.current.onPageChange = (callback) => {
                PAGE_EVENT_CB.push(callback);
            };

            const onMouseWheel = (ev: WheelEvent) => {
                if (mouseWheeled || !mouseWheel) return;
                const delta = direction === "vertical" ? ev.deltaY : ev.deltaX;

                if (delta < 0 && childIndex.v > 0) {
                    childIndex.v -= 1;
                } else if (delta >= DELTA_DIFF && childIndex.v < childs) {
                    childIndex.v += 1;
                } else {
                    return;
                }

                transformUpdate(client());

                mouseWheeled = true;

                activeElement();
            };

            const clearTouchEvents = trackTouchEvent(element, {
                delta_diff: DELTA_DIFF,
                onTouchDirection(_direction) {
                    if (touchWheeled) {
                        return;
                    }

                    if (
                        _direction.startsWith("h-") &&
                        direction === "vertical"
                    ) {
                        return;
                    } else if (
                        _direction.startsWith("v-") &&
                        direction === "horizontal"
                    ) {
                        return;
                    }

                    if (
                        (_direction === "h-right" || _direction === "v-down") &&
                        childIndex.v > 0
                    ) {
                        childIndex.v -= 1;
                    } else if (
                        (_direction === "h-left" || _direction === "v-up") &&
                        childIndex.v < childs
                    ) {
                        childIndex.v += 1;
                    }

                    transformUpdate(client());

                    touchWheeled = true;

                    activeElement();
                },
                onTouchEnd() {
                    window.setTimeout(() => {
                        touchWheeled = false;
                        element.focus();
                    }, 250);
                },
            });

            const onMouseWheelDebounce = (_: WheelEvent) => {
                if (!mouseWheel) return;
                mouseWheeled = false;
                element.focus();
            };

            const onResize = (_: UIEvent) => {
                element.style.transition = "none";
                if (childs < 0) return;
                transformUpdate(client(), false);
            };

            const onResizeDebounce = (_: UIEvent) => {
                element.style.transition = `all ${TRANSITION_TIME}ms ease 0s`;
            };

            element.addEventListener("wheel", onMouseWheel);
            element.addEventListener(
                "wheel",
                debounce(onMouseWheelDebounce, 250)
            );
            window.addEventListener("resize", onResize);
            window.addEventListener("resize", debounce(onResizeDebounce, 250));

            return () => {
                clearTouchEvents();
                window.removeEventListener("resize", onResize);
                window.removeEventListener("resize", onResizeDebounce);
                element.removeEventListener("wheel", onMouseWheel);
                element.addEventListener("wheel", onMouseWheelDebounce);
            };
        }, []);

        const newChildren = React.Children.map(children, (child, index) => {
            if (!React.isValidElement(child) || child?.type !== SwiperSlide) {
                return child;
            }

            return (
                <SwiperSlideContext.Provider
                    key={index}
                    value={{
                        selected: index === selected,
                        sliderIndex: index,
                        direction,
                        width: 100 / childsCount,
                        showArrowDown,
                        controller: $controller,
                    }}
                >
                    {child}
                </SwiperSlideContext.Provider>
            );
        });

        return (
            <>
                <div
                    ref={el}
                    style={{
                        width:
                            direction === "horizontal"
                                ? `${childsCount * 100}%`
                                : undefined,
                    }}
                    className="fps__wrapper"
                >
                    {newChildren}
                </div>

                {showDots && (
                    <div
                        className={`fps__dots fps__dots-${
                            direction === "horizontal"
                                ? dotPosition || "bottom"
                                : dotPosition || "right"
                        }`}
                    >
                        <ul>
                            {new Array(childsCount)
                                .fill(null)
                                .map((_, index) => {
                                    return (
                                        <li key={index}>
                                            <a
                                                style={{ cursor: "pointer" }}
                                                onClick={() =>
                                                    $controller.current?.jumpTo(
                                                        index
                                                    )
                                                }
                                                className={
                                                    index === selected
                                                        ? "active"
                                                        : undefined
                                                }
                                            >
                                                <span />
                                            </a>
                                        </li>
                                    );
                                })}
                        </ul>
                    </div>
                )}
            </>
        );
    }
);

FullPageSwiper.displayName = "FullPageSwiper";

type SwiperSlideProps = PropsWithChildren<{
    className?: string;
    bgColor?: string;
}>;

export const SwiperSlide = (props: SwiperSlideProps) => {
    const { children, className = "", bgColor } = props;

    const { sliderIndex, direction, width, showArrowDown, controller } =
        useSwiperSlide();

    const elRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!elRef.current) return;
        const parent = elRef.current.parentElement!;
        const element = elRef.current!;

        const onResize = () => {
            switch (direction) {
                case "vertical":
                    element.style.height = `${parent.clientHeight}px`;
                    break;
                case "horizontal":
                    break;
            }
        };
        window.requestAnimationFrame(onResize);

        if (direction === "horizontal" && width !== undefined) {
            element.style.width = `${width}%`;
        }

        window.addEventListener("resize", onResize);
        return () => {
            window.removeEventListener("resize", onResize);
        };
    }, []);

    return (
        <div
            ref={elRef}
            data-slider={sliderIndex}
            style={{
                display: direction === "horizontal" ? "inline-block" : "block",
                backgroundColor: bgColor,
            }}
            className={cn(`fps__slider`, className)}
        >
            {children}

            {showArrowDown && sliderIndex === 0 && (
                <div
                    className={
                        direction === "vertical"
                            ? "fps__arrow-down"
                            : "fps__arrow-right"
                    }
                >
                    <svg onClick={() => controller?.current?.next()}>
                        <path d="M12 18c-.2 0-.5-.1-.7-.3l-11-10c-.4-.4-.4-1-.1-1.4.4-.4 1-.4 1.4-.1l10.4 9.4 10.3-9.4c.4-.4 1-.3 1.4.1.4.4.3 1-.1 1.4l-11 10c-.1.2-.4.3-.6.3z" />
                    </svg>
                </div>
            )}
        </div>
    );
};

function trackTouchEvent(
    element: HTMLElement,
    params: {
        delta_diff?: number;
        onTouchDirection?: (
            direction: "h-right" | "h-left" | "v-up" | "v-down"
        ) => void;
        onTouchEnd?: (e: TouchEvent) => void;
    }
) {
    const { delta_diff = 40 } = params;

    let touchStartX = 0;
    let touchStartY = 0;

    const touchstart = (e: TouchEvent) => {
        const touch = e.touches[0];
        touchStartX = touch.clientX;
        touchStartY = touch.clientY;
    };

    const touchmove = (e: TouchEvent) => {
        const touch = e.touches[0];
        const touchCurrentX = touch.clientX;
        const touchCurrentY = touch.clientY;

        const deltaX = touchCurrentX - touchStartX;
        const deltaY = touchCurrentY - touchStartY;

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > delta_diff) {
                params.onTouchDirection && params.onTouchDirection("h-right");
            } else if (-delta_diff > deltaX) {
                params.onTouchDirection && params.onTouchDirection("h-left");
            }
        } else {
            if (deltaY > delta_diff) {
                params.onTouchDirection && params.onTouchDirection("v-down");
            } else if (-delta_diff > deltaY) {
                params.onTouchDirection && params.onTouchDirection("v-up");
            }
        }
    };

    const touchend = (e: TouchEvent) => {
        touchStartX = 0;
        touchStartY = 0;
        params.onTouchEnd && params.onTouchEnd(e);
    };

    element.addEventListener("touchstart", touchstart);
    element.addEventListener("touchmove", touchmove);
    element.addEventListener("touchend", touchend);
    return () => {
        element.removeEventListener("touchstart", touchstart);
        element.removeEventListener("touchmove", touchmove);
        element.removeEventListener("touchend", touchend);
    };
}

export function useSwiperSlide() {
    const context = React.useContext(SwiperSlideContext);
    if (!context) {
        throw new Error(
            "useSwiperSlide must be used within a SwiperSlideContext"
        );
    }

    return context;
}

export function useSwiperActiveSlide(defaults = false) {
    const [active, setActive] = useState(defaults);
    const { controller, sliderIndex } = useSwiperSlide();

    useEffect(() => {
        if (!controller?.current) return;
        const ref = controller.current;

        ref.onPageChange &&
            ref.onPageChange((page: [number, number]) =>
                setActive(page[0] === sliderIndex)
            );
    }, [controller?.current]);

    return {
        active,
        controller,
    };
}
