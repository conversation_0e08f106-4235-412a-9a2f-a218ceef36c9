import { useState, useLayoutEffect } from "react";

const HEIGHT_RATIO = 3.375;

const getImage = () => {
    return {
        ios: `https://cdn.iconscout.com/icon/free/png-512/free-app-store-2844886-2365235.png?f=avif&w=512`,
        android: `https://cdn.iconscout.com/icon/free/png-512/free-google-play-store-2038773-1721660.png?f=avif&w=512`,
    };
};
interface ReactStoreBadgesProps {
    /** url of App Store and Play Store */
    url: string;

    /** platform name. 'ios' and 'android' only */
    platform: "ios" | "android";

    /** default locale code. default is en-us */
    defaultLocale?: string;

    /** locale name. such as en-us */
    locale?: string;

    /** width for badge size */
    width?: number;

    /** height for badge size */
    height?: number;

    /** target for url to be opened */
    target?: "_self" | "_blank" | "_parent" | "_top";
}

export const ReactStoreBadges = ({
    url,
    platform,

    target = "_self",
}: ReactStoreBadgesProps) => {
    const [image] = useState(getImage());

    return (
        <a href={url} target={target} className="inline-block w-full h-full">
            <img
                src={image[platform]}
                style={{
                    width: "100%",
                    height: "100%",
                }}
            />
        </a>
    );
};
