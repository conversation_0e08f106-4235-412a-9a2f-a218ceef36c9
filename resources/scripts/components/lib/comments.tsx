import { <PERSON><PERSON><PERSON><PERSON>Hook, useAuth<PERSON><PERSON>, useZiggy } from "@/hooks";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ner<PERSON>oa<PERSON> } from "./ui";
import { ItemType, Paginate } from "@/types";
import { FormEvent, useCallback, useEffect, useRef, useState } from "react";
import { asset, fetchApi } from "@/utils";
import { Comment } from "@/types/models";
import { IoPencil, IoTrashOutline } from "./icons";
import { router } from "@inertiajs/react";

type CommentsProps = {
    item: ItemType;
    modal: IModalHook;
};

export function useComments(item: ItemType) {
    // Refs
    const loaded = useRef(false);

    // Local state
    const [pagination, setPagination] = useState<Paginate<Comment> | null>(
        null
    );
    const [comments, setComments] = useState<Comment[]>([]);
    const [loading, setLoading] = useState(false);
    const [text, setText] = useState("");
    const [count, setCount] = useState(0);

    // Route ziggy
    const route = useZiggy();

    const nextUrl = pagination?.links.next;

    const fetchComment = useCallback(
        (url?: string) => {
            setLoading(true);
            fetchApi<Paginate<Comment>>(
                url ||
                    route(`app.comments.${item.type}`, {
                        [item.type]: item.data.id,
                    })
            )
                .then((value) => {
                    setPagination(value);
                    setComments((ps) => ps.concat(value.data));
                })
                .finally(() => setLoading(false));
        },
        [item, setPagination, setLoading, setComments]
    );

    useEffect(() => {
        if (!loaded.current) {
            fetchComment();
            loaded.current = true;
        }
    }, []);

    useEffect(() => {
        if (pagination) {
            setCount(pagination.meta.total);
        }
    }, [pagination]);

    const handleCreate = (e: FormEvent) => {
        e.preventDefault();

        if (text.length < 2) {
            return;
        }

        fetchApi<Comment>(
            route(`app.comments.${item.type}.create`, {
                [item.type]: item.data.id,
            }),
            {
                method: "post",
                body: JSON.stringify({ comment: text }),
            }
        )
            .then((data) => {
                setComments((ps) => [data].concat(ps));
                setCount((c) => c + 1);
            })
            .finally(() => {
                router.reload({});
            });

        setText("");
    };

    let type = "";
    switch (item.type) {
        case "article":
            type = "audio";
            break;
        case "playlist":
            type = "playlist";
            break;
        case "channel":
            type = item.data.type;
            break;
    }

    return {
        item,
        type,
        comments,
        count,
        loading,
        text,
        nextUrl,
        setText,
        handleCreate,
        fetchComment,
        setComments,
        setCount,
    };
}

export function Comments(props: CommentsProps) {
    const {
        item,
        type,
        comments,
        count,
        loading,
        text,
        setText,
        handleCreate,
        fetchComment,
        setComments,
        setCount,
        nextUrl,
    } = useComments(props.item);

    return (
        <Drawer
            {...props.modal}
            title={
                <span className="flex flex-col w-full">
                    <span>
                        {count} Comments{" "}
                        <span className="uppercase text-xs opacity-25">
                            {type}
                        </span>
                    </span>
                    <span className="capitalize text-xs">{item.data.name}</span>
                </span>
            }
        >
            <form action="post" className="w-full" onSubmit={handleCreate}>
                <InputField
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder="Add comment"
                />
            </form>

            <hr className="dark:opacity-40 my-2" />

            {comments.map((data) => {
                return (
                    <CommentText
                        onDelete={(id) => {
                            setComments((ps) => ps.filter((c) => c.id !== id));
                            setCount((c) => c - 1);
                        }}
                        key={data.id}
                        comment={data}
                    />
                );
            })}

            {nextUrl && (
                <div className="w-full flex">
                    <Button
                        variant="outline"
                        loading={loading}
                        disabled={loading}
                        className="border border-primary text-sm"
                        onClick={() => fetchComment(nextUrl)}
                    >
                        Load more
                    </Button>
                </div>
            )}

            {!nextUrl && loading && (
                <div className="w-full flex justify-center">
                    <SpinnerLoader />
                </div>
            )}
        </Drawer>
    );
}

export function CommentText({
    comment: $comment,
    onDelete,
}: {
    comment: Comment;
    onDelete?: (id: string) => void;
}) {
    // Inetia
    const auth = useAuthUser();

    // Local state
    const [edit, setEdit] = useState(false);
    const [comment, setComment] = useState($comment);
    const [text, setText] = useState(comment.comment);

    const route = useZiggy();

    const user = comment.user;

    useEffect(() => {
        setText(comment.comment);
    }, [comment]);

    const handleEdit = (e: FormEvent) => {
        e.preventDefault();
        if (text.length < 2) {
            return;
        }

        setEdit(false);

        if (text === comment.comment) {
            return;
        }

        fetchApi<Comment>(
            route("app.comments.update", { comment: $comment.id }),
            {
                method: "put",
                body: JSON.stringify({ comment: text }),
            }
        ).then((data) => setComment(data));
    };

    const handleDelete = (e: FormEvent) => {
        e.preventDefault();

        if (!confirm("Are you sure ?")) {
            return;
        }

        fetchApi(route("app.comments.delete", { comment: $comment.id }), {
            method: "delete",
        }).finally(() => {
            router.reload({});
        });

        onDelete && onDelete(comment.id);
    };

    return (
        <div className="w-full border-b mb-3 border-slate-300/50 dark:border-slate-300/10 pb-3">
            <div className="my-2 flex space-x-1 items-center">
                <Avatar
                    imageTitle={user?.name}
                    size={30}
                    src={asset(user?.image)}
                    alt={user?.name}
                    className="bg-light-primary-background dark:bg-dark-primary-background"
                />

                <div className="text-sm flex-1 truncate font-semibold">
                    {user?.name}
                </div>

                {auth?.id === comment.user?.id && (
                    <div className="flex space-x-3 justify-end">
                        <button
                            type="button"
                            className="dark:text-primary text-primary-600"
                            onClick={() => setEdit((e) => !e)}
                        >
                            <IoPencil />
                        </button>

                        <button
                            type="button"
                            className="text-red-300"
                            onClick={handleDelete}
                        >
                            <IoTrashOutline />
                        </button>
                    </div>
                )}
            </div>

            <div className="w-full mt-1">
                {!edit && (
                    <div className="flex space-x-2 justify-between">
                        <p className="pl-1 text-sm font-normal">{text}</p>
                        {comment.readable_date && (
                            <div className="text-xs opacity-40 text-right mb-1">
                                {comment.readable_date}
                            </div>
                        )}
                    </div>
                )}

                {edit && (
                    <form onSubmit={handleEdit}>
                        <InputField
                            type="textarea"
                            name="comment"
                            placeholder="Comment Text"
                            value={text}
                            onKeyDown={(e) => e.keyCode === 13 && handleEdit(e)}
                            onChange={(e) => setText(e.target.value)}
                        />
                    </form>
                )}
            </div>
        </div>
    );
}
