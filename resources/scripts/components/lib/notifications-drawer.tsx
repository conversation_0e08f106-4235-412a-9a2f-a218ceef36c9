import { IModalHook, useIPage, useZiggy } from "@/hooks";
import { Drawer } from "./ui";
import { HiBell, IoClose } from "./icons";
import { Notification } from "@/types/models";
import { cn } from "@/utils";
import { Link } from "@inertiajs/react";

type NotificationsDrawerProps = {
    modal: IModalHook;
};

export function NotificationsDrawer({ modal }: NotificationsDrawerProps) {
    const { props } = useIPage();
    const route = useZiggy();

    const notifications = props.notifications;
    const hasNotifications = notifications.length > 0;

    return (
        <Drawer
            {...modal}
            title={
                <span>
                    Notifications
                    {hasNotifications && (
                        <>
                            <br />
                            <Link
                                as="button"
                                method="post"
                                href={route("app.notifications.clear")}
                                className="capitalize text-xs underline"
                            >
                                clear
                            </Link>
                        </>
                    )}
                </span>
            }
        >
            {notifications.map((notif) => {
                return <NotificationItem {...notif} key={notif.id} />;
            })}

            {!hasNotifications && (
                <div className="flex justify-center items-center flex-col space-y-2 mt-14">
                    <button className="px-[0.4rem] py-[0.4rem] md:px-2 md:py-2 rounded-full bg-light-primary-background dark:bg-dark-primary-700">
                        <HiBell className="text-dark-primary dark:text-light h-4 w-4" />
                    </button>

                    <div className="font-semibold text-center">
                        No notifications here
                    </div>

                    <div className="opacity-70 text-sm text-center">
                        Please check again later
                    </div>
                </div>
            )}
        </Drawer>
    );
}

function NotificationItem({ id, title, readable_date, body }: Notification) {
    const route = useZiggy();

    return (
        <div
            className="w-full py-2 border-b dark:border-gray-700"
            title={title}
            id={id}
        >
            <div className="flex space-x-2 justify-between items-center">
                <h3
                    className={cn(
                        "w-full overflow-x-hidden text-ellipsis text-sm",
                        "font-semibold whitespace-nowrap"
                    )}
                >
                    {title}
                </h3>

                <Link
                    as="button"
                    method="delete"
                    href={route("app.notifications.destroy", {
                        notificationId: id,
                    })}
                    className="opacity-90"
                >
                    <IoClose />
                </Link>
            </div>

            {readable_date && (
                <div className="text-xs opacity-40 my-1">{readable_date}</div>
            )}

            {body && (
                <div className="text-xs mt-3 text-gray-500 dark:text-gray-300">
                    <p>{body}</p>
                </div>
            )}
        </div>
    );
}
