import { Transition } from "@headlessui/react";
import {
    PropsWithChildren,
    createContext,
    useCallback,
    useContext,
    useRef,
    useState,
} from "react";
import { Toast, IToast } from "./ui";

type ToastItem = IToast & {
    id?: number;
    timer?: NodeJS.Timeout;
    duration?: number;
};

const defaultPush = (
    toast: Pick<ToastItem, "duration" | "title" | "type">
) => {}; // Méthode de base que l'on mettra dans le contexte par défaut

const ToastContext = createContext({
    pushToastRef: { current: defaultPush },
});

// On entourera notre application de ce provider pour rendre le toasts fonctionnel
export function ToastContextProvider({ children }: PropsWithChildren) {
    const pushToastRef = useRef(defaultPush);
    return (
        <ToastContext.Provider value={{ pushToastRef }}>
            <Toasts />
            {children}
        </ToastContext.Provider>
    );
}

export function useToasts() {
    const { pushToastRef } = useContext(ToastContext);
    return {
        pushToast: useCallback(
            (toast: Pick<ToastItem, "title" | "type" | "duration">) => {
                pushToastRef.current(toast);
            },
            [pushToastRef]
        ),
    };
}

function Toasts() {
    const [toasts, setToasts] = useState<ToastItem[]>([]);
    // On modifie la méthode du contexte
    const { pushToastRef } = useContext(ToastContext);

    pushToastRef.current = ({ duration, ...props }) => {
        // On génère un id pour différencier les messages
        const id = Date.now();
        // On sauvegarde le timer pour pouvoir l'annuler si le message est fermé
        const timer = setTimeout(() => {
            setToasts((v) => v.filter((t) => t.id !== id));
        }, (duration ?? 5) * 1000);

        const toast = { ...props, id, timer };

        setToasts((v) => [...v, toast]);
    };

    const onRemove = (toast: ToastItem) => {
        clearTimeout(toast.timer);
        setToasts((v) => v.filter((t) => t !== toast));
    };

    return (
        <div className="fixed right-5 top-5 z-[1000]">
            {toasts.map((toast) => (
                <Transition
                    key={toast.id}
                    show={true}
                    enter="transition-opacity duration-75"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition-opacity duration-150"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <Toast
                        title={toast.title}
                        type={toast.type}
                        onClose={() => onRemove(toast)}
                    />
                </Transition>
            ))}
        </div>
    );
}
