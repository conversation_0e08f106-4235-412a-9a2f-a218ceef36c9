import { cn } from "@/utils";
import { IoPauseSharp, IoPlaySharp } from "./icons";
import { Button } from "./ui";

type PlayButtonProps = {
    loading?: boolean;
    onClick?: () => void;
    playing?: boolean;
    className?: string;
};

export function PlayButton(props: PlayButtonProps) {
    return (
        <Button
            type="button"
            variant="light"
            className={cn(
                "btn-play disabled:opacity-100 p-3 text-primary dark:text-primary-600",
                props.className
            )}
            shape="circle"
            spinnerLoaderSize={13}
            replaceValueOnLoading={true}
            onClick={props.onClick}
            loading={props.loading}
        >
            {!props.loading && (
                <>
                    {props.playing ? (
                        <IoPauseSharp className="text-dark" />
                    ) : (
                        <IoPlaySharp />
                    )}
                </>
            )}
        </Button>
    );
}
