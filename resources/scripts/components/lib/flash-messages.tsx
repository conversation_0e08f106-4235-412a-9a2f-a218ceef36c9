import { useIPage } from "@/hooks";
import { useToasts } from "./toasts";
import { useEffect } from "react";
import { useDebouncedCallback } from "use-debounce";

export function FlashMessages() {
    const toast = useToasts();
    const { props } = useIPage();

    const callFash = useDebouncedCallback(() => {
        if (props.flash?.message) {
            toast.pushToast({
                title: props.flash.message,
                type: "default",
            });
        }

        if (props.flash?.error) {
            toast.pushToast({
                title: props.flash.error,
                type: "error",
            });
        }

        if (props.flash?.success) {
            toast.pushToast({
                title: props.flash.success,
                type: "success",
            });
        }
    }, 100);

    useEffect(() => {
        callFash();
    }, [props.flash]);

    return null;
}
