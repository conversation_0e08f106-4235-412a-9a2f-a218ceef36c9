import { User } from "@/types/models";
import { asset, cn } from "@/utils";
import { Avatar } from "./ui";
import { Link } from "@inertiajs/react";
import { useZiggy } from "@/hooks";

type UserCardProps = {
    user: User;
    className?: string;
    link?: string;
};

export function UserListItem(props: UserCardProps) {
    const route = useZiggy();

    const image = props.user.image;
    const user = props.user;

    const ellipsis =
        "block overflow-x-hidden text-ellipsis whitespace-nowrap max-w-full";

    const link = props.link || route("app.artists.show", { user: user.id });

    return (
        <article
            className={cn(
                "relative p-0 max-w-full flex flex-nowrap flex-col snap-start rounded",
                "flex flex-row items-center space-x-3 group/article",
                props.className
            )}
        >
            <Avatar
                imageTitle={user.name}
                src={asset(image)}
                href={link}
                className="bg-light-primary-700 dark:bg-dark-primary-background rounded-full size-16 lg:size-[5.5rem]"
            />

            <div className="flex flex-col space-y-1 h-auto w-full flex-1 overflow-x-hidden">
                <h3
                    title={user.name}
                    className={cn(
                        "dark:text-light/90",
                        ellipsis,
                        "hover:text-primary-600 dark:hover:text-primary group-hover/article:text-primary-600"
                    )}
                >
                    <Link href={link}>{user.name}</Link>
                </h3>

                <span
                    className={cn("text-slate-400 text-sm uppercase", ellipsis)}
                >
                    {user.role}
                </span>
            </div>
        </article>
    );
}
