import { User } from "@/types/models";
import { asset, cn } from "@/utils";
import { Avatar } from "./ui";
import { Link } from "@inertiajs/react";
import { useZiggy } from "@/hooks";

type UserCardProps = {
    user: User;
    layout: "fill" | "static";
};

export function UserCard(props: UserCardProps) {
    const route = useZiggy();

    const image = props.user.image;
    const user = props.user;

    const ellipsis =
        "block overflow-x-hidden text-ellipsis whitespace-nowrap max-w-full";

    const link = route("app.artists.show", {
        user: user.id,
    });

    return (
        <article
            className={cn(
                "relative p-0 max-w-full flex flex-nowrap flex-col snap-start",
                "hover:bg-slate-400/25 p-1 rounded-md transition-colors"
            )}
        >
            <Avatar
                imageTitle={user.name}
                src={asset(image)}
                href={link}
                layout={props.layout === "fill" ? "fill" : undefined}
                className={cn(
                    "bg-light-primary-700 dark:bg-dark-primary-background rounded-full",
                    props.layout === "static" && ["h-28 w-28 lg:h-36 lg:w-36"]
                )}
            />

            <div className="flex flex-col space-y-1 mt-2 h-auto items-center w-full">
                <h3
                    title={user.name}
                    className={cn(
                        "dark:text-light/90",
                        ellipsis,
                        "hover:text-primary-600 dark:hover:text-primary"
                    )}
                >
                    <Link href={link}>{user.name}</Link>
                </h3>

                <span
                    className={cn("text-slate-400 text-sm uppercase", ellipsis)}
                >
                    {user.role}
                </span>
            </div>
        </article>
    );
}
