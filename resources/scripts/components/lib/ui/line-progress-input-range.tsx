import { useCallbackRef, useRefSync } from "@/hooks";
import { cn } from "@/utils";
import debounce from "lodash/debounce";
import { useEffect, useRef, useState } from "react";

type LineProgressInputRangeType = {
    min: number;
    max: number;
    value: number;
    className?: string;
    disabled?: boolean;
    onReleased?: (value: number) => Promise<void> | void;
    onChange?: (value: number) => void;
};

const MAX = 100;
const MIN = 0;

export const LineProgressInputRange = (props: LineProgressInputRangeType) => {
    const { min, max, value, disabled, className, onReleased, onChange } =
        props;

    // Init values
    const get_value = (v: number) => (v * MAX) / max;
    const pvalue = get_value(value > max || value < min ? min : value) || 0;

    // Local state
    const [ivalue, setIValue] = useState(pvalue);
    const pourcent = (ivalue * 100) / MAX;

    // Refs
    const inputRef = useRef<HTMLInputElement | null>(null);
    const hasSwiperRange = useRef(false);
    const $ivalue = useRefSync(ivalue);
    const $max = useRefSync(max);
    const $onReleased = useCallbackRef(onReleased);

    const changeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (min >= max) {
            return;
        }

        const target = e.target;
        const val = +target.value;
        setIValue(val);

        hasSwiperRange.current = true;
        onChange && onChange(val * (max * 0.01));
    };

    useEffect(() => {
        if (hasSwiperRange.current) {
            return;
        }
        setIValue(pvalue);
    }, [pvalue]);

    useEffect(() => {
        if (!inputRef.current) return;
        inputRef.current.style.backgroundSize = pourcent + "% 100%";
    }, [pourcent]);

    useEffect(() => {
        const handleInput = () => {
            document.addEventListener("mouseup", handleMouseUp);
            document.addEventListener("touchend", handleMouseUp);
        };

        const handleMouseUp = debounce((ev: MouseEvent | TouchEvent) => {
            ev.stopPropagation();

            document.removeEventListener("mouseup", handleMouseUp);
            document.removeEventListener("touchend", handleMouseUp);

            // Handle thumb release event here
            if (hasSwiperRange.current) {
                $onReleased.current &&
                    $onReleased.current(
                        $ivalue.current * ($max.current * 0.01)
                    );

                hasSwiperRange.current = false;
            }
        }, 200);

        inputRef.current?.addEventListener("input", handleInput);
        return () => {
            inputRef.current?.removeEventListener("input", handleInput);
            document.removeEventListener("mouseup", handleMouseUp);
            document.removeEventListener("touchend", handleMouseUp);
        };
    }, []);

    return (
        <input
            type="range"
            ref={inputRef}
            disabled={disabled}
            className={cn(
                `bg-primary bg-[length:0]`,
                "bg-gradient-to-r from-primary-400 to-primary-400 bg-no-repeat",
                "range",
                "disabled:opacity-70 disabled:!cursor-default",
                className
            )}
            value={ivalue}
            min={MIN}
            max={MAX}
            onChange={changeHandler}
        />
    );
};
