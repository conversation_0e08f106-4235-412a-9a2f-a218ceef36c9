import { cn } from "@/utils";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, PropsWithChildren, useRef } from "react";
import { IoClose } from "../icons";

type Props = PropsWithChildren<{
    title?: string;
    description?: string;
    isOpen: boolean;
    closeModal: () => void;
    className?: string;
}>;

export function Modal({
    isOpen,
    closeModal,
    children,
    title,
    description,
    className,
}: Props) {
    const refDiv = useRef(null);

    return (
        <Transition
            show={isOpen}
            enter="transition duration-100 ease-out"
            enterFrom="transform scale-95 opacity-0"
            enterTo="transform scale-100 opacity-100"
            leave="transition duration-75 ease-out"
            leaveFrom="transform scale-100 opacity-100"
            leaveTo="transform scale-95 opacity-0"
            as={Fragment}
        >
            <Dialog
                initialFocus={refDiv}
                onClose={closeModal}
                as="div"
                className="fixed inset-0 backdrop-brightness-100 backdrop-blur-sm z-[999]"
            >
                <div
                    ref={refDiv}
                    className="absolute inset-0 flex items-center justify-center p-4"
                >
                    <Dialog.Panel className={className}>
                        {title && <Dialog.Title>{title}</Dialog.Title>}
                        {description && (
                            <Dialog.Description>
                                {description}
                            </Dialog.Description>
                        )}

                        {children}
                    </Dialog.Panel>
                </div>
            </Dialog>
        </Transition>
    );
}

type IModalBody = PropsWithChildren<{
    className?: string;
    onClose?: () => void;
}>;

export function ModalBody(props: IModalBody) {
    return (
        <div
            className={cn(
                "bg-light-primary-background dark:bg-dark-primary-background relative",
                "w-11/12 md:w-1/2 lg:w-5/12 p-10 rounded-lg",
                props.className
            )}
        >
            {props.onClose && (
                <span
                    onClick={props.onClose}
                    className="top-4 right-10 absolute cursor-pointer"
                >
                    <IoClose className="w-5 h-5 dark:text-light" />
                </span>
            )}

            {props.children}
        </div>
    );
}
