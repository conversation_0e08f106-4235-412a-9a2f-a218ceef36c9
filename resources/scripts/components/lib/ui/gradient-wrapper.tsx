import { Image } from "@/types/models";
import { asset, cn } from "@/utils";
import { PropsWithChildren } from "react";

type GradientWrapperProps = PropsWithChildren<{
    image?: Image;
    direction?: "top-bottom" | "bottom-top";
    className?: string;
}>;

export function GradientWrapper(props: GradientWrapperProps) {
    const { direction = "top-bottom" } = props;
    return (
        <div
            className={cn(
                "relative rounded-md",
                "bg-fixed bg-center bg-cover bg-no-repeat p-6",
                "min-h-[14rem] w-full",
                "flex justify-center flex-col",
                !props.image && ["bg-gradient-to-r from-sky-500 to-indigo-500"],
                props.className
            )}
            style={{
                backgroundImage: props.image
                    ? `url(${asset(props.image)})`
                    : undefined,
            }}
        >
            {/* Overlay background */}
            <div
                className={cn(
                    "absolute inset-0 isolate backdrop-blur-[2px]",
                    direction === "top-bottom" && [
                        "dark:bg-gradient-to-b dark:from-dark-primary/90 dark:to-dark-primary",
                        "bg-gradient-to-b from-light-primary/90 to-light-primary",
                    ],

                    direction === "bottom-top" && [
                        "dark:bg-gradient-to-b dark:from-dark-primary dark:to-dark-primary/90",
                        "bg-gradient-to-b from-light-primary to-light-primary/90",
                    ]
                )}
            />

            <div className="flex justify-center flex-col z-10">
                {props.children}
            </div>
        </div>
    );
}
