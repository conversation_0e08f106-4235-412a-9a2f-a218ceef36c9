import { Image } from "@/types/models";
import { asset, cn, moduleResolve } from "@/utils";
import gradient from "@privjs/gradients";
import { Button } from "./button";
import {
    IoHeart,
    IoHeartOutline,
    IoPlaySharp,
    IoPauseSharp,
    // --- Icons for item types ---
    IoShuffle, // For PlaylistMix
    IoRadioOutline, // For Channel
    IoMusicalNotes, // For Playlist
    IoMicOutline, // For Podcast
    IoPersonOutline, // For Artist
    IoMusicalNoteSharp, // Fir single Audio track
    IoDiscOutline, // For Album
    // --- Add other icons as needed ---
} from "../icons"; // Assuming '../icons' exports these from react-icons/io5 or similar
import { Link } from "@inertiajs/react";

const $gradient = moduleResolve(gradient);

// Define possible item types (including the new ones)
export type ItemViewType =
    | "Playlist"
    | "PlaylistMix"
    | "Channel"
    | "Audio"
    | "Artist"
    | "Album"
    | "AlbumSingle"
    | "Podcast";

export type ArticleFigureProps = {
    id: string;
    image?: Image | string;
    title?: string;
    href?: string;
    entryAction?: {
        onPlayClick?: () => void;
        onLikeClick?: () => void;
    };
    liked?: boolean;
    className?: string;
    playing?: boolean;
    playLoading?: boolean;
    // --- itemType prop remains the same ---
    itemType?: ItemViewType;
};

// Helper component for the type indicator icon
const ItemTypeIndicator = ({ itemType }: { itemType: ItemViewType }) => {
    let IconComponent = null;
    let iconTitle = "";
    // Define distinct positions for the icons we implement
    let positionClasses = "top-2 left-2"; // Default (can be overridden)

    switch (itemType) {
        case "Playlist":
            IconComponent = IoMusicalNotes;
            iconTitle = "Playlist";
            positionClasses = "top-2 left-2"; // Top-left
            break;
        case "PlaylistMix":
            IconComponent = IoShuffle;
            iconTitle = "Playlist Mix";
            positionClasses = "bottom-2 left-2"; // Bottom-left
            break;
        case "Channel":
            IconComponent = IoRadioOutline;
            iconTitle = "Channel";
            positionClasses = "top-2 right-2"; // Top-right
            break;
        case "Podcast": // Added Podcast
            IconComponent = IoMicOutline;
            iconTitle = "Podcast";
            positionClasses = "bottom-2 right-2"; // Bottom-right
            break;
        case "Artist": // Added Artist (Consider if needed, could clash with Playlist visually if using same corner)
            IconComponent = IoPersonOutline;
            iconTitle = "Artist";
            positionClasses = "top-2 left-2";
            // Alternatively, choose a different corner or omit if too noisy:
            // positionClasses = "top-2 right-2"; // Or bottom-right etc.
            break;
        case "Album":
        case "AlbumSingle":
            IconComponent =
                itemType === "AlbumSingle" ? IoMusicalNoteSharp : IoDiscOutline;
            iconTitle = "Album";
            positionClasses = "bottom-2 left-2";
            break;
        case "Audio": // Optional: Icon for single track (uncomment & choose icon/position if needed)
            IconComponent = IoMusicalNoteSharp;
            iconTitle = "Track";
            positionClasses = "bottom-2 right-2"; // Example position
            break;
    }

    if (!IconComponent) {
        return null; // Don't render anything if no specific icon for this type
    }

    return (
        <div
            className={cn(
                "absolute z-[5] pointer-events-none",
                "bg-black/40 backdrop-blur-sm rounded-full p-1.5", // Style
                "text-white/90 text-xs", // Icon style
                positionClasses // Position the indicator
            )}
            title={iconTitle} // Accessibility
        >
            {/* Using '1em' makes size relative to parent's 'text-xs' */}
            <IconComponent size="1em" />
        </div>
    );
};

export function ArticleFigure(props: ArticleFigureProps) {
    const { image, entryAction, itemType } = props;

    const imgSrc = image
        ? typeof image === "string"
            ? image
            : asset(image)
        : undefined;

    const img = imgSrc ? (
        <img
            src={imgSrc}
            className="rounded absolute w-full h-full object-cover"
            alt={props.title}
            loading="lazy"
        />
    ) : undefined;

    return (
        <figure
            className={cn(
                "article-item-figure group/article w-full h-full m-0 p-0 rounded bg-opacity-10 relative overflow-hidden",
                img && [
                    "dark:bg-dark-primary-background/75 bg-light-primary-background/75",
                ],
                !img && ["flex items-center justify-center"],
                props.className
            )}
            style={{ background: !img ? $gradient(props.id) : undefined }}
        >
            {/* --- Render Item Type Indicator --- */}
            {itemType && <ItemTypeIndicator itemType={itemType} />}

            {/* Optional: Add title centered on gradient if no image */}
            {!img && props.title && (
                <span className="text-white font-bold text-center p-2 text-lg truncate block">
                    {props.title}
                </span>
            )}

            {props.href ? (
                // Make link cover the area, but ensure it's behind interactable elements
                <Link
                    href={props.href}
                    className="absolute inset-0 rounded z-0"
                    aria-hidden="true"
                    tabIndex={-1}
                >
                    {img}
                </Link>
            ) : (
                // Render image directly if no link
                img
            )}

            {entryAction && (
                <div
                    className={cn(
                        "absolute inset-0 bg-black/30 z-10 transition-opacity", // Use inset-0
                        // More pronounced hover visibility for the controls
                        "opacity-0 group-hover/article:opacity-100 focus-within:opacity-100", // Show on hover or when child has focus
                        "px-[5%] w-full h-full rounded flex items-center justify-center text-light",
                        (props.playing || props.playLoading) && ["opacity-100"] // Always show if playing/loading
                    )}
                >
                    {props.href && (
                        <Link
                            href={props.href}
                            className="absolute w-full h-full isolate cursor-default"
                        />
                    )}

                    {/* --- Play Button --- */}
                    {entryAction.onPlayClick && (
                        <Button
                            type="button"
                            variant="light" // Or adjust variant based on your design system
                            className="z-10 btn-play disabled:opacity-100 transform scale-100 transition-transform group-hover/article:scale-110" // Add subtle grow on hover
                            shape="circle"
                            onClick={(e) => {
                                // No preventDefault needed here if Link is z-0 and this is z-10
                                e.stopPropagation();
                                if (!props.playLoading) {
                                    entryAction?.onPlayClick &&
                                        entryAction.onPlayClick();
                                }
                            }}
                            spinnerLoaderSize={18}
                            replaceValueOnLoading={true}
                            loading={props.playLoading}
                            aria-label={props.playing ? "Pause" : "Play"} // Accessibility
                        >
                            {!props.playLoading && (
                                <>
                                    {props.playing ? (
                                        <IoPauseSharp
                                            className="text-dark"
                                            size={18}
                                        />
                                    ) : (
                                        <IoPlaySharp
                                            className="text-dark"
                                            size={18}
                                        />
                                    )}
                                </>
                            )}
                        </Button>
                    )}

                    {/* --- Like Button --- */}
                    {entryAction.onLikeClick && (
                        <button
                            type="button"
                            className={cn(
                                "z-10", // Ensure it's clickable
                                "absolute right-3 bottom-3 p-1", // Add padding for easier click target
                                "text-white/70 hover:text-white hover:scale-110 transition-all duration-200", // Smoother transition
                                props.liked && ["!text-primary"] // Use primary color variable if possible
                            )}
                            onClick={(e) => {
                                e.stopPropagation();
                                entryAction?.onLikeClick &&
                                    entryAction.onLikeClick();
                            }}
                            aria-label={props.liked ? "Unlike" : "Like"} // Accessibility
                        >
                            {props.liked ? (
                                <IoHeart size={16} />
                            ) : (
                                <IoHeartOutline size={16} />
                            )}
                        </button>
                    )}
                </div>
            )}
        </figure>
    );
}
