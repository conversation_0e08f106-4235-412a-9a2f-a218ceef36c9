import { cn } from "@/utils";
import { IoCheckmark, IoClose, IoInformationSharp, IoWarning } from "../icons";

export type IToast = {
    title?: string;
    type?: "success" | "default" | "error" | "warning";
    onClose?: () => void;
};

export function Toast({ title, type = "default", onClose }: IToast) {
    return (
        <div
            className={cn(
                "flex items-center w-full max-w-xs p-4 mb-4  rounded-lg shadow",
                "text-gray-500 bg-white dark:text-gray-400 dark:bg-dark-primary-700"
            )}
            role="alert"
        >
            <div
                className={cn(
                    "inline-flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg",
                    type === "success" && [
                        "text-green-500 bg-green-100  dark:bg-green-800 dark:text-green-200",
                    ],
                    type === "default" && [
                        "text-blue-500 bg-blue-100 dark:bg-blue-800 dark:text-blue-200",
                    ],
                    type === "error" && [
                        "text-red-500 bg-red-100 dark:bg-red-800 dark:text-red-200",
                    ],
                    type === "warning" && [
                        "text-orange-500 bg-orange-100 dark:bg-orange-700 dark:text-orange-200",
                    ]
                )}
            >
                {type === "default" && (
                    <IoInformationSharp className="text-blue-800 dark:text-white w-5 h-5" />
                )}
                {type === "success" && (
                    <IoCheckmark className="text-green-800 dark:text-white w-5 h-5" />
                )}
                {type === "error" && (
                    <IoClose className="text-red-800 dark:text-white w-5 h-5" />
                )}
                {type === "warning" && (
                    <IoWarning className="text-orange-700 dark:text-white w-5 h-5" />
                )}
            </div>
            <div className="ml-3 mr-4 text-sm font-normal">{title}</div>
            <button
                type="button"
                className={cn(
                    "ml-auto -my-1.5 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100",
                    "inline-flex h-8 w-8",
                    "text-gray-400 hover:text-gray-900",
                    "dark:hover:bg-dark-primary-700",
                    "dark:text-gray-500 dark:hover:text-white"
                )}
                data-dismiss-target="#toast-success"
                aria-label="Close"
                onClick={onClose}
            >
                <span className="sr-only">Close</span>
                <svg
                    aria-hidden="true"
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                    ></path>
                </svg>
            </button>
        </div>
    );
}
