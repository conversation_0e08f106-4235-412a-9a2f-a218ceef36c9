import { cn, mergeRefs } from "@/utils";
import {
    Dispatch,
    forwardRef,
    ReactNode,
    SetStateAction,
    useEffect,
    useRef,
    useState,
} from "react";

type Props = {
    readonly errors?: Record<string, string>;
    readonly setErrors?: Dispatch<SetStateAction<Record<string, string>>>;
    wrapperClassName?: string;
    trailingNode?: ReactNode;
    leadingNode?: ReactNode;
    autoCustomFocus?: boolean;
    label?: string;
} & React.ComponentPropsWithRef<"input">;

const border =
    "border-gray-300 focus:border-primary focus:ring-primary dark:border-gray-600 dark:focus:border-primary";
const inputClasse = cn(
    "block w-full rounded-lg shadow-sm outline-none focus:ring-1 focus:ring-inset disabled:opacity-70 dark:bg-dark-primary-700 dark:text-white",
    "transition duration-75",
    "min-h-[50px] w-full",
    "border-none"
);

export const InputField = forwardRef<HTMLInputElement, Props>(
    (
        {
            className,
            type = "text",
            errors,
            name,
            wrapperClassName,
            setErrors,
            trailingNode,
            leadingNode,
            autoCustomFocus,
            label,
            ...res
        },
        ref
    ) => {
        const [error, setError] = useState<string | undefined>(undefined);
        const inputRef = useRef<HTMLInputElement>(null);

        useEffect(() => {
            if (errors && name && errors[name]) {
                setError(errors[name]);
            } else {
                setError(undefined);
            }
        }, [errors, name]);

        const onKeyUp = () => {
            if (setErrors && name) {
                setErrors((ls) => ({ ...ls, [name]: "" }));
            } else {
                setError(undefined);
            }
        };

        /**
         *  Focusing on the input field when the modal is open.
         */
        useEffect(() => {
            if (autoCustomFocus) {
                inputRef.current?.focus();
            }
        }, [inputRef, autoCustomFocus]);

        const iClassName = cn(inputClasse, border, className);

        const inputElement =
            type !== "textarea" ? (
                <input
                    type={type}
                    name={name}
                    ref={mergeRefs([ref, inputRef])}
                    className={iClassName}
                    onKeyUp={onKeyUp}
                    {...res}
                />
            ) : (
                <textarea
                    name={name}
                    ref={mergeRefs([ref, inputRef]) as any}
                    className={iClassName}
                    onKeyUp={onKeyUp as any}
                    rows={3}
                    value={res.value}
                    defaultValue={res.defaultValue}
                    onChange={res.onChange as any}
                    onKeyDown={res.onKeyDown as any}
                />
            );

        return (
            <div
                className={cn(
                    "w-full mb-3 relative",
                    label && ["flex flex-col space-y-2 w-full"],
                    wrapperClassName
                )}
            >
                {label && (
                    <label htmlFor={name} className="capitalize">
                        {label}
                    </label>
                )}
                <div className="flex justify-between">
                    {leadingNode && (
                        <div className="flex items-center">{leadingNode}</div>
                    )}
                    <div className="flex-1">{inputElement}</div>
                    {trailingNode && (
                        <div className="flex items-center">{trailingNode}</div>
                    )}
                </div>

                {error && (
                    <div className="self-start justify-self-start text-sm text-red-500">
                        {error}
                    </div>
                )}
            </div>
        );
    }
);

InputField.displayName = "InputField";

export function SelectInput(
    props: React.ComponentPropsWithRef<"select"> & {
        readonly errors?: Record<string, string>;
    }
) {
    const { className, ...res } = props;
    const iClassName = cn(inputClasse, border, className);

    const error =
        props.errors && props.name && props.errors[props.name]
            ? props.errors[props.name]
            : undefined;

    return (
        <div className="flex flex-col space-y-1 w-full">
            <select className={iClassName} {...res} />
            {error && (
                <div className="self-start justify-self-start text-sm text-red-500">
                    {error}
                </div>
            )}
        </div>
    );
}
