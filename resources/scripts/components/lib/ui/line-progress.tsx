type LineProgressType = {
    backgroundColor: string,
    slideColor: string,
    slideLength: string,

}

const LineProgress = ({ backgroundColor, slideColor, slideLength }: LineProgressType) => {
  return (
    <div className={`w-full h-[2px] ${backgroundColor} flex  items-center`}>
        <div className={`${slideLength} h-[2px] ${slideColor}`}></div>
        <div className={`w-[6px] h-[6px] ${slideColor} rounded-full`}></div>
    </div>
  )
}

export default LineProgress