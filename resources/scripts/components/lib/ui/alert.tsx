import { cn } from "@/utils";
import { IoInformationCircle } from "../icons";
import React from "react";

export type Alert = {
    title?: React.ReactNode;
    type?: "success" | "default" | "error" | "warning";
    message?: React.ReactNode;
    onClose?: () => void;
};

export function Alert(props: Alert) {
    const { type = "default" } = props;

    return (
        <div
            className={cn(
                "flex p-4 mb-4 text-sm rounded-lg bg-light-primary-background dark:bg-dark-primary-700",
                type === "error" && ["text-red-800 dark:text-red-400"],
                type === "default" && ["text-blue-800 dark:text-blue-400"],
                type === "success" && ["text-green-800 dark:text-green-400"],
                type === "warning" && ["text-yellow-800 dark:text-yellow-300"]
            )}
            role="alert"
        >
            <IoInformationCircle className="w-5 h-5 mr-3" />
            <div>
                <span className="font-semibold">{props?.title}</span>{" "}
                {props?.message}
            </div>
        </div>
    );
}
