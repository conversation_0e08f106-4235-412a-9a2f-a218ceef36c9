import React, { useState } from "react";
import { Tab } from "@headlessui/react";
import type { IconType } from "react-icons";
import { cn } from "@/utils";

export type ITab = {
    title: string;
    Icon?: IconType;
    disabled?: boolean;
    active?: boolean;
};

type Props = {
    tabs: ITab[];
    defaultIndex?: number;
    children?: (Panel: typeof Tab.Panel) => React.ReactNode;
};

export function Tabs({ tabs, defaultIndex = 0, children }: Props) {
    const [selectedIndex, setSelectedIndex] = useState(defaultIndex);

    return (
        <Tab.Group selectedIndex={selectedIndex} onChange={setSelectedIndex}>
            <Tab.List className="border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400">
                    {tabs
                        .filter((tab) => tab.active !== false)
                        .map((tab, i) => {
                            const active = i === selectedIndex;
                            return (
                                <Tab
                                    disabled={tab.disabled}
                                    className={cn(
                                        "mr-2 outline-none",
                                        "inline-flex p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group",
                                        tab.disabled && [
                                            "inline-block text-gray-400 cursor-not-allowed dark:text-gray-500",
                                        ],
                                        active &&
                                            !tab.disabled && [
                                                "inline-flex text-primary-600 border-b-2 border-primary-600 active dark:text-primary dark:border-primary group",
                                            ]
                                    )}
                                    key={i}
                                >
                                    {tab.Icon && (
                                        <tab.Icon
                                            className={cn(
                                                "w-5 h-5 mr-2 text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300",
                                                active && [
                                                    "w-5 h-5 mr-2 text-primary-600 dark:text-primary",
                                                ]
                                            )}
                                        />
                                    )}
                                    {tab.title}
                                </Tab>
                            );
                        })}
                </div>
            </Tab.List>

            <Tab.Panels>{children && children(Tab.Panel)}</Tab.Panels>
        </Tab.Group>
    );
}
