type cardImageTypes = {
    picture: string | string;
    alt: string;
    height: string;
    width?: string;
    tlbr?: boolean;
    bltr?: boolean;
    rounded?: boolean;
    roundedSize?: string;
};

export const CardImage = ({
    picture,
    alt,
    height,
    width,
    tlbr = false,
    bltr = false,
    rounded = false,
    roundedSize = "xl",
}: cardImageTypes) => {
    let roundedValue = tlbr
        ? `rounded-tl-${roundedSize}  rounded-br-${roundedSize}`
        : bltr
        ? `rounded-bl-${roundedSize} rounded-tr-${roundedSize}`
        : rounded
        ? `rounded-${roundedSize}`
        : "";
    const styles = {
        imgContainer: `relative ${height} ${
            width ? width : "w-auto"
        } overflow-hidden bg-slate-300 ${roundedValue}`,
    };

    return (
        <div className={styles.imgContainer}>
            <img className="" src={picture} alt={alt} />
        </div>
    );
};
