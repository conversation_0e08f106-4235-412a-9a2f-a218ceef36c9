import { cn } from "@/utils";
import { PropsWithChildren, useEffect, useState } from "react";
import { IoPencil } from "../icons";
import { Link } from "@inertiajs/react";

type Props = {
    className?: string;
    size?: number;
    src?: string;
    shape?: "circle" | "square";
    alt?: string;
    imageTitle?: string;
    layout?: "static" | "fill";
    href?: string;
    hasInput?: boolean;
    hasEditableClick?: boolean;
    onEditableClick?: () => void;
    onChange?: (file: File) => void;
} & PropsWithChildren;

export function Avatar({
    className,
    src: srcImg,
    size,
    shape = "circle",
    children,
    alt,
    imageTitle,
    hasInput,
    onChange,
    hasEditableClick,
    onEditableClick,
    href,
    layout,
}: Props) {
    const [isrc, setISrc] = useState(srcImg);

    useEffect(() => setISrc(srcImg), [srcImg]);

    const imageText = (imageTitle || "").split(" ").reduce((acc, v, i) => {
        if (i <= 2) {
            acc += v[0];
        }
        return acc;
    }, "");

    return (
        <div
            className={cn(
                "bg-light-primary-700 dark:bg-dark-primary-background relative",
                shape === "circle" && ["rounded-full"],
                shape === "square" && ["rounded-md"],
                imageTitle && !isrc && ["flex justify-center items-center"],
                layout === "fill" && ["article-item-figure w-full h-full"],
                className
            )}
            style={{ width: size, height: size }}
        >
            {imageTitle && !isrc && (
                <span
                    className={cn(
                        "uppercase font-medium text-[80%] text-dark dark:text-light",
                        layout === "fill" && ["absolute z-10"]
                    )}
                >
                    {imageText}
                </span>
            )}

            {href && <Link href={href} className="absolute inset-0 z-10" />}

            {isrc && (
                <img
                    src={isrc}
                    className={cn(
                        "w-full h-full object-cover",
                        shape === "circle" && ["rounded-full"],
                        shape === "square" && ["rounded-md"],
                        layout === "fill" && ["absolute w-full h-full"]
                    )}
                    alt={alt}
                />
            )}

            {(hasInput || hasEditableClick) && (
                <>
                    {/* */}
                    <div
                        className={cn(
                            "absolute inset-0 z-20 w-full h-full border cursor-pointer",
                            "brightness-100 backdrop-blur bg-light-primary/50 dark:bg-dark-primary/50",
                            "transition-all hover:transition-all",
                            "flex justify-center items-center",
                            "hover:opacity-100 opacity-0",
                            shape === "circle" && ["rounded-full"],
                            shape === "square" && ["rounded-md"]
                        )}
                        onClick={onEditableClick}
                    >
                        {hasInput && (
                            <input
                                type="file"
                                className="opacity-0 absolute inset-0 z-10 cursor-pointer"
                                accept="image/png, image/jpeg"
                                onChange={(e) => {
                                    const file = e.currentTarget.files
                                        ? e.currentTarget.files[0]
                                        : null;
                                    if (!file) return;

                                    const imageSrc = URL.createObjectURL(file);

                                    setISrc(imageSrc);

                                    onChange && onChange(file);

                                    window.setTimeout(
                                        () => URL.revokeObjectURL(imageSrc),
                                        2000
                                    );
                                }}
                            />
                        )}
                        <IoPencil className="w-5 h-5 dark:text-light text-dark transition-all" />
                    </div>
                </>
            )}

            {children}
        </div>
    );
}
