import { cn } from "@/utils";
import { Dialog, Transition } from "@headlessui/react";
import { PropsWithChildren, Fragment } from "react";

type Props = PropsWithChildren<{
    title?: React.ReactNode;
    description?: string;
    isOpen: boolean;
    closeModal: () => void;
    className?: string;
}>;

export function Drawer(props: Props) {
    return (
        <Transition show={props.isOpen} as={Fragment}>
            <Dialog onClose={props.closeModal}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black/30 z-50" />
                </Transition.Child>

                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="transform-none"
                    leave="ease-in duration-200"
                    leaveFrom="transform-none"
                    leaveTo="translate-x-full"
                >
                    <div
                        aria-labelledby="drawer-right-label"
                        className={cn(
                            "fixed top-0 right-0 z-50 h-screen p-4 overflow-y-auto w-full xs:w-96",
                            "bg-light-primary dark:bg-dark-primary",
                            props.className
                        )}
                    >
                        <Dialog.Panel
                            onClick={(e) => e.stopPropagation()}
                            className="h-[96%]"
                        >
                            <button
                                onClick={props.closeModal}
                                type="button"
                                data-drawer-hide="drawer-right"
                                aria-controls="drawer-right"
                                className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 absolute top-2.5 right-2.5 inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            >
                                <svg
                                    aria-hidden="true"
                                    className="w-5 h-5"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                <span className="sr-only">Close menu</span>
                            </button>

                            {props.title && (
                                <Dialog.Title className="inline-flex items-center mb-4 text-base font-semibold text-dark dark:text-light/90">
                                    {props.title}
                                </Dialog.Title>
                            )}

                            {props.description && (
                                <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 break-words overflow-hidden">
                                    {props.description}
                                </p>
                            )}

                            <div className="max-h-full scrollbar-hide w-full block overflow-y-auto text-dark dark:text-light/90 pb-10">
                                {props.children}
                            </div>
                        </Dialog.Panel>
                    </div>
                </Transition.Child>
            </Dialog>
        </Transition>
    );
}
