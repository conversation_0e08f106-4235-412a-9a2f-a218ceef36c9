import { cn, getMinAndSec } from "@/utils";
import { Link } from "@inertiajs/react";
import { ArticleFigure, ArticleFigureProps } from "./article-figure";
import { IoHeart, IoHeartOutline } from "../icons";
import { Artist } from "@/types/models";
import { useZiggy } from "@/hooks";
import React from "react";

export type ArticleItemProps = {
    subtitle?: { text: string; href?: string };
    podcast?: { channelLink?: string } | boolean;
    type: "card" | "list";
    duration?: number | null;
    likes?: number;
    featurings?: Artist[];
} & ArticleFigureProps;

export function ArticleItem(props: ArticleItemProps) {
    const {
        subtitle,
        className,
        type = "card",
        podcast,
        entryAction,
        likes,
        featurings,
        ...figure
    } = props;

    const route = useZiggy();

    const ellipsis =
        "block overflow-x-hidden text-ellipsis whitespace-nowrap max-w-full";

    const hoverText = "hover:text-primary-600 dark:hover:text-primary";

    const hasLikes = !!(entryAction?.onLikeClick || likes);

    const featuringsData = featurings || [];

    return (
        <article
            data-article-id={props.id}
            className={cn(
                "relative p-0 max-w-full flex flex-nowrap flex-col snap-start rounded",
                type === "card" && ["space-y-3"],
                type === "list" && ["flex flex-row items-center space-x-3"],
                type === "list" && ["article-item-list group/article"],
                className
            )}
        >
            <ArticleFigure
                {...figure}
                itemType={props.type === "list" ? undefined : figure.itemType}
                entryAction={
                    entryAction
                        ? {
                              onPlayClick: entryAction.onPlayClick,
                              onLikeClick:
                                  type === "card"
                                      ? entryAction.onLikeClick
                                      : undefined,
                          }
                        : undefined
                }
                className={cn(type === "list" && ["w-16 lg:w-24"])}
            />

            <div
                className={cn(
                    "flex flex-col space-y-1 h-auto w-full",
                    type === "list" && ["flex-1 overflow-x-hidden"]
                )}
            >
                <div className="w-full flex flex-row justify-between">
                    <h3
                        title={props.title}
                        className={cn(
                            "dark:text-light/90",
                            ellipsis,
                            props.href && [
                                "hover:text-primary-600 dark:hover:text-primary",
                            ]
                        )}
                    >
                        {props.href ? (
                            <Link href={props.href}>{props.title}</Link>
                        ) : (
                            props.title
                        )}
                    </h3>

                    {podcast && type === "card" && (
                        <PodcastIcon podcast={podcast} />
                    )}
                </div>

                {subtitle && (
                    <div
                        title={
                            subtitle?.text +
                            featuringsData
                                .map((artist) => `, ${artist.name}`)
                                .join("")
                        }
                        className={cn("text-slate-400 text-sm", ellipsis)}
                    >
                        {subtitle?.href ? (
                            <Link
                                title={subtitle?.text}
                                className={hoverText}
                                href={subtitle?.href}
                            >
                                {subtitle?.text}
                            </Link>
                        ) : (
                            subtitle?.text
                        )}
                        {featuringsData.length > 0 &&
                            featuringsData.map((artist) => {
                                return (
                                    <React.Fragment key={artist.id}>
                                        <span>{", "}</span>
                                        <Link
                                            title={artist.name}
                                            className={hoverText}
                                            href={route("app.artists.show", {
                                                user: artist.id,
                                            })}
                                        >
                                            {artist.name}
                                        </Link>
                                    </React.Fragment>
                                );
                            })}
                    </div>
                )}

                {props.type === "list" &&
                    hasLikes &&
                    props.duration !== undefined && (
                        <div className="opacity-70 text-sm">
                            {getMinAndSec(props.duration || 0)}
                        </div>
                    )}
            </div>

            {props.type === "list" &&
                !hasLikes &&
                props.duration !== undefined && (
                    <div className="flex flex-col gap-3 min-w-6 justify-center">
                        {podcast && (
                            <PodcastIcon podcast={podcast} className="ml-0" />
                        )}

                        <div className="opacity-70 text-sm">
                            {getMinAndSec(props.duration || 0)}
                        </div>
                    </div>
                )}

            {props.type === "list" && hasLikes && (
                <div className="flex flex-col gap-3 min-w-6 justify-center">
                    {podcast && (
                        <PodcastIcon podcast={podcast} className="ml-0" />
                    )}

                    <div className="opacity-90 flex justify-center space-x-1">
                        {/* <span>{abbreviateNumber(likes || 0)}</span> */}
                        <button
                            type="button"
                            className={cn(
                                "hover:scale-150 transition-all",
                                props.liked && [
                                    "text-primary dark:text-primary-50",
                                ]
                            )}
                            onClick={(e) => {
                                e.stopPropagation();
                                entryAction?.onLikeClick &&
                                    entryAction.onLikeClick();
                            }}
                        >
                            {props.liked ? <IoHeart /> : <IoHeartOutline />}
                        </button>
                    </div>
                </div>
            )}
        </article>
    );
}

function PodcastIcon({
    podcast,
    className,
}: {
    podcast?: true | { channelLink?: string };
    className?: string;
}) {
    if (!podcast) return null;
    return (
        <div
            title="podcast"
            className={cn(
                "inline-block relative",
                "py-1 px-2 uppercase text-center text-primary-600 dark:text-primary",
                "bg-light-primary-background dark:bg-dark-primary-700",
                "rounded-md ml-2 font-semibold text-xs cursor-default",
                typeof podcast === "object" &&
                    podcast.channelLink && ["cursor-pointer"],
                className
            )}
        >
            {typeof podcast === "object" && podcast.channelLink && (
                <Link
                    className="absolute inset-0 z-10"
                    href={podcast.channelLink}
                />
            )}
            <span className="opacity-70">{"p"}</span>
        </div>
    );
}
