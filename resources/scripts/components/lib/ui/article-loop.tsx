import { cn } from "@/utils";
import { PropsWithChildren } from "react";

type ArticleLookProps = (
    | { type: "list" }
    | { type: "card"; cols: 3 | 6 }
    | { type: "auto" }
) & {
    className?: string;
};

export function ArticleLook(props: PropsWithChildren<ArticleLookProps>) {
    if (props.type === "card") {
        return (
            <div
                className={cn(
                    "grid grid-cols-2 gap-4 w-full",
                    props.cols === 3 && ["lg:grid-cols-3"],
                    props.cols === 6 && ["lg:grid-cols-4 xl:grid-cols-6"],
                    props.className
                )}
            >
                {props.children}
            </div>
        );
    }

    if (props.type === "list") {
        return (
            <div
                className={cn(
                    "flex flex-col space-y-4 [counter-reset:li]",
                    props.className
                )}
            >
                {props.children}
            </div>
        );
    }

    return (
        <div
            className={cn(
                "lg:flex lg:flex-col lg:space-y-4 lg:[counter-reset:li]",
                "grid grid-cols-2 gap-4 lg:gap-0 w-full",
                props.className
            )}
        >
            {props.children}
        </div>
    );
}
