import { useHasMounted } from "@/hooks";
import { createPortal } from "react-dom";
import { cn } from "@/utils";

export function SpinnerLoader({
    size = 31,
    className,
    variant = "primary",
    forcePrimary,
}: {
    size?: number;
    className?: string;
    variant?: "primary" | "light" | "dark";
    forcePrimary?: boolean;
}) {
    return (
        <span
            className={cn(
                "animate-spin",
                [
                    variant === "primary" && [
                        "border-t-primary border-r-primary border-b-transparent border-l-primary",
                        !forcePrimary && [
                            "dark:border-t-white dark:border-r-white dark:border-b-transparent dark:border-l-white",
                        ],
                    ],
                    variant === "light" && [
                        "border-t-white border-r-white border-b-transparent border-l-white",
                    ],
                    variant === "dark" && [
                        "border-t-dark-primary border-r-dark-primary border-b-transparent border-l-dark-primary",
                    ],
                ],
                className
            )}
            style={{
                width: `${size}px`,
                height: `${size}px`,
                borderRadius: "100%",
                borderWidth: "2px",
                borderStyle: "solid",
                borderImage: "initial",
                display: "inline-block",
            }}
        />
    );
}

export function BackdropLoader({
    title,
    show,
    fadeIn = true,
    canCreatePortal = true,
}: {
    title?: string;
    show?: boolean;
    fadeIn?: boolean;
    canCreatePortal?: boolean;
}) {
    const { mounted } = useHasMounted();

    const content = (
        <div
            className={cn(
                "fixed inset-0 z-[9999]",
                "backdrop-brightness-90 backdrop-blur-sm",
                "flex justify-center items-center",
                [
                    show
                        ? [
                              fadeIn ? ["fade-in"] : ["opacity-100"],
                              "h-full w-full",
                          ]
                        : ["fade-out h-0 w-0"],
                ]
            )}
        >
            <div className="flex space-x-2">
                <div className="w-[31px] h-[31px] mr-4">
                    <SpinnerLoader className="mr-1" />
                </div>
                <div className="text-xs whitespace-nowrap text-ellipsis">
                    {title}
                </div>
            </div>
        </div>
    );

    if (!canCreatePortal) {
        return content;
    }

    if (!mounted) {
        return <></>;
    }

    return createPortal(content, document.body);
}
