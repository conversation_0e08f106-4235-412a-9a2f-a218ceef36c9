import { useZiggy } from "@/hooks";
import { Genre } from "@/types/models";
import { asset, cn, moduleResolve } from "@/utils";
import { Link } from "@inertiajs/react";
import gradient from "@privjs/gradients";

const $gradient = moduleResolve(gradient);

export function CategoryItem({ genre }: { genre: Genre }) {
    const route = useZiggy();
    const image = genre.image;

    return (
        <div className="relative p-0 max-w-full flex snap-start rounded overflow-hidden">
            <figure
                className={cn(
                    "article-item-figure w-full h-full m-0 p-0 rounded opacity-70"
                )}
                style={{ background: $gradient(genre.id) }}
            />

            {image && (
                <div className="absolute -bottom-3 -right-3 w-2/6 h-2/6 isolate z-[5]">
                    <img
                        className="h-full w-full -rotate-45 object-cover rounded-lg"
                        src={asset(image)}
                    />
                </div>
            )}

            <div className="absolute inset-0 z-[2] bg-black/20 dark:bg-black/10" />

            <Link
                href={route("app.search.genres.show", { genre: genre.id })}
                className="absolute inset-0 z-[11] bg-transparent"
            />

            <div className="h-5/6 z-10 w-full px-2 absolute pt-2">
                <h3 className="text-white font-semibold">{genre.name}</h3>
            </div>
        </div>
    );
}
