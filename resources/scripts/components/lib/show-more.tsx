import { useCallbackRef, useRefSync } from "@/hooks";
import { Paginate } from "@/types";
import { router } from "@inertiajs/react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { SpinnerLoader } from "./ui";

export function ShowMore<T extends Paginate<any>>({
    pagination,
    onSuccess,
    autoLoad = true,
}: {
    pagination: T;
    onSuccess?: () => void;
    autoLoad?: boolean;
}) {
    const container = useRef<React.ElementRef<"div">>(null);

    const $onSuccess = useCallbackRef(onSuccess);
    const $pagination = useRefSync(pagination);
    const [loading, setLoading] = useState(false);

    const [nextUrl, setNextUrl] = useState<string | null>(
        pagination.links.next || null
    );

    const loadMore = useCallback(() => {
        if (!nextUrl) return;

        setLoading(true);
        router.get(nextUrl, undefined, {
            preserveState: true,
            preserveScroll: true,
            onSuccess() {
                window.history.replaceState({}, "", pagination.meta.path);
                setTimeout(() => {
                    setNextUrl($pagination.current.links.next);
                    $onSuccess.current && $onSuccess.current();
                }, 0);
            },
            onFinish() {
                setLoading(false);
            },
            replace: true,
        });
    }, [nextUrl, $onSuccess.current, pagination]);

    const $loadMore = useCallbackRef(loadMore);

    useEffect(() => {
        if (!container.current || !nextUrl || !autoLoad) {
            return;
        }

        const options = {
            threshold: [0],
        };

        const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting === true) {
                $loadMore.current && $loadMore.current();
            }
        }, options);

        observer.observe(container.current);

        return () => {
            container.current && observer.unobserve(container.current);
            observer.disconnect();
        };
    }, [container, nextUrl, autoLoad]);

    const element = (
        <div ref={container} className="flex items-center my-6 w-full">
            <hr className="w-full dark:opacity-10" />
            <div className="px-5">
                {loading && <SpinnerLoader />}
                {!loading && (
                    <button
                        type="button"
                        onClick={loadMore}
                        className="whitespace-nowrap opacity-70"
                    >
                        Load more
                    </button>
                )}
            </div>
            <hr className="w-full dark:opacity-10" />
        </div>
    );

    return nextUrl ? element : <></>;
}
