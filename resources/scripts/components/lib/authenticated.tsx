import { useAuthUser } from "@/hooks";
import { User } from "@/types/models";
import { PropsWithChildren } from "react";

type Props = {
    children?: React.ReactNode | ((user: User) => React.ReactNode);
};

/**
 * Render children when user authenticated
 * @returns
 */
export function Authenticated(props: Props) {
    const user = useAuthUser();
    const { children } = props;

    return user ? (
        <>{typeof children === "function" ? children(user) : children}</>
    ) : null;
}

/**
 * Render children when user not authenticated
 * @param props
 * @returns
 */
export function UnAuthenticated(props: PropsWithChildren) {
    const user = useAuthUser();

    return user ? null : props.children;
}
