import { useModal } from "@/hooks";
import { BiSolidPlaylist } from "./icons";
import { ArticleLook, Drawer } from "./ui";
import { ItemArticle } from "./item-article";
import { usePlayer } from "../player";
import { useMemo } from "react";

export function QueuePlaylist() {
    const player = usePlayer();
    const modal = useModal();

    const playlist = useMemo(() => player?.playlist || [], [player?.playlist]);
    const isInitialized = !!player;

    return (
        <>
            <button
                title="Playlist"
                disabled={!isInitialized}
                onClick={isInitialized ? modal.openModal : undefined}
            >
                <BiSolidPlaylist className="h-5 w-5" />
            </button>

            {isInitialized && (
                <Drawer
                    {...modal}
                    title={`Playlist${
                        playlist.length > 0 ? " - " + playlist.length : ""
                    }`}
                >
                    <ArticleLook type="list" className="h-full">
                        {playlist.map((article) => {
                            return (
                                <ItemArticle
                                    key={article.id}
                                    onPlayActionType="skip-to"
                                    viewType="list"
                                    type="article"
                                    data={article}
                                />
                            );
                        })}
                    </ArticleLook>
                </Drawer>
            )}
        </>
    );
}
