import { ArticleItem, ArticleItemProps, ItemViewType } from "./ui";
import { useAuthUser, useItemLike, useZiggy } from "@/hooks";
import { ItemType } from "@/types";
import { useState } from "react";
import { usePlayer } from "../player";
import { Image, Playlist } from "@/types/models";

export type ItemArticleProps = {
    viewType: NonNullable<ArticleItemProps["type"]>;
    showLikes?: boolean;
    onPlayActionType?: "set-play" | "skip-to";
    className?: string;
} & ItemType;

export function ItemArticle(props: ItemArticleProps) {
    const [loading, setLoading] = useState(false);
    const player = usePlayer();
    const route = useZiggy();
    const auth = useAuthUser();

    const { likeCall } = useItemLike();

    const { onPlayActionType = "set-play" } = props;
    const currentTrack = player?.currentTrack;

    let link = "";
    switch (props.type) {
        case "article":
        case "channel":
            link = route("app.channels.show", {
                channel:
                    props.type === "article"
                        ? props.data.channel.id
                        : props.data.id,
                ...(props.type === "article" ? { article: props.data.id } : {}),
            });
            break;

        case "playlist":
            link = route("app.playlists.show", { playlist: props.data.id });
            break;
    }

    // Check if the item is a podcast
    let isPodcast = false;
    switch (props.type) {
        case "article":
            isPodcast = props.data.channel.type === "CHANNEL";
            break;
        case "channel":
            isPodcast = props.data.type === "CHANNEL";
            break;
    }

    // Check if the item has an image
    let image: Image | undefined;
    switch (props.type) {
        case "article":
            image = props.data.image || props.data.channel.image;
            break;
        case "channel":
            image = props.data.image;
            break;
        case "playlist":
            image = props.data.image || props.data.fallback_image;
            break;
    }

    let canPlay = true;
    if (props.type === "article" && !props.data.audio) {
        canPlay = false;
    }

    let playings: boolean | undefined = false;
    if (props.type === "article") {
        if (props.data.id === currentTrack?.id) {
            playings = Boolean(player?.isPlaying);
        }
    } else if (props.type === "playlist") {
        if (props.data.id === player?.playlistId) {
            playings = Boolean(player?.isPlaying);
        }
    }

    if (props.type === "channel") {
        if (props.data.id === currentTrack?.channel.id) {
            playings = Boolean(player?.isPlaying);
        }
    }

    function showPageParam(page: string) {
        const currentRoute = route().current();
        if (currentRoute?.endsWith(`${page}s.show`)) {
            const params = route().params;
            return params[page];
        }
        return null;
    }

    function onLikeClick() {
        const data = { type: props.type, data: props.data } as ItemType;
        // Refresh player status if the liked item is current playing
        likeCall(
            data,
            props.type === "article" && props.data.id === currentTrack?.id
        );
    }

    function onPlayClick() {
        let data = { type: props.type, data: props.data } as ItemType;

        // Here just handle the case where user click on a article within a channel
        // To include the siblings articles on play
        if (props.type === "article") {
            const channelId = showPageParam("channel");
            const playlistId = showPageParam("playlist");

            if (channelId) {
                data = {
                    type: "channel",
                    data: props.data.channel,
                    activeArticle: props.data,
                };
            } else if (playlistId) {
                data = {
                    type: "playlist",
                    data: { id: playlistId } as Playlist,
                    activeArticle: props.data,
                };
            }
        }

        if (playings) {
            player?.actions.pause();
        } else if (onPlayActionType === "set-play") {
            const playStatus = player?.guessPlaylist(data);

            /**
             * If play request a fetch, the show the loading
             */
            if (playStatus instanceof Promise) {
                setLoading(true);
                playStatus.finally(() => setLoading(false));
            }
        } else if (onPlayActionType === "skip-to" && props.type === "article") {
            player?.jumpTo(props.data);
        }
    }

    let likesCount: number | undefined = undefined;
    if (props.type !== "playlist") {
        likesCount =
            props.viewType === "list" && props.showLikes
                ? props.data.likes_count
                : undefined;
    }

    const handlePlayClick = canPlay ? onPlayClick : undefined;

    const featurings =
        props.type === "article" ? props.data.featurings : undefined;

    const duration =
        props.type === "article" ? props.data.audio?.duration : undefined;

    const data = props.data;

    let handleLikeClick: VoidFunction | undefined = onLikeClick;
    if (
        // For now disable the like button for the channel
        props.type === "channel" ||
        (props.type === "article" && !props.showLikes) ||
        (props.type === "playlist" && auth && auth.id === data.user?.id)
    ) {
        handleLikeClick = undefined;
    }

    let subtitle: any = undefined;
    if (data.user) {
        if (
            props.type !== "playlist" ||
            (props.type === "playlist" && auth && auth.id !== data.user?.id)
        ) {
            subtitle = {
                text: data.user?.name,
                href: route("app.artists.show", {
                    user: data.user.id,
                }),
            };
        }
    }

    let itemType: ItemViewType | undefined = undefined;

    if (props.type === "article") {
        itemType = props.data.channel.type === "CHANNEL" ? "Podcast" : "Audio";
    } else if (props.type === "playlist") {
        itemType = props.data.is_system_generated ? "PlaylistMix" : "Playlist";
    } else if (props.type === "channel") {
        switch (props.data.type) {
            case "CHANNEL":
                itemType = "Channel";
                break;
            case "ALBUM": {
                itemType = "Album";
                if (props.data.articles_count === 1) {
                    itemType = "AlbumSingle";
                }
                break;
            }
            case "SINGLE":
                itemType = "Audio";
                break;
            default:
                itemType = "Channel";
                break;
        }
    }

    return (
        <ArticleItem
            type={props.viewType}
            className={props.className}
            href={link}
            image={image}
            playing={playings}
            featurings={featurings}
            entryAction={{
                onLikeClick: handleLikeClick,
                onPlayClick: handlePlayClick,
            }}
            playLoading={loading}
            likes={likesCount}
            liked={data.liked}
            id={data.id}
            title={data.name}
            duration={duration}
            itemType={itemType}
            podcast={isPodcast ? { channelLink: link } : undefined}
            subtitle={subtitle}
        />
    );
}
