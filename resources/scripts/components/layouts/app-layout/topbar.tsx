import { <PERSON><PERSON>, But<PERSON> } from "@components/lib/ui";
import { asset, cn } from "@/utils";
import { useIPage, useModal, useTheme, useZiggy } from "@/hooks";
import {
    <PERSON><PERSON>ell,
    IoStatsChart,
    IoMoon,
    IoSunny,
    IoPersonCircle,
    IoLogOutOutline,
    IoCloseCircleOutline,
} from "@/components/lib/icons";
import { Menu, Transition } from "@headlessui/react";
import { PropsWithChildren } from "react";
import { Link } from "@inertiajs/react";
import {
    Authenticated,
    NotificationsDrawer,
    UnAuthenticated,
} from "@/components/lib";

export function TopBar({
    className,
    minify,
}: {
    className?: string;
    minify?: boolean;
}) {
    const { props } = useIPage();
    const route = useZiggy();

    return (
        <div
            className={cn(
                "sticky -top-1 flex justify-between items-center py-5 z-30",
                "backdrop-blur-sm bg-light-primary/70 dark:bg-dark-primary/70",
                "app-topbar",
                className
            )}
        >
            <Link
                href={route("app.discover")}
                className="font-semibold flex items-center space-x-2 md:space-x-0"
            >
                {/* <BkeLogo className="fill-primary block md:hidden w-5 -mt-2" /> */}
                <span className={cn("inline", !minify && ["hidden sm:inline"])}>
                    {props.appName}
                </span>
            </Link>

            {/* left */}
            <TopBarItem />

            <UnAuthenticated>
                <div className="flex items-center space-x-7">
                    <ThemeSwitcher />

                    <a href={route("app.discover")}>
                        <Button variant="primary">Sign in</Button>
                    </a>
                </div>
            </UnAuthenticated>
        </div>
    );
}

function ThemeSwitcher() {
    const { toggle } = useTheme();
    return (
        <Button
            onClick={toggle}
            className="cursor-pointer bg-light-primary-background dark:bg-dark-primary-700 flex justify-center items-center"
        >
            <IoSunny className="h-5 w-5 text-primary-500 dark:text-primary dark:hidden" />
            <IoMoon className="h-5 w-5 text-primary-500 dark:text-primary hidden dark:block" />
        </Button>
    );
}

function TopBarItem() {
    return (
        <Authenticated>
            {(user) => (
                <div className="flex items-center space-x-7">
                    {user.role !== "GUEST" && (
                        <a
                            href="/dash"
                            className="bg-light-primary-background dark:bg-dark-primary-700 flex justify-center items-center p-1 rounded-md"
                        >
                            <IoStatsChart className="text-dark-primary dark:text-light h-4 w-4" />
                        </a>
                    )}

                    <ThemeSwitcher />

                    <Notifications />

                    <div className="flex items-center">
                        <UserMenu>
                            <Avatar
                                imageTitle={user.name}
                                size={40}
                                src={asset(user.image)}
                                className="bg-light-primary-700 dark:bg-dark-primary-background cursor-pointer"
                            />
                        </UserMenu>
                    </div>
                </div>
            )}
        </Authenticated>
    );
}

function Notifications() {
    const { props } = useIPage();
    const modal = useModal();

    const notifications = props.notifications || [];

    return (
        <>
            <button
                onClick={modal.openModal}
                className="relative px-[0.4rem] py-[0.4rem] md:px-2 md:py-2 rounded-full bg-light-primary-background dark:bg-dark-primary-700"
            >
                <HiBell className="text-dark-primary dark:text-light h-4 w-4" />
                {notifications.length > 0 && (
                    <>
                        <div className="absolute top-[7px] right-[9px] w-2 h-2 bg-primary rounded-full" />
                        <div className="animate-ping absolute top-[7px] right-[9px] w-2 h-2 bg-primary rounded-full" />
                    </>
                )}
            </button>

            <NotificationsDrawer modal={modal} />
        </>
    );
}

function UserMenu(props: PropsWithChildren) {
    const {
        props: { isImpersonating },
    } = useIPage();
    const route = useZiggy();

    const menus = [
        {
            href: route("app.settings.account"),
            Icon: IoPersonCircle,
            title: "Account",
        },
        ...(isImpersonating
            ? [
                  {
                      href: route("impersonate.leave"),
                      title: "Leave impersonation",
                      Icon: IoCloseCircleOutline,
                      hover: true,
                      logout: false,
                  },
              ]
            : []),
        {
            href: route("logout"),
            Icon: IoLogOutOutline,
            title: "Sign out",
            hover: true,
            logout: true,
        },
    ];
    return (
        <div className="relative">
            <Menu>
                <Menu.Button>{props.children}</Menu.Button>
                <Transition
                    enter="transition duration-100 ease-out"
                    enterFrom="transform scale-95 opacity-0"
                    enterTo="transform scale-100 opacity-100"
                    leave="transition duration-75 ease-out"
                    leaveFrom="transform scale-100 opacity-100"
                    leaveTo="transform scale-95 opacity-0"
                    className="absolute right-0 z-10 bg-light-primary-700 dark:bg-dark-primary-background rounded-md mt-2"
                >
                    <Menu.Items className="w-40 p-2 overflow-hidden">
                        {menus.map((menu, i) => {
                            return (
                                <Menu.Item key={i}>
                                    <Link
                                        method={
                                            menu.logout ? "post" : undefined
                                        }
                                        as={menu.logout ? "button" : undefined}
                                        href={menu.href}
                                        className={cn(
                                            "flex font-medium text-sm space-x-2 items-center py-2 px-2 w-full",
                                            i !== menus.length - 1 && [
                                                "border-b border-b-gray-50/30 mb-2",
                                            ],
                                            menu.hover && [
                                                "hover:bg-primary/90 hover:text-white hover:rounded-md",
                                            ]
                                        )}
                                    >
                                        <menu.Icon className="w-5 h-5" />
                                        <span
                                            title={menu.title}
                                            className="whitespace-nowrap text-ellipsis overflow-hidden"
                                        >
                                            {menu.title}
                                        </span>
                                    </Link>
                                </Menu.Item>
                            );
                        })}
                    </Menu.Items>
                </Transition>
            </Menu>
        </div>
    );
}
