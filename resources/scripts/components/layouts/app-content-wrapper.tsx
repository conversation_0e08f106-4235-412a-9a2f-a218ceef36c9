import { PropsWithChildren } from "react";

export function AppContentWrapper(
    props: PropsWithChildren<{ title?: string }>
) {
    return (
        <div className="flex flex-col space-y-4 w-full">
            {props.title && (
                <h1 className="text-xl dark:text-2xl">{props.title}</h1>
            )}

            <div className="w-full">{props.children}</div>
        </div>
    );
}
