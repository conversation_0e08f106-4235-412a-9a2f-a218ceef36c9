import { useIPage } from "@/hooks";
import { Head } from "@inertiajs/react";
import { PropsWithChildren } from "react";

export function AppHead({
    title,
    children,
}: PropsWithChildren<{ title?: string }>) {
    const { props } = useIPage();

    return (
        <Head>
            <title>
                {title ? `${title} - ${props.appName}` : props.appName}
            </title>
            {children}
        </Head>
    );
}
