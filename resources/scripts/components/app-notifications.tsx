import { useEffect, useRef } from "react";
import { useToasts } from "./lib";
import { router } from "@inertiajs/react";

export function AppNotifications() {
    return <EmailVerified />;
}

function EmailVerified() {
    const displayed = useRef(false);
    const toast = useToasts();

    useEffect(() => {
        const emailVerified = location.search.includes("verified=1");
        if (emailVerified && !displayed.current) {
            toast.pushToast({
                title: "Email has been verified successfully !",
                type: "success",
            });

            router.replace({
                url: location.origin + location.pathname,
            });

            displayed.current = true;
        }
    }, []);

    return <></>;
}
