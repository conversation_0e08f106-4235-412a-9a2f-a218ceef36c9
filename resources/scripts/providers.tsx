import React, { PropsWithChildren, Suspense } from "react";
import { Provider } from "jotai";
import { ClientOnly, ToastContextProvider } from "./components/lib";
import { AppNotifications } from "./components/app-notifications";
import { FullscreenLoader } from "./components/lib/fullscreen-loader";
import { PlayerProvider } from "./components/player";

export function Providers(props: PropsWithChildren) {
    return (
        <React.StrictMode>
            <Provider>
                <Suspense fallback={<FullscreenLoader />}>
                    <ToastContextProvider>
                        <ClientOnly>
                            <AppNotifications />
                        </ClientOnly>

                        <PlayerProvider>{props.children}</PlayerProvider>
                    </ToastContextProvider>
                </Suspense>
            </Provider>
        </React.StrictMode>
    );
}
