// @ts-nocheck
import { Howler } from "howler";
import {
    activeArticleAtom,
    activePlayerAtom,
    appPlayerVolumeAtom,
    articlesPlaysAtom,
    playerSeekAtom,
} from "@/store";
import throttle from "lodash/throttle";
import { useAtomValue, useSet<PERSON>tom, useAtom } from "jotai";

import { useCallback, useEffect, useRef, useState } from "react";
import { ItemType, Player } from "@/types";
import { useZiggy } from "../use-ziggy";
import { fetchApi } from "@/utils";
import { Article, Channel } from "@/types/models";
import { useAuthUser, useRefSync } from "@/hooks";

export function usePlayer({ listenToSeek = false } = {}) {
    // Local States
    const [, setKey] = useState(0);

    // Animation frame reference to properly cleanup
    const animationFrameRef = useRef<number | null>(null);

    // Inertial status
    const authUser = useAuthUser();

    // Atoms
    const activePlayer = useAtomValue(activePlayerAtom);
    const [appPlayerVolume, setAppPlayerVolume] = useAtom(appPlayerVolumeAtom);
    const [articlesPlays, setArticlePlays] = useAtom(articlesPlaysAtom);
    const [playerSeek, setPlayerSeek] = useAtom(playerSeekAtom);

    // Atoms setters
    const setActiveArticle = useSetAtom(activeArticleAtom);

    // Refs
    const $activePlayer = useRefSync(activePlayer);
    const $articlesPlays = useRefSync(articlesPlays);

    // Routes
    const route = useZiggy();

    // Player controllers
    const { resume, stop, pause, toggle } = usePlayerControllers(activePlayer);

    // Clean up sounds if auth is lost
    useEffect(() => {
        if (!authUser && activePlayer?.sound) {
            try {
                activePlayer.sound.unload();
                Howler.unload();
            } catch (error) {
                console.error("Error unloading sound after auth lost:", error);
            }
        }
    }, [authUser, activePlayer]);

    // Trigger the renderer when called
    const rerender = useCallback(() => setKey((k) => k + 1), []);

    const playArticles = useCallback(
        (articles: Article[]) => {
            if (!articles.length) return;

            // Stop the current player first
            if ($activePlayer.current?.sound) {
                try {
                    $activePlayer.current.sound.stop();
                    $activePlayer.current.sound.seek(0);
                } catch (error) {
                    console.error(
                        "Error stopping sound before playing new articles:",
                        error
                    );
                }
            }

            setTimeout(() => {
                setArticlePlays(articles);
                setActiveArticle(articles[0]);

                // Small timeout to ensure state updates before resuming
                setTimeout(() => {
                    // Play the active article
                    resume();
                }, 50);
            }, 0);
        },
        [resume, setArticlePlays, setActiveArticle]
    );

    /**
     * Update a specific article in the playlist
     */
    const updateArticlePlay = useCallback(
        (article: Article) => {
            setArticlePlays((ps) => {
                return ps.map((v) => (v.id === article.id ? article : v));
            });
        },
        [setArticlePlays]
    );

    /**
     * Fetch articles for a channel and play them
     */
    const getChannelArticles = useCallback(
        (channel: Channel) => {
            return fetchApi<Article[]>(
                route("app.channels.articles", { channel: channel.id })
            )
                .then((data) => {
                    if (Array.isArray(data) && data.length > 0) {
                        playArticles(data);
                        return data;
                    }
                    return [];
                })
                .catch((error) => {
                    console.error("Error fetching channel articles:", error);
                    return [];
                });
        },
        [route, playArticles]
    );

    /**
     * Play items based on type (article, channel, or article list)
     */
    const play = useCallback(
        (
            item:
                | ItemType
                | {
                      type: "articles";
                      data: Article[];
                  }
        ) => {
            if (item.type === "article") {
                playArticles([item.data]);
            } else if (item.type === "channel") {
                return getChannelArticles(item.data);
            } else if (item.type === "articles") {
                playArticles(item.data);
            }
        },
        [getChannelArticles, playArticles]
    );

    /**
     * Skip to a specific track based on its playlist article id.
     */
    const skipTo = useCallback(
        (article: Article) => {
            if (!$activePlayer.current?.sound) return;
            const sound = $activePlayer.current.sound;
            const aArticle = $activePlayer.current.article;

            // If already on this article, just ensure it's playing
            if (aArticle.id === article.id) {
                if (!sound.playing()) {
                    if (sound.state() === "unloaded") {
                        sound.load();
                        sound.once("load", () => sound.play());
                    } else {
                        sound.play();
                    }
                }
                return;
            }

            // Stop current playback before switching
            try {
                sound.stop();
                sound.seek(0);
            } catch (error) {
                console.error("Error stopping sound before skipping:", error);
            }

            // Update active article
            setActiveArticle(article);

            // Small timeout to ensure state updates before resuming
            setTimeout(() => {
                resume();
            }, 50);
        },
        [setActiveArticle, resume]
    );

    /**
     * Add an item to the playlist
     */
    const add = useCallback(
        (item: ItemType) => {
            if (item.type === "article") {
                setArticlePlays((ps) => ps.concat(item.data));
            } else if (item.type === "channel") {
                return getChannelArticles(item.data);
            }
        },
        [getChannelArticles, setArticlePlays]
    );

    /**
     * Skip to the next or previous track.
     * @param  {String} direction 'next' or 'prev'.
     */
    const skip = useCallback(
        (direction: "next" | "prev") => {
            if (!$activePlayer.current) return;
            const player = $activePlayer.current;
            const aArticle = player.article;

            // Find current article index
            const currentIndex = $articlesPlays.current.findIndex(
                (article) => aArticle.id === article.id
            );

            if (currentIndex === -1) return;

            // Calculate next index with wrap-around
            let nextIndex =
                direction === "next" ? currentIndex + 1 : currentIndex - 1;

            // Wrap around if we reach the end or beginning
            if (nextIndex >= $articlesPlays.current.length) {
                nextIndex = 0;
            } else if (nextIndex < 0) {
                nextIndex = $articlesPlays.current.length - 1;
            }

            const nextArticle = $articlesPlays.current[nextIndex];

            if (nextArticle) {
                try {
                    if (player.sound) {
                        player.sound.stop();
                        player.sound.seek(0);
                    }
                } catch (error) {
                    console.error("Error stopping sound during skip:", error);
                }

                // Update active article
                setActiveArticle({ ...nextArticle });

                // Small timeout to ensure state updates before resuming
                setTimeout(() => {
                    resume();
                }, 50);
            }
        },
        [setActiveArticle, resume]
    );

    /**
     * Set the volume and update the volume state.
     * @param  {Number} val Volume between 0 and 1.
     */
    const volume = useCallback(
        (val: number) => {
            // Ensure volume is within valid range
            const safeVolume = Math.max(0, Math.min(1, val));
            setAppPlayerVolume(safeVolume);
        },
        [setAppPlayerVolume]
    );

    /**
     * Format the time from seconds to M:SS.
     * @param  {Number} secs Seconds to format.
     * @return {String}      Formatted time.
     */
    const formatTime = useCallback((secs: number) => {
        const minutes = Math.floor(secs / 60) || 0;
        const seconds = Math.floor(secs - minutes * 60) || 0;

        return minutes + ":" + (seconds < 10 ? "0" : "") + seconds;
    }, []);

    /**
     * The step called within requestAnimationFrame to update the playback position.
     * Track the player seek progress, and the play counter to the server if article played at least half
     */
    const autoStepSeek = useCallback(
        throttle(() => {
            if (!$activePlayer.current?.sound) return;
            const sound = $activePlayer.current.sound;
            const article = $activePlayer.current.article;
            const audio = article?.audio;

            // Only proceed if the sound is loaded
            if (sound.state() === "unloaded") {
                animationFrameRef.current = requestAnimationFrame(autoStepSeek);
                return;
            }

            try {
                // Determine our current seek position.
                setPlayerSeek(Math.round(sound.seek() || 0));
            } catch (error) {
                console.error("Error updating seek position:", error);
            }

            // If the sound is still playing, continue stepping.
            if (sound.playing()) {
                animationFrameRef.current = requestAnimationFrame(autoStepSeek);
            }
        }, 100),
        [setPlayerSeek]
    );

    /**
     * Seek to a new position in the currently playing track.
     */
    const jumpSeek = useCallback(function (per: number) {
        if (!$activePlayer.current?.sound) return;
        const sound = $activePlayer.current.sound;

        try {
            // Ensure sound is loaded before seeking
            if (sound.state() === "unloaded") {
                sound.load();
                sound.once("load", () => {
                    sound.seek(per);
                    sound.play();
                });
            } else {
                sound.seek(per);
                sound.play();
            }
        } catch (error) {
            console.error("Error during jumpSeek:", error);
        }
    }, []);

    // Cleanup animation frames on unmount
    useEffect(() => {
        return () => {
            if (animationFrameRef.current !== null) {
                cancelAnimationFrame(animationFrameRef.current);
            }
        };
    }, []);

    // Trigger rerender whenever sound state changes
    useEffect(() => {
        if (!activePlayer?.sound) return;
        const sound = activePlayer.sound;

        const eventHandlers = {
            play: rerender,
            pause: rerender,
            stop: rerender,
            end: rerender,
            load: rerender,
        };

        // Add event listeners
        Object.entries(eventHandlers).forEach(([event, handler]) => {
            sound.on(event as any, handler);
        });

        window.addEventListener("play", rerender);
        window.addEventListener("pause", rerender);

        return () => {
            // Remove event listeners if sound is still valid
            if (sound && typeof sound.off === "function") {
                Object.entries(eventHandlers).forEach(([event, handler]) => {
                    sound.off(event as any, handler);
                });
            }

            window.removeEventListener("play", rerender);
            window.removeEventListener("pause", rerender);
        };
    }, [activePlayer, rerender]);

    // Auto seek when playing audio
    useEffect(() => {
        if (!listenToSeek || !activePlayer?.sound) return;
        const sound = activePlayer.sound;

        const onKeyUp = ({ key, keyCode }: KeyboardEvent) => {
            if (
                keyCode === 179 ||
                ["MediaPlayPause", "MediaStop"].includes(key)
            ) {
                autoStepSeek();
            }
        };

        // Trigger auto step seek when play event occurs
        sound.on("play", autoStepSeek);
        document.addEventListener("keyup", onKeyUp);

        return () => {
            if (sound && typeof sound.off === "function") {
                sound.off("play", autoStepSeek);
            }
            document.removeEventListener("keyup", onKeyUp);

            // Cancel any pending animation frames
            if (animationFrameRef.current !== null) {
                cancelAnimationFrame(animationFrameRef.current);
                animationFrameRef.current = null;
            }
        };
    }, [activePlayer, listenToSeek, autoStepSeek]);

    return authUser
        ? {
              play,
              seek: playerSeek,
              activePlayer,
              pause,
              stop,
              add,
              resume,
              jumpSeek,
              formatTime,
              toggle,
              volume,
              appPlayerVolume,
              articlesPlays,
              skipTo,
              skip,
              updateArticlePlay,
          }
        : undefined;
}

export type IPlayerHook = ReturnType<typeof usePlayer>;

export const usePlayerControllers = (player: Player | null) => {
    const playerRef = useRefSync(player);

    // Methods and Controllers
    const resume = useCallback(() => {
        // Play the active article
        setTimeout(() => {
            if (!playerRef.current?.sound) return;
            const sound = playerRef.current.sound;

            try {
                if (sound.state() === "unloaded") {
                    sound.load();
                    sound.once("load", () => {
                        if (sound && !sound.playing()) {
                            sound.play();
                        }
                    });
                } else if (!sound.playing()) {
                    sound.play();
                }
            } catch (error) {
                console.error("Error resuming playback:", error);
            }
        }, 10);
    }, []);

    const pause = useCallback(() => {
        try {
            if (playerRef.current?.sound?.playing()) {
                playerRef.current.sound.pause();
            }
        } catch (error) {
            console.error("Error pausing playback:", error);
        }
    }, []);

    const stop = useCallback(() => {
        try {
            if (playerRef.current?.sound) {
                playerRef.current.sound.stop();
            }
        } catch (error) {
            console.error("Error stopping playback:", error);
        }
    }, []);

    const toggle = useCallback(() => {
        if (!playerRef.current?.sound) return;
        const sound = playerRef.current.sound;

        try {
            // Play the active article
            if (sound.playing()) {
                pause();
            } else {
                resume();
            }
        } catch (error) {
            console.error("Error toggling playback:", error);
        }
    }, [resume, pause]);

    return {
        resume,
        pause,
        stop,
        toggle,
    };
};
