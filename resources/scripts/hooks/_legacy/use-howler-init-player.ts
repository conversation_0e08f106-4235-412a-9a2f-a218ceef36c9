// @ts-nocheck
import {
    activeArticleAtom,
    activePlayerAtom,
    appPlayerVolumeAtom,
    articlesPlaysAtom,
    playerSeek<PERSON>tom,
    playersAtom,
} from "@/store";
import { Player } from "@/types";
import { Article } from "@/types/models";
import { Howl, Howler } from "howler";
import {
    useCallback,
    useEffect,
    useLayoutEffect,
    useRef,
    useState,
} from "react";
import { useAtomValue, useSetAtom } from "jotai";
import { useRefSync } from "../use-ref-sync";
import { usePlayerControllers } from "./use-howler-player";

const AUTO_SKIP_NEXT = true;

/**
 * This must be call once at top app component
 *
 * @returns
 */
export function useInitPlayer() {
    // Refs
    const [initialized, setInitialized] = useState(false);
    const loaded = useRef(false);

    // Atoms
    const articlesPlays = useAtomValue(articlesPlaysAtom);
    const appPlayerVolume = useAtomValue(appPlayerVolumeAtom);
    const activePlayer = useAtomValue(activePlayerAtom);
    const playerSeek = useAtomValue(playerSeekAtom);

    // Atoms Setters
    const setPlayers = useSetAtom(playersAtom);
    const setActiveArticle = useSetAtom(activeArticleAtom);

    // Refs
    const $articlesPlays = useRefSync(articlesPlays);
    const $activePlayer = useRefSync(activePlayer);

    // Player controller
    const { resume } = usePlayerControllers(activePlayer);

    /**
     * Retrieves the audio for an article and creates a Howl instance.
     * @param article The article object.
     * @returns A Howl instance representing the audio, or undefined if no audio is available.
     */
    const getAudio = useCallback((article: Article) => {
        if (article.audio) {
            return new Howl({
                src: `/audio/${article.audio.id}`,
                format: ["mp3"],
                html5: true,
                preload: false,
                onloaderror: (_id, error) => {
                    console.error(
                        `Error loading audio ${article.audio?.id}:`,
                        error
                    );
                },
            });
        }
        return undefined;
    }, []);

    /**
     * Safe method to unload audio from a player
     */
    const safeUnloadSound = useCallback((player: Player) => {
        try {
            if (player.sound) {
                player.sound.stop();
                player.sound.seek(0);
                player.sound.unload();
            }
        } catch (error) {
            console.error("Error unloading sound:", error);
        }
    }, []);

    const updatePlayers = useCallback(
        (articlesPlays: Article[]) => {
            return (oldPlayers: Player[]) => {
                // Retrieve the existing player IDs
                const existingPlayerIds = oldPlayers.map((p) => p.article.id);

                // Extract the IDs of the article plays
                const articlePlayIds = articlesPlays.map((a) => a.id);

                /**
                 * Update the state of the players based on the current articles
                 */
                const updatedPlayers = articlesPlays.reduce((acc, article) => {
                    if (!existingPlayerIds.includes(article.id)) {
                        // If the article doesn't exist in the players, add it as a new player
                        acc.push({
                            article,
                            sound: getAudio(article),
                        });
                    } else {
                        // If the article already exists, check if the audio file has changed
                        const existingPlayer = oldPlayers.find(
                            (p) => p.article.id === article.id
                        );
                        if (
                            existingPlayer &&
                            existingPlayer.article.audio?.id !==
                                article.audio?.id
                        ) {
                            // If the audio file has changed, unload the previous audio and update it with the new one
                            safeUnloadSound(existingPlayer);
                            acc.push({
                                article,
                                sound: getAudio(article),
                            });
                        } else {
                            // If the audio file hasn't changed, keep the existing player
                            existingPlayer &&
                                acc.push({
                                    article,
                                    sound: existingPlayer.sound,
                                });
                        }
                    }

                    return acc;
                }, [] as Player[]);

                /**
                 * Clean up players that are no longer associated with any article
                 */
                oldPlayers
                    .filter((p) => !articlePlayIds.includes(p.article.id))
                    .forEach(safeUnloadSound);

                return updatedPlayers;
            };
        },
        [getAudio, safeUnloadSound]
    );

    /**
     * Auto skip to next article when current article finishes playing
     */
    const autoSkipNext = useCallback(() => {
        const aArticle = $activePlayer.current?.article;
        const aSound = $activePlayer.current?.sound;

        if (!aArticle || !aSound) return;

        const currentArticleIndex = $articlesPlays.current.findIndex(
            (article) => aArticle.id === article.id
        );

        if (currentArticleIndex === -1) return;

        const nextArticle = $articlesPlays.current[currentArticleIndex + 1];

        if (nextArticle) {
            aSound.stop();
            aSound.seek(0);
            setActiveArticle(nextArticle);

            // Small timeout to ensure state updates before resuming
            setTimeout(() => {
                resume();
            }, 50);
        }
    }, [resume, setActiveArticle]);

    /**
     * Update players when articles change
     */
    useLayoutEffect(() => {
        if (!initialized) return;
        setPlayers(updatePlayers(articlesPlays));
    }, [articlesPlays, initialized, updatePlayers, setPlayers]);

    /**
     * Set default player volume
     */
    useLayoutEffect(() => {
        if (!initialized) return;
        Howler.volume(appPlayerVolume);
    }, [appPlayerVolume, initialized]);

    /**
     * Auto play forward (skip to next) when track ends
     */
    useEffect(() => {
        if (!AUTO_SKIP_NEXT || !activePlayer || !initialized) return;
        const sound = activePlayer.sound;

        if (!sound) return;

        sound.on("end", autoSkipNext);
        return () => {
            sound.off("end", autoSkipNext);
        };
    }, [activePlayer, autoSkipNext, initialized]);

    /**
     * Set the initial player seek, if loaded from cache
     */
    useEffect(() => {
        if (loaded.current || !initialized) return;

        if (
            activePlayer?.sound &&
            playerSeek &&
            activePlayer.sound.state() === "unloaded"
        ) {
            try {
                activePlayer.sound.load();
                activePlayer.sound.once("load", () => {
                    if (activePlayer.sound) {
                        activePlayer.sound.seek(playerSeek);
                        loaded.current = true;
                    }
                });
            } catch (error) {
                console.error("Error setting initial seek position:", error);
            }
        }
    }, [activePlayer, playerSeek, initialized]);

    return { setInitialized };
}
