// @ts-nocheck

import { type PlayerState, type RepeatMode } from "amplitudejs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Article, Channel } from "@/types/models";
import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { useRefSync } from "../use-ref-sync";
import { atomWithStorage } from "jotai/utils";
import { fetchApi, getAppIdentifier, undefinefy } from "@/utils";
import { useZiggy } from "../use-ziggy";
import { ItemType } from "@/types";
import { useAuthUser } from "../features/use-auth-user";

// ##########################  UTILS  ##########################

const prefix = (v: string) => {
    let prefix = "app-store-cache-" + v;

    const identifier = getAppIdentifier();
    if (identifier) {
        prefix = identifier + "-" + prefix;
    }

    return prefix;
};

function mapArticleToSong(article: Article, index: number) {
    return {
        name: article.name,
        artist: article.user?.name || "Unknown Artist",
        album: article.channel.name,
        url: article.audio?.url || "",
        cover_art_url: undefinefy(
            article.image?.url || article.channel.image?.url
        ),
        visualization: article.id,
        index, // Store the index for easier reference
        id: article.id, // Store the article ID for lookup
    };
}

// ##########################  STATE  ##########################

const AmplitudeReader = atom(async () => {
    return (await import("amplitudejs")).default;
});

const isInitializedAtom = atom(false);

// Playlist
const playlistAtom = atomWithStorage<Article[]>(prefix("player-playlist"), []);
const currentSongAtom = atom<Article | null>(null);

// Playback state
const activeSongIndexAtom = atomWithStorage(prefix("player-active-index"), 0);
const playerStateAtom = atom<PlayerState | null>(null);
const isShuffleAtom = atomWithStorage(prefix("player-shuffle"), false);
const repeatStateAtom = atomWithStorage<RepeatMode>(
    prefix("player-repeat-state"),
    false
);
const hasNextAtom = atom(false);
const hasPrevAtom = atom(false);
const totalDurationAtom = atom<number>(0);
const seekingAtom = atom(false);
const seekTimeAtom = atomWithStorage(prefix("player-seek-time"), 0);

// Network state
const loadingAtom = atom(false);
const errorAtom = atom<string | null>(null);

// Seek state
type SeekSubscriber = (seek: number) => void;
const seekSubscribersAtom = atom<Set<SeekSubscriber>>(
    () => new Set<SeekSubscriber>()
);

// Volume 0 - 100
const volumeAtom = atomWithStorage(prefix("player-volume"), 60);

// ##########################  COMPONENT  ##########################

function PlayerInit() {
    const Amplitude = useAtomValue(AmplitudeReader);
    const [isInitialized, setIsInitialized] = useAtom(isInitializedAtom);
    const $isInitialized = useRefSync(isInitialized);

    // Playlist
    const playlist = useAtomValue(playlistAtom);
    const playlistIds = useMemo(
        () => playlist.map((a) => a.id).join("-"),
        [playlist]
    );
    const setCurrentSong = useSetAtom(currentSongAtom);

    // Playback state
    const setPlayerState = useSetAtom(playerStateAtom);

    const [IsShuffle, setIsShuffle] = useAtom(isShuffleAtom);
    const $isShuffle = useRefSync(IsShuffle);

    const repeatState = useAtomValue(repeatStateAtom);
    const $repeatState = useRefSync(repeatState);

    const setHasNext = useSetAtom(hasNextAtom);
    const setHasPrev = useSetAtom(hasPrevAtom);

    const [activeSongIndex, setActiveSongIndex] = useAtom(activeSongIndexAtom);
    const $activeSongIndex = useRefSync(activeSongIndex);

    const setTotalDuration = useSetAtom(totalDurationAtom);

    const [seeking, setSeeking] = useAtom(seekingAtom);
    const $seeking = useRefSync(seeking);

    const [seekTime, setSeekTime] = useAtom(seekTimeAtom);
    const $seekTime = useRefSync(seekTime);

    // Seek state
    const $seekSubscribers = useRefSync(useAtomValue(seekSubscribersAtom));

    // Player
    const $volume = useRefSync(useAtomValue(volumeAtom));

    // Network state
    const setLoading = useSetAtom(loadingAtom);
    const setError = useSetAtom(errorAtom);

    const cleanupAmplitude = useCallback(() => {
        if (Amplitude.getPlayerState() === "playing") {
            Amplitude.stop();
        }
        setIsInitialized(false);
    }, [setIsInitialized]);

    // Update player state based on current AmplitudeJS state
    const updatePlayerState = useCallback(
        (initialSeekTime?: number) => {
            if (!isInitialized || playlist.length === 0) return;

            const activeIndex = Amplitude.getActiveIndex();
            const songs = Amplitude.getSongs();
            const activeSong = songs[activeIndex];

            if (activeSong) {
                setCurrentSong(
                    playlist.find((s) => s.audio?.url === activeSong.url) ||
                        null
                );
            }

            // This will be executed only on initial load
            if (activeSong && initialSeekTime) {
                Amplitude.skipTo(initialSeekTime, activeIndex);
                Amplitude.pause();
            }

            setActiveSongIndex(activeIndex);
            setPlayerState(Amplitude.getPlayerState());
            setIsShuffle(Amplitude.getShuffle());

            if (!$seeking.current) {
                setTotalDuration(Amplitude.getSongDuration());
            }

            // Check if there are next/previous songs
            setHasNext(
                activeIndex < playlist.length - 1 ||
                    Amplitude.getRepeat() !== false
            );
            setHasPrev(activeIndex > 0);
        },
        [isInitialized, playlist, $seeking]
    );

    const $updatePlayerState = useRefSync(updatePlayerState);

    // Initialize AmplitudeJS with the current playlist
    useEffect(() => {
        // Skip initialization if playlist is empty
        if (playlist.length === 0) {
            return;
        }

        // @ts-ignore
        window.Amplitude = Amplitude;

        // Clean up any existing instance if we're taking control
        if ($isInitialized.current) {
            if (Amplitude.getPlayerState() === "playing") {
                Amplitude.stop();
            }
        }

        if ($activeSongIndex.current > playlist.length - 1) {
            $activeSongIndex.current = 0;
        }

        const songs = playlist.map(mapArticleToSong);

        // Initialize with configuration
        Amplitude.init({
            songs,
            callbacks: {
                initialized: () => {
                    setIsInitialized(true);

                    setTimeout(() => {
                        // With initial seek time
                        $updatePlayerState.current($seekTime.current);

                        // set some initial values
                        if ($repeatState.current) {
                            switch ($repeatState.current) {
                                case "song":
                                    Amplitude.setRepeat(true);
                                    Amplitude.setRepeatSong(true);
                                    break;
                                case "playlist":
                                    Amplitude.setRepeat(true);
                                    break;
                            }
                        }
                    }, 50);
                },
                // Update player state
                play: () => $updatePlayerState.current(),
                stop: () => $updatePlayerState.current(),
                pause: () => $updatePlayerState.current(),
                next: () => $updatePlayerState.current(),
                prev: () => $updatePlayerState.current(),
                song_repeated: () => $updatePlayerState.current(),
                song_change: () => $updatePlayerState.current(),
                ended: () => $updatePlayerState.current(),

                // progress
                seeked: () => setSeeking(false),

                timeupdate: () => {
                    if (!$seeking.current) {
                        // Only update seek time if playing state
                        // This will prevent updating seek time to 0 on initial load
                        if (Amplitude.getPlayerState() === "playing") {
                            setSeekTime(Amplitude.getSongPlayedSeconds());
                        }

                        $seekSubscribers.current.forEach((subscriber) => {
                            subscriber(Amplitude.getSongPlayedSeconds());
                        });
                    }
                },

                // network state
                loadstart: () => {
                    setLoading(true);
                    setError(null);
                },
                error: (err) => {
                    setError(err?.message || "Network error");
                    setLoading(false);
                },
                abort: () => setLoading(false),
                loadeddata: () => {
                    setLoading(false);
                    $updatePlayerState.current();
                },
            },
            bindings: {
                37: "prev",
                39: "next",
            },
            shuffle_on: $isShuffle.current,
            start_song: $activeSongIndex.current,
            volume: $volume.current,
            continue_next: true,
            debug: false,
            preload: "metadata",
        });

        return cleanupAmplitude;
    }, [playlistIds]); // only update when playlist ids changes

    return null;
}

export function PlayerProvider({ children }: { children: React.ReactNode }) {
    return (
        <>
            {children}
            <PlayerInit />
        </>
    );
}

// ##########################  HOOK  ##########################

export function usePlayer(listenToSeek = false) {
    const Amplitude = useAtomValue(AmplitudeReader);
    const isInitialized = useAtomValue(isInitializedAtom);
    const currentSong = useAtomValue(currentSongAtom);
    const seekSubscriber = useAtomValue(seekSubscribersAtom);

    // player state
    const playlist = useAtomValue(playlistAtom);
    const playerState = useAtomValue(playerStateAtom);
    const [isShuffle, setIsShuffle] = useAtom(isShuffleAtom);
    const [repeatState, setRepeatState] = useAtom(repeatStateAtom);
    const hasNext = useAtomValue(hasNextAtom);
    const hasPrev = useAtomValue(hasPrevAtom);
    const [volume, setVolume] = useAtom(volumeAtom);
    const [seek, setSeek] = useState(0);

    const totalDuration = useAtomValue(totalDurationAtom);
    const setSeeking = useSetAtom(seekingAtom);

    // Auth
    const authUser = useAuthUser();

    // Network state
    const loading = useAtomValue(loadingAtom);
    const error = useAtomValue(errorAtom);

    // Safe operation function - only perform actions if AmplitudeJS is initialized
    const safeOperation = useCallback(
        function <T>(operation: () => T) {
            if (!isInitialized) {
                return;
            }

            return operation();
        },
        [isInitialized]
    );

    const seekTo = useCallback(
        (seconds: number) => {
            safeOperation(() => {
                const activeIndex = Amplitude.getActiveIndex();

                if (![-1, null, undefined].includes(activeIndex)) {
                    setSeeking(true);
                    setSeek(seconds);
                    Amplitude.skipTo(seconds, activeIndex);
                }
            });
        },
        [safeOperation, setSeek]
    );

    useEffect(() => {
        if (!listenToSeek || !currentSong) return;

        const subscriber = (seek: number) => setSeek(seek);
        seekSubscriber?.add(subscriber);
        return () => {
            seekSubscriber.delete(subscriber);
        };
    }, [currentSong, listenToSeek]);

    const seekPercent = useMemo(() => {
        return safeOperation(() => Amplitude.getSongPlayedPercentage());
    }, [seek, safeOperation]);

    const formatTime = useCallback((secs: number) => {
        const minutes = Math.floor(secs / 60) || 0;
        const seconds = Math.floor(secs - minutes * 60) || 0;
        return minutes + ":" + (seconds < 10 ? "0" : "") + seconds;
    }, []);

    const formattedSeekTime = useMemo(() => {
        return formatTime(seek);
    }, [formatTime, seek]);

    const updateVolume = useCallback(
        (volume: number) => {
            safeOperation(() => {
                Amplitude.setVolume(volume);
                setVolume(volume);
            });
        },
        [safeOperation]
    );

    const next = useCallback(() => {
        // Couldn't use native Amplitude.next() method because it doesn't next on repeat
        safeOperation(() => {
            const currentIndex = Amplitude.getActiveIndex();
            const nextIndex = currentIndex + 1;

            if (nextIndex >= playlist.length) {
                return;
            }

            Amplitude.playSongAtIndex(nextIndex);
        });
    }, [safeOperation]);

    const prev = useCallback(() => {
        // Couldn't use native Amplitude.prev() method because it doesn't prev on repeat
        safeOperation(() => {
            const currentIndex = Amplitude.getActiveIndex();
            const prevIndex = currentIndex - 1;

            if (prevIndex < 0) {
                return;
            }

            Amplitude.playSongAtIndex(prevIndex);
        });
    }, [safeOperation]);

    const pause = useCallback(() => {
        safeOperation(() => {
            Amplitude.pause();
        });
    }, [safeOperation]);

    const toggleShuffle = useCallback(() => {
        safeOperation(() => {
            const newShuffle = !Amplitude.getShuffle();
            Amplitude.setShuffle(newShuffle);
            setIsShuffle(newShuffle);
        });
    }, [safeOperation, setIsShuffle]);

    const timeoutRepeat = useRef<number>(0);
    const toggleRepeat = useCallback(() => {
        // Used timeout to fix some unexpected behavior
        safeOperation(() => {
            switch (repeatState) {
                case false:
                case null:
                    clearTimeout(timeoutRepeat.current);
                    timeoutRepeat.current = window.setTimeout(() => {
                        Amplitude.setRepeat(true);
                        Amplitude.setRepeatSong(false);
                    }, 1000);

                    setRepeatState("song");
                    break;
                case "song":
                    clearTimeout(timeoutRepeat.current);
                    timeoutRepeat.current = window.setTimeout(() => {
                        Amplitude.setRepeat(true);
                        Amplitude.setRepeatSong(false);
                    }, 1000);

                    setRepeatState("playlist");
                    break;
                case "playlist":
                    clearTimeout(timeoutRepeat.current);
                    timeoutRepeat.current = window.setTimeout(() => {
                        Amplitude.setRepeat(false);
                        Amplitude.setRepeatSong(false);
                    }, 1000);

                    setRepeatState(false);
                    break;
            }
        });
    }, [safeOperation, setRepeatState, repeatState]);

    const togglePlay = useCallback(() => {
        safeOperation(() => {
            if (["stopped", "paused"].includes(playerState as any)) {
                Amplitude.play();
            } else {
                Amplitude.pause();
            }
        });
    }, [safeOperation, playerState]);

    const returnedObject = {
        // State
        isInitialized,
        playlist,
        playerState,
        isShuffle,
        repeatState,
        hasNext,
        hasPrev,
        seek,
        currentSong,
        seekPercent,
        formattedSeekTime,
        volume,
        loading,
        error,

        // actions
        seekTo,
        next,
        prev,
        pause,
        togglePlay,
        updateVolume,
        toggleRepeat,
        toggleShuffle,
        totalDuration,

        // Utils
        formatTime,
        ...usePlayerWithModels(safeOperation, playlist),
    };

    return authUser ? returnedObject : undefined;
}

export type IPlayerHook = ReturnType<typeof usePlayer>;

type SafeOperation = <T>(operation: () => T) => T | undefined;

type GuessPlaylist =
    | ItemType
    | {
          type: "articles";
          data: Article[];
      };

function usePlayerWithModels(
    safeOperation: SafeOperation,
    playlist: Article[]
) {
    const Amplitude = useAtomValue(AmplitudeReader);
    const setSeekTime = useSetAtom(seekTimeAtom);
    const setPlaylist = useSetAtom(playlistAtom);
    const setActiveSongIndex = useSetAtom(activeSongIndexAtom);
    const setCurrentSong = useSetAtom(currentSongAtom);

    // Routes
    const route = useZiggy();

    const jumpTo = useCallback(
        (article: Article) => {
            safeOperation(() => {
                const songs = Amplitude.getSongs();
                const songIndex = songs.findIndex(
                    (s) => s.url === article.audio?.url
                );

                if (songIndex > -1) {
                    Amplitude.playSongAtIndex(songIndex);
                }
            });
        },
        [safeOperation, playlist]
    );

    // Network actions
    const getChannelArticles = useCallback(
        (channel: Channel) => {
            return fetchApi<Article[]>(
                route("app.channels.articles", { channel: channel.id })
            )
                .then((data) => {
                    if (Array.isArray(data) && data.length > 0) {
                        return data;
                    }
                    return [];
                })
                .catch((error) => {
                    console.error("Error fetching channel articles:", error);
                    return [];
                });
        },
        [route]
    );

    const playArticles = useCallback(
        (articles: Article[], activeArticle?: Article) => {
            if (!articles.length) return;
            // Reset seek time
            setSeekTime(0);
            setActiveSongIndex(0);

            setPlaylist(articles);

            safeOperation(() => {
                setTimeout(() => {
                    if (activeArticle) {
                        jumpTo(activeArticle);
                    } else {
                        // Play the active article
                        Amplitude.playSongAtIndex(0);
                    }
                }, 100);
            });
        },
        [safeOperation, jumpTo]
    );

    const updateArticleInPlaylist = useCallback(
        (article: Article) => {
            setCurrentSong((c) => (c?.id === article.id ? article : c));
            setPlaylist((ps) => {
                return ps.map((v) => (v.id === article.id ? article : v));
            });
        },
        [setPlaylist]
    );

    const guessPlaylist = useCallback(
        async (item: GuessPlaylist) => {
            if (item.type === "article") {
                playArticles([item.data]);
            } else if (item.type === "channel") {
                const articles = await getChannelArticles(item.data);
                if (articles.length > 0) {
                    playArticles(articles, item.activeArticle);
                }
            } else if (item.type === "articles") {
                playArticles(item.data);
            }
        },
        [getChannelArticles, playArticles]
    );

    return {
        jumpTo,
        updateArticleInPlaylist,
        playArticles,
        guessPlaylist,
    };
}
