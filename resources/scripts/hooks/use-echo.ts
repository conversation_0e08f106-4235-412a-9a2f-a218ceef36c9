import { User } from "@/types/models";
import { echo } from "@/utils/socket";
import Echo, { Broadcaster } from "laravel-echo";
import { useEffect } from "react";
import { useAuthUser } from "./features/use-auth-user";
import { useRefSync } from "./use-ref-sync";

type Callback = (
    echo: Echo<keyof Broadcaster>,
    user: User
) => VoidFunction | void;

export function useEcho(callback: Callback) {
    const auth = useAuthUser();
    const $callback = useRefSync(callback);

    useEffect(() => {
        if (!auth?.id) return;
        echo.connect();

        const unsubscribe = $callback.current(echo, auth);

        return () => {
            unsubscribe?.();
            echo.disconnect();
        };
    }, [auth?.id]);
}
