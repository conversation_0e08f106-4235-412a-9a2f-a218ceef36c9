import { atom, useAtom } from "jotai";
import { useCallback } from "react";

declare global {
    interface Window {
        theme_storage_key: string;
    }
}

const themeAtom = atom<"light" | "dark" | null>(null);
themeAtom.onMount = (set) => {
    const key = window.theme_storage_key;
    let theme = localStorage.getItem(key);
    if (!theme) {
        theme = window.matchMedia("(prefers-color-scheme: dark)").matches
            ? "dark"
            : "light";
    }

    if (theme) {
        set(theme as any);
    }
};

export function useTheme() {
    const [theme, setTheme] = useAtom(themeAtom);

    const toggle = useCallback(() => {
        const key = window.theme_storage_key;
        // if set via local storage previously
        if (localStorage.getItem(key)) {
            if (localStorage.getItem(key) === "light") {
                document.documentElement.classList.add("dark");
                localStorage.setItem(key, "dark");
                setTheme("dark");
            } else {
                document.documentElement.classList.remove("dark");
                localStorage.setItem(key, "light");
                setTheme("light");
            }
        } else {
            if (document.documentElement.classList.contains("dark")) {
                document.documentElement.classList.remove("dark");
                localStorage.setItem(key, "light");
                setTheme("light");
            } else {
                document.documentElement.classList.add("dark");
                localStorage.setItem(key, "dark");
                setTheme("dark");
            }
        }
    }, []);

    return { toggle, theme };
}
