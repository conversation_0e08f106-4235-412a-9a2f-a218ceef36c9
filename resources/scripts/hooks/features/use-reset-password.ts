import { useToasts } from "@/components/lib";
import { useZiggy } from "../use-ziggy";
import { useForm } from "@inertiajs/react";

export function useForgotPassword(onSuccess?: () => void) {
    const route = useZiggy();
    const { pushToast } = useToasts();

    const { data, post, processing, errors, reset, setData } = useForm({
        email: "",
    });

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        post(route("password.email"), {
            onSuccess: () => {
                pushToast({
                    title: `
                        Password reset instructions have been sent to your email.
                        Please check your inbox and follow the provided link to reset your password.
                    `,
                    duration: 30,
                    type: "default",
                });
                reset();
                onSuccess && onSuccess();
            },
        });
    };

    return {
        data,
        post,
        processing,
        errors,
        handleSubmit,
        setData,
    };
}

export function useResetPassword(token: string, email?: string | null) {
    const route = useZiggy();
    const { pushToast } = useToasts();

    const { data, post, processing, errors, reset, setData } = useForm({
        email: email || "",
        password: "",
        password_confirmation: "",
        token,
    });

    const onChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        post(route("password.update"), {
            onSuccess: () => {
                reset();
                pushToast({
                    title: "Password Reset Successful",
                    duration: 15,
                    type: "success",
                });
            },
        });
    };

    return {
        onChange,
        data,
        processing,
        errors,
        handleSubmit,
    };
}
