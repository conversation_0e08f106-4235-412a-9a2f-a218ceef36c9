import { ipApi } from "@/api";
import { useToasts } from "@/components/lib";
import { useForm } from "@inertiajs/react";
import { useEffect } from "react";

export function useRegistration(onSuccess?: () => void) {
    const { pushToast } = useToasts();

    const { data, post, processing, errors, setData } = useForm({
        name: "",
        email: "",
        password: "",
        password_confirmation: "",
        country_name: "",
        country_code: "",
        timezone: "",
        city: "",
        region: "",
    });

    useEffect(() => {
        ipApi().then((data) => {
            setData((ds) => ({
                ...ds,
                country_name: data.country_name,
                country_code: data.country_code,
                timezone: data.timezone,
                city: data.city,
                region: data.region,
            }));
        });
    }, []);

    const onChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const onRegistration = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        post("/register", {
            onSuccess: () => {
                pushToast({
                    title: "A email verification link has been emailed to you!",
                    type: "success",
                    duration: 30,
                });

                onSuccess && onSuccess();
            },
        });
    };

    return {
        data,
        onChange,
        onRegistration,
        processing,
        errors,
    };
}

export function useLogin() {
    const { data, post, processing, errors, setData } = useForm({
        email: "",
        password: "",
        remember: true,
    });

    const onChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const onLogin = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        post(`/login?redirect=${location.href}`, {
            onSuccess: () => {
                location.reload();
            },
        });
    };

    return {
        data,
        post,
        onChange,
        processing,
        errors,
        onLogin,
    };
}
