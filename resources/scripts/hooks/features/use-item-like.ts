import { ItemType } from "@/types";
import { useCallback } from "react";
import { router } from "@inertiajs/react";
import { useZiggy } from "../use-ziggy";
import { fetchApi } from "@/utils";
import { Article } from "@/types/models";
import { usePlayer } from "@/components/player";

export function useItemLike() {
    const route = useZiggy();
    const player = usePlayer();

    const likeCall = useCallback(
        (item: ItemType, refreshPlayer = false) => {
            return router.put(
                route(`app.like.${item.type}`, { [item.type]: item.data.id }),
                undefined,
                {
                    preserveScroll: true,
                    onSuccess: () => {
                        if (refreshPlayer && item.type === "article") {
                            refreshItem({ type: item.type, data: item.data });
                        }
                    },
                }
            );
        },
        [route]
    );

    const refreshItem = useCallback(
        (item: ItemType) => {
            if (item.type !== "article") {
                return;
            }
            return fetchApi<Article>(
                route(`app.articles.view`, {
                    article: item.data.id,
                })
            ).then((article) => player?.updateArticleInPlaylist(article));
        },
        [route]
    );

    return { likeCall };
}
