import { useToasts } from "@/components/lib";
import { useForm } from "@inertiajs/react";
import { useAuthUser } from "./use-auth-user";
import { useZiggy } from "../use-ziggy";

export function useSettingsAccountProfile() {
    const { pushToast } = useToasts();
    const user = useAuthUser();
    const route = useZiggy();

    const { data, put, processing, errors, setData } = useForm({
        name: user?.name || "",
        email: user?.email || "",
        phone: user?.phone || "",
        country_name: user?.country_name || "",
        description: user?.description || "",
    });

    const onChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const oldEmail = user?.email;

        put(route("user-profile-information.update"), {
            errorBag: "updateProfileInformation",
            onSuccess: () => {
                if (oldEmail !== data.email) {
                    pushToast({
                        title: "An email verification link has been emailed to your new email address!",
                        type: "success",
                        duration: 30,
                    });
                } else {
                    pushToast({
                        title: "Profile informations updated!",
                        type: "success",
                        duration: 10,
                    });
                }
            },
        });
    };

    return {
        data,
        onChange,
        processing,
        errors,
        handleSubmit,
    };
}

export function useSettingsAccountImage(onSuccess?: () => void) {
    const { pushToast } = useToasts();
    const route = useZiggy();

    const { data, post, processing, errors, setData } = useForm<{
        image: File | null;
    }>({
        image: null,
    });

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        post(route("app.settings.account.image-profile"), {
            onSuccess: () => {
                pushToast({
                    title: "Profile image updated!",
                    type: "success",
                    duration: 10,
                });

                onSuccess && onSuccess();
            },
        });
    };

    return {
        data,
        processing,
        errors,
        setData,
        handleSubmit,
    };
}

export function useSettingsAccountUpdatePassword() {
    const { pushToast } = useToasts();
    const route = useZiggy();

    const { data, put, processing, errors, reset, setData } = useForm({
        current_password: "",
        password: "",
        password_confirmation: "",
    });

    const onChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        put(route("user-password.update"), {
            errorBag: "updatePassword",
            onSuccess: () => {
                reset();
                pushToast({
                    title: "Password updated!",
                    type: "success",
                    duration: 10,
                });
            },
        });
    };

    return {
        data,
        processing,
        errors,
        onChange,
        onSubmit,
    };
}

export function useSettingsArtistRequest() {
    const route = useZiggy();

    const { data, post, processing, errors, reset, setData } = useForm({
        artist_type: "",
        description: "",
        youtube_link: "",
        social_links: {} as Record<string, string>,
    });

    const onChange = (
        ev: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        setData((ds) => ({
            ...ds,
            [ev.target.name]: ev.target.value,
        }));
    };

    const onSocialLinkChange = (platform: string, url: string) => {
        setData((ds) => ({
            ...ds,
            social_links: {
                ...ds.social_links,
                [platform]: url,
            },
        }));
    };

    const removeSocialLink = (platform: string) => {
        setData((ds) => {
            const newSocialLinks = { ...ds.social_links };
            delete newSocialLinks[platform];
            return {
                ...ds,
                social_links: newSocialLinks,
            };
        });
    };

    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        post(route("app.settings.artist-request"), {
            onSuccess: () => reset(),
        });
    };

    return {
        data,
        post,
        processing,
        errors,
        onChange,
        onSubmit,
        onSocialLinkChange,
        removeSocialLink,
    };
}
