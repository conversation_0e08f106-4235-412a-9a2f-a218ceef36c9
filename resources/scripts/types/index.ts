import { Article, Channel, Playlist } from "./models";

type Nl<T> = T | (T | null) | (T | undefined);

type PrimitiveValue = Nl<string> | Nl<boolean> | Nl<number>;

export type OnlyPrimitiveFields<T> = {
    [k in keyof T]: T[k] extends PrimitiveValue ? T[k] : never;
};

export type CanPolicy<K extends string> = {
    can: {
        [i in K]: boolean;
    };
};

export interface Paginate<T> {
    data: T[];
    links: Links;
    meta: Meta;
}

interface Links {
    first: string;
    last: string;
    prev: any;
    next: any;
}

interface Meta {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
}

export type ItemType =
    | {
          type: "article";
          data: Article;
      }
    | {
          type: "playlist";
          data: Playlist;
          activeArticle?: Article;
      }
    | {
          type: "channel";
          data: Channel;
          activeArticle?: Article;
      };

export type PlayerSeek = {
    id: string | null;
    seek: number;
};
