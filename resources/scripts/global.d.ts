declare module "x-amplitudejs" {
    export type RepeatMode = false | "song" | "playlist";
    export type PlayerState = "playing" | "paused" | "stopped";

    interface Song {
        name: string;
        artist?: string;
        album?: string;
        url: string;
        cover_art_url?: string;
        visualization?: string;
        live?: boolean;
        time_callbacks?: Record<string, () => void>;
        amplitude_callbacks?: Record<string, () => void>;
    }

    interface Config {
        songs: Song[];
        playlists?: Record<string, { songs: number[]; title?: string }>;
        default_album_art?: string;
        autoplay?: boolean;
        bindings?: Record<number, string>;
        callbacks?: {
            initialized?: () => void;
            play?: () => void;
            stop?: () => void;
            pause?: () => void;
            song_repeated?: () => void;
            next?: () => void;
            prev?: () => void;
            song_change?: () => void;
            seeked?: () => void;
            seeking?: () => void;
            volumechange?: () => void;
            timeupdate?: () => void;
            ended?: () => void;
            loadstart?: () => void;
            error?: (error: Error | null) => void;
            abort?: () => void;
            loadeddata?: () => void;
        };
        volume?: number;
        volume_increment?: number;
        volume_decrement?: number;
        soundcloud_client?: string;
        start_song?: number;
        starting_playlist?: string;
        debug?: boolean;
        visualization?: string;
        waveforms?: {
            sample_rate?: number;
            bar_width?: number;
            separation_width?: number;
            sample_array_length?: number;
            bar_color?: string;
        };
        continue_next?: boolean;
        delay?: number;
        shuffle_on?: boolean;
        repeat?: boolean;
        preload?: "none" | "metadata" | "auto";
    }

    interface AmplitudeAPI {
        init(config: Config): void;
        bindNewElements(): void;
        getSongs(): Song[];
        getActiveIndex(): number;
        getSongPlayedSeconds(): number;
        getSongPlayedPercentage(): number;
        getAudio(): HTMLAudioElement;

        getShuffle(): boolean;
        getRepeat(): RepeatMode;
        getPlayerState(): PlayerState;
        getSongDuration(): number;
        // getVolume(): number;

        setRepeat(repeat: boolean): void;
        setRepeatSong(repeat: boolean): void;
        setShuffle(shuffle: boolean): void;
        setVolume(level: number): void;

        play(): void;
        playNow(song: Song): void;
        pause(): void;
        stop(): void;
        next(playlist?: string): void;
        prev(playlist?: string): void;
        playSongAtIndex(index: number): void;
        skipTo(seconds: number, songIndex?: number, playlist?: string): void;

        visualize(): void;
        visualizationCapableAudio(): boolean;
    }

    const Amplitude: AmplitudeAPI;
    export default Amplitude;
}
