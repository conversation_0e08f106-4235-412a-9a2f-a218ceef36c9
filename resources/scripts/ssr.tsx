import { createInertiaApp } from "@inertiajs/react";
import createServer from "@inertiajs/react/server";
import ReactDOMServer from "react-dom/server";
import { Providers } from "./providers";

createServer((page) =>
    createInertiaApp({
        page,
        render: ReactDOMServer.renderToString,
        resolve: (name) => {
            const pages = import.meta.glob("../views/pages/**/*.tsx", {
                eager: true,
            });
            return pages[`../views/pages/${name}.tsx`];
        },
        setup: ({ App, props }) => (
            <Providers>
                <App {...props} />
            </Providers>
        ),
    })
);
