{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.0.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive monitoring dashboard for Smovee music streaming platform", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 100, "panels": [], "title": "🏥 Application Health & Status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Database connection status", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "dark-red", "index": 0, "text": "❌ Disconnected"}, "1": {"color": "dark-green", "index": 1, "text": "✅ Connected"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value"}, "targets": [{"expr": "smovee_database_connection_status", "refId": "A"}], "title": "Database Status", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Cache connection status", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "dark-red", "index": 0, "text": "❌ Disconnected"}, "1": {"color": "dark-green", "index": 1, "text": "✅ Connected"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 1}, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value"}, "targets": [{"expr": "smovee_cache_connection_status", "refId": "A"}], "title": "<PERSON>ache <PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Search index status", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "dark-red", "index": 0, "text": "❌ Unhealthy"}, "1": {"color": "dark-green", "index": 1, "text": "✅ Healthy"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 1}, "id": 72, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value"}, "targets": [{"expr": "smovee_search_index_status", "refId": "A"}], "title": "Search Index", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Application info", "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "color-background", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 1}, "id": 6, "options": {"showHeader": true}, "targets": [{"expr": "smovee_app_info", "format": "table", "instant": true, "refId": "A"}], "title": "Application Info", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "job": true, "instance": true}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 101, "panels": [], "title": "👥 User Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Distribution of users by role", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 6}, "id": 8, "options": {"displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "sum by (role) (smovee_users_total)", "legendFormat": "{{role}}", "refId": "A"}], "title": "Users by Role", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "User activity metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 16, "x": 8, "y": 6}, "id": 10, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "smovee_users_active_30d", "legendFormat": "Active Users (30d)", "refId": "A"}, {"expr": "smovee_users_new_24h", "legendFormat": "New Users (24h)", "refId": "B"}, {"expr": "smovee_users_new_7d", "legendFormat": "New Users (7d)", "refId": "C"}, {"expr": "smovee_users_new_30d", "legendFormat": "New Users (30d)", "refId": "D"}], "title": "User Activity Overview", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 102, "panels": [], "title": "🎵 Content & Engagement", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Content overview", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 15}, "id": 18, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "smovee_articles_total", "legendFormat": "Articles", "refId": "A"}, {"expr": "smovee_channels_total", "legendFormat": "Channels", "refId": "B"}, {"expr": "smovee_playlists_total", "legendFormat": "Playlists", "refId": "C"}], "title": "Content Overview", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Articles by genre", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 15}, "id": 24, "options": {"displayLabels": ["name", "value"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "sum by (genre) (smovee_articles_by_genre)", "legendFormat": "{{genre}}", "refId": "A"}], "title": "Articles by <PERSON><PERSON>", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Engagement metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 21}, "id": 30, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "smovee_plays_total", "legendFormat": "Total Plays", "refId": "A"}, {"expr": "smovee_likes_total", "legendFormat": "Total Likes", "refId": "B"}, {"expr": "smovee_comments_total", "legendFormat": "Total Comments", "refId": "C"}, {"expr": "smovee_follows_total", "legendFormat": "Total Follows", "refId": "D"}], "title": "Engagement Metrics", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 103, "panels": [], "title": "🏆 Competition & Performance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Competition metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "purple", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 26}, "id": 42, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "smovee_competitions_total", "legendFormat": "Total Competitions", "refId": "A"}, {"expr": "smovee_competitions_active", "legendFormat": "Active Competitions", "refId": "B"}, {"expr": "smovee_competition_entries_total", "legendFormat": "Total Entries", "refId": "C"}, {"expr": "smovee_competition_votes_total", "legendFormat": "Total Votes", "refId": "D"}], "title": "Competition Overview", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Cache hit rate", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 26}, "id": 62, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "targets": [{"expr": "smovee_cache_hit_rate", "refId": "A"}], "title": "<PERSON><PERSON> Hit Rate", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Queue status", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 26}, "id": 54, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true}, "targets": [{"expr": "sum by (queue) (smovee_queue_jobs_pending)", "legendFormat": "{{queue}}", "refId": "A"}], "title": "Queue Status", "type": "bargauge"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 104, "panels": [], "title": "📊 Infrastructure & Search", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory usage over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 58, "options": {"legend": {"calcs": ["last"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"expr": "smovee_memory_usage_bytes", "legendFormat": "Current Memory", "refId": "A"}, {"expr": "smovee_memory_peak_bytes", "legendFormat": "Peak Memory", "refId": "B"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Search index metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 74, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "smovee_search_indexed_articles", "legendFormat": "Indexed Articles", "refId": "A"}, {"expr": "smovee_search_indexed_users", "legendFormat": "Indexed Users", "refId": "B"}, {"expr": "smovee_search_indexed_channels", "legendFormat": "Indexed Channels", "refId": "C"}, {"expr": "smovee_audio_files_with_embeddings", "legendFormat": "Audio with Embeddings", "refId": "D"}], "title": "Search & AI Metrics", "type": "stat"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["smovee", "application", "prometheus"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Smovee Application Dashboard", "uid": "smovee-dashboard", "version": 1, "weekStart": ""}