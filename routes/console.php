<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Cleanup commands
Schedule::command('telescope:prune --hours=48')->daily();

Schedule::command('queue:prune-batches --hours=48')->daily();

// App commands
Schedule::command('app:store-audio-embeddings')->daily()->at('01:00');

Schedule::command('app:convert-audios-hls')->daily()->at('01:30');

Schedule::command('app:generate-user-recommendations')
    ->dailyAt('03:00')
    ->withoutOverlapping(120);

// Backup commands
// Schedule::command('backup:clean')->daily()->at('02:00');

// Schedule::command('backup:run')->daily()->at('02:30');

// Schedule::command('backup:monitor')->daily()->at('02:55');
