<?php

use App\Features\DumpItemsApi;
use App\Http\Controllers\API\ArticleController;
use App\Http\Controllers\API\ArtistController;
use App\Http\Controllers\API\ChannelController;
use App\Http\Controllers\API\CommentController;
use App\Http\Controllers\API\DiscoveryController;
use App\Http\Controllers\API\FavoriteController;
use App\Http\Controllers\API\FollowController;
use App\Http\Controllers\API\IssueAuthTokenController;
use App\Http\Controllers\API\LikeController;
use App\Http\Controllers\API\NotificationController;
use App\Http\Controllers\API\PlayController;
use App\Http\Controllers\API\PodcastController;
use App\Http\Controllers\API\SearchController;
use App\Http\Controllers\API\SettingsController;
use App\Http\Controllers\API\UserController;
use Illuminate\Support\Facades\Route;
use Laravel\Pennant\Middleware\EnsureFeaturesAreActive;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Authentication
Route::prefix('auth')
    ->group(function () {
        Route::post('token', IssueAuthTokenController::class);
    });

Route::middleware('auth:sanctum')
    ->group(function () {
        // User Profile
        Route::get('/user', [UserController::class, 'profile']);

        // Discovery
        Route::get('/discover', [DiscoveryController::class, 'index']);

        // Podcasts
        Route::get('/podcasts', [PodcastController::class, 'index']);

        // Favorites
        Route::prefix('favorites')
            ->group(function () {
                Route::get('/', [FavoriteController::class, 'index']);
                Route::get('/articles', [FavoriteController::class, 'articles']);
                Route::get('/channels', [FavoriteController::class, 'channels']);
                Route::get('/following', [FavoriteController::class, 'following']);
            });

        // Search
        Route::get('/search', [SearchController::class, 'search']);

        // Artists
        Route::prefix('artists')
            ->group(function () {
                Route::get('/', [ArtistController::class, 'index']);
                Route::get('/{user}', [ArtistController::class, 'show']);
                Route::get('/{user}/channels', [ArtistController::class, 'channels']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [ArtistController::class, 'store']);
                        Route::get('/search/name', [ArtistController::class, 'searchByName']);
                        Route::get('/search/email', [ArtistController::class, 'searchByEmail']);
                    });
            });

        // Articles
        Route::prefix('articles')
            ->group(function () {
                Route::get('/{article}', [ArticleController::class, 'show']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [ArticleController::class, 'store']);
                    });
            });

        // Channels
        Route::prefix('channels')
            ->group(function () {
                Route::get('/', [ChannelController::class, 'index']);
                Route::get('/{channel}', [ChannelController::class, 'show']);
                Route::get('/{channel}/articles', [ChannelController::class, 'articles']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [ChannelController::class, 'store']);
                    });
            });

        // Plays
        Route::put('/plays/articles/{article}', [PlayController::class, 'recordArticlePlay']);

        // Following
        Route::put('/follow/{user}', [FollowController::class, 'toggle']);

        // Likes
        Route::prefix('likes')
            ->group(function () {
                Route::put('/articles/{article}', [LikeController::class, 'toggleArticleLike']);
                Route::put('/channels/{channel}', [LikeController::class, 'toggleChannelLike']);
            });

        // Notifications
        Route::prefix('notifications')
            ->group(function () {
                Route::post('/clear', [NotificationController::class, 'clear']);
                Route::delete('/{notificationId}', [NotificationController::class, 'destroy']);
            });

        // Comments
        Route::prefix('comments')
            ->group(function () {
                Route::put('/{comment}', [CommentController::class, 'update']);
                Route::delete('/{comment}', [CommentController::class, 'destroy']);
            });

        // Article Comments
        Route::prefix('articles/{article}/comments')
            ->group(function () {
                Route::get('/', [CommentController::class, 'articleComments']);
                Route::post('/', [CommentController::class, 'storeArticleComment']);
            });

        // Channel Comments
        Route::prefix('channels/{channel}/comments')
            ->group(function () {
                Route::get('/', [CommentController::class, 'channelComments']);
                Route::post('/', [CommentController::class, 'storeChannelComment']);
            });

        // Settings
        Route::prefix('settings')
            ->group(function () {
                Route::get('/account', [SettingsController::class, 'account']);
                Route::post('/profile-image', [SettingsController::class, 'updateProfileImage']);
                Route::post('/artist-request', [SettingsController::class, 'createArtistRequest']);
            });
    });
