//@ts-check
const colors = require("tailwindcss/colors");

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: "class",
    content: ["./resources/**/*.{js,ts,jsx,tsx}"],
    theme: {
        extend: {
            fontFamily: {
                sans: ["Open Sans", "sans-serif"],
                brand: [
                    "var(--font-heading)",
                    "ui-sans-serif",
                    "system-ui",
                    "sans-serif",
                    '"Apple Color Emoji"',
                    '"Segoe UI Emoji"',
                    '"Segoe UI Symbol"',
                    '"Noto Color Emoji"',
                ],
            },
            screens: {
                xxs: "409px",
                xs: "540px",
                sm: "576px",
                md: "768px",
                tablet: "992px",
                lg: "992px",
                xl: "1200px",
                "2xl": "1400px",
            },
            gridTemplateColumns: {
                genre: "repeat(5, minmax(350px, 1fr))",
            },
            animation: {
                blob: 'blob 7s infinite',
            },
            keyframes: {
                blob: {
                    '0%': {
                        transform: 'translate(0px, 0px) scale(1)',
                    },
                    '33%': {
                        transform: 'translate(30px, -50px) scale(1.1)',
                    },
                    '66%': {
                        transform: 'translate(-20px, 20px) scale(0.9)',
                    },
                    '100%': {
                        transform: 'translate(0px, 0px) scale(1)',
                    },
                },
            },
            colors: {
                primary: {
                    DEFAULT: "#49de80",
                    50: "#f0fdf4",
                    100: "#dcfce7",
                    200: "#bbf7d0",
                    300: "#86efac",
                    // 400: "#49de80",
                    400: "#22c55f",
                    500: "#22c55f",
                    600: "#16a34b",
                    700: "#15803d",
                    800: "#166534",
                    900: "#14532d",
                    950: "#052e16",
                },

                light: {
                    DEFAULT: "#f2f0f7",
                },

                dark: {
                    DEFAULT: "#150f1e",
                },

                // BG Colors
                "dark-primary": {
                    DEFAULT: colors.gray["800"],
                    background: colors.gray["900"],
                    700: colors.gray["700"],
                },

                "light-primary": {
                    DEFAULT: "#f2f4f7",
                    background: "#e8eaec",
                    700: "#dadcdd",
                },

                // Body Text Colors
                "dark-text-color": "#848388",
                "light-text-color": "#1a1625",
                "light-helper-color": "#243c5a",
            },
        },
    },
    plugins: [
        require("@tailwindcss/forms"),
        require("tailwind-scrollbar-hide"),
        require("@tailwindcss/typography"),
        function ({ addUtilities }) {
            const newUtilities = {
                '.animation-delay-2000': {
                    'animation-delay': '2s',
                },
                '.animation-delay-4000': {
                    'animation-delay': '4s',
                },
            }
            addUtilities(newUtilities)
        },
    ],
};
