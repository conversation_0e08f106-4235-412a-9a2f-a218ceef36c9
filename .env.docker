######################## APP ##########################

APP_NAME=Smovee
APP_KEY=base64:EcJpfBEx+ZxvoS2faKLxxnTsziZiML40d2GLBofAHz8==
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

ASSET_URL="${APP_URL}"

APP_MAINTENANCE_DRIVER=cache
# APP_MAINTENANCE_STORE=database

VITE_APP_NAME="${APP_NAME}"

############### AUTO GENERATE GENRES ###############

DEFAULT_GENRES=true

################ OCTANE ############################

OCTANE_SERVER=frankenphp
OCTANE_HTTPS=false

################ CONTAINER #########################

PHP_CLI_SERVER_WORKERS=4

################ BCRYPT ###########################

BCRYPT_ROUNDS=12

################ LOGGING #########################

LOG_CHANNEL=stderr
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

################ DATABASE ########################

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE="${POSTGRES_DB}"
DB_USERNAME="${POSTGRES_USER}"
DB_PASSWORD="${POSTGRES_PASSWORD}"

################# POSTGRES ####################

POSTGRES_DB=smovee
POSTGRES_USER=postgres
POSTGRES_PASSWORD=Uhoo7hah

################# SESSION #########################

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

################# BROADCASTING ###################

BROADCAST_CONNECTION=null

################# FILESYSTEM #####################

FILESYSTEM_DISK=local

################# QUEUE ###########################

QUEUE_CONNECTION=redis

################# CACHE ###########################

CACHE_STORE=redis

################# SCOUT ###########################

SCOUT_DRIVER=meilisearch

################# MEILISEARCH ####################

MEILI_MASTER_KEY=Xhoo7hah

MEILISEARCH_HOST=http://meilisearch:7700
MEILISEARCH_KEY="${MEILI_MASTER_KEY}"
MEILI_MASTER_KEY="${MEILI_MASTER_KEY}"

################# REDIS #########################

REDIS_CLIENT=predis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

################# PULSE #########################

PULSE_INGEST_DRIVER=redis

################# MAIL ##########################

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

################# AUDIO #####################

HLS_ENABLED=false

################# METRICS ####################

METRICS_ACCESS_RESTRICTED=true
METRICS_ALLOWED_IPS=127.0.0.1

################# AWS #########################

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

################# PUSHER ######################

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

################# REVERB ######################

REVERB_APP_ID=
REVERB_APP_KEY=
REVERB_APP_SECRET=
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"

################# SOCIAL LOGIN ####################

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT=

########### TELEGRAM (BACKUP NOTIFICATIONS) ########

TELEGRAM_BOT_TOKEN=
TELEGRAM_NOTIFIABLE_CHAT_ID=

################# SERVICES ####################

SERVICE_MAEG_URL="http://audio-embedding-generator:5000/model/predict"
# SERVICE_AME_URL="http://127.0.0.1:5001/extract"
# SERVICE_IMAGE_GENERATOR_URL="http://127.0.0.1:5030"
# SERVICE_TEXT_EMBEDDER_URL="http://127.0.0.1:5020/embedding/encode"

# OLLAMA_URL="http://127.0.0.1:11434"
# OLLAMA_MODEL="gemma:2b-instruct"
