<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MetricsEndpointTest extends TestCase
{
    // use RefreshDatabase;

    public function test_metrics_endpoint_returns_prometheus_format()
    {
        $response = $this->get('/metrics');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');

        $content = $response->getContent();

        // Check for basic Prometheus format
        $this->assertStringContainsString('# HELP', $content);
        $this->assertStringContainsString('# TYPE', $content);

        // Check for some expected metrics
        $this->assertStringContainsString('smovee_app_info', $content);
        $this->assertStringContainsString('smovee_database_connection_status', $content);
        $this->assertStringContainsString('smovee_users_total', $content);
    }

    public function test_metrics_endpoint_with_access_restriction()
    {
        // Enable access restriction
        config(['metrics.access_restricted' => true]);
        config(['metrics.require_auth' => true]);

        $response = $this->get('/metrics');

        $response->assertStatus(401);
    }

    public function test_metrics_endpoint_with_api_token()
    {
        // Enable access restriction with token
        config(['metrics.access_restricted' => true]);
        config(['metrics.api_token' => 'test-token']);

        // Test without token
        $response = $this->get('/metrics');
        $response->assertStatus(401);

        // Test with token in header
        $response = $this->withHeaders([
            'X-Metrics-Token' => 'test-token',
        ])->get('/metrics');
        $response->assertStatus(200);

        // Test with token in query
        $response = $this->get('/metrics?token=test-token');
        $response->assertStatus(200);
    }

    public function test_metrics_endpoint_handles_errors_gracefully()
    {
        // This test ensures the endpoint doesn't crash even if some metrics fail
        $response = $this->get('/metrics');

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should still return valid Prometheus format even if some metrics fail
        $this->assertStringContainsString('smovee_', $content);
    }
}
