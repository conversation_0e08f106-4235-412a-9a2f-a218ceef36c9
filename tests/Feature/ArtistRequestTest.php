<?php

use App\Models\ArtistRequest;
use App\Models\User;
use App\Enums\UserRole;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('artist request can be created with youtube link', function () {
    $user = User::factory()->create([
        'role' => UserRole::GUEST,
        'email_verified_at' => now(),
    ]);

    $response = $this->actingAs($user)
        ->post(route('app.settings.artist-request'), [
            'artist_type' => 'singer',
            'description' => 'I am a passionate singer with 5 years of experience and would love to share my music on this platform.',
            'youtube_link' => 'https://youtube.com/@testartist',
        ]);

    $response->assertRedirect();

    $this->assertDatabaseHas('artist_requests', [
        'user_id' => $user->id,
        'artist_type' => 'singer',
        'youtube_link' => 'https://youtube.com/@testartist',
    ]);
});

test('artist request validates youtube link format', function () {
    $user = User::factory()->create([
        'role' => UserRole::GUEST,
        'email_verified_at' => now(),
    ]);

    $response = $this->actingAs($user)
        ->post(route('app.settings.artist-request'), [
            'artist_type' => 'singer',
            'description' => 'I am a passionate singer with 5 years of experience.',
            'youtube_link' => 'https://invalid-site.com/channel',
        ]);

    $response->assertSessionHasErrors(['youtube_link']);
});

test('artist request works without youtube link', function () {
    $user = User::factory()->create([
        'role' => UserRole::GUEST,
        'email_verified_at' => now(),
    ]);

    $response = $this->actingAs($user)
        ->post(route('app.settings.artist-request'), [
            'artist_type' => 'singer',
            'description' => 'I am a passionate singer with 5 years of experience and would love to share my music on this platform.',
        ]);

    $response->assertRedirect();

    $this->assertDatabaseHas('artist_requests', [
        'user_id' => $user->id,
        'artist_type' => 'singer',
        'youtube_link' => null,
    ]);
});

test('api artist request can be created with youtube link', function () {
    $user = User::factory()->create([
        'role' => UserRole::GUEST,
        'email_verified_at' => now(),
    ]);

    $response = $this->actingAs($user, 'sanctum')
        ->postJson('/api/settings/artist-request', [
            'artist_type' => 'singer',
            'description' => 'I am a passionate singer with 5 years of experience and would love to share my music on this platform.',
            'youtube_link' => 'https://youtube.com/channel/UC123456789',
        ]);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Artist request submitted successfully',
        ]);

    $this->assertDatabaseHas('artist_requests', [
        'user_id' => $user->id,
        'artist_type' => 'singer',
        'youtube_link' => 'https://youtube.com/channel/UC123456789',
    ]);
});

test('api artist request validates youtube link format', function () {
    $user = User::factory()->create([
        'role' => UserRole::GUEST,
        'email_verified_at' => now(),
    ]);

    $response = $this->actingAs($user, 'sanctum')
        ->postJson('/api/settings/artist-request', [
            'artist_type' => 'singer',
            'description' => 'I am a passionate singer with 5 years of experience.',
            'youtube_link' => 'https://invalid-site.com/channel',
        ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['youtube_link']);
});
