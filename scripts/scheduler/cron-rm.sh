#!/bin/bash

# Get the current working directory
WORKING_DIR=$(pwd)

# Define the beginning of the command we want to remove
CMD_START="cd $WORKING_DIR && php artisan schedule:run"

# Create a temporary file for the new crontab
TEMP_CRONFILE=$(mktemp)

# Write the current crontab to the temporary file, excluding the job we want to remove
crontab -l | grep -v "$CMD_START" > "$TEMP_CRONFILE"

# Install the new crontab
crontab "$TEMP_CRONFILE"

# Remove the temporary file
rm "$TEMP_CRONFILE"

echo "Cron job removed successfully if it existed."
