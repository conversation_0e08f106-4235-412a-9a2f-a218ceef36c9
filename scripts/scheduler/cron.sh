#!/bin/bash

# Get the current working directory
WORKING_DIR=$(pwd)

# Define the command with the dynamic path
CRON_JOB="* * * * * cd $WORKING_DIR && php artisan schedule:run >> /dev/null 2>&1"

# Check if the crontab already contains the exact cron job
crontab -l | grep -Fq "$CRON_JOB" 

# $? is a special variable that holds the exit status of the last command that was executed
if [ $? -ne 0 ]; then
    # The cron job does not exist in the crontab, so we will add it
    
    # Write out the current crontab to a temporary file
    crontab -l > mycron
    
    # Echo the new cron job into the temporary file
    echo "$CRON_JOB" >> mycron
    
    # Install the new cron jobs from the temporary file
    crontab mycron
    
    # Remove the temporary file
    rm mycron
    
    echo "Cron job added successfully."
else
    # The cron job already exists, no action required
    echo "Cron job already exists."
fi
