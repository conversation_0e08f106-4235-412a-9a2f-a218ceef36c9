#!/bin/bash

# Define the path to the original config and the supervisor config directory
ORIGINAL_CONFIG_PATH="scripts/supervisord/smovee.conf"
SUPERVISOR_CONFIG_DIR="/etc/supervisor/conf.d"

# Use 'pwd' to get the current directory of the script, assuming it is in the project root
PROJECT_PATH=$(pwd)

# Get the current username
CURRENT_USER=${USER}

# Ensure the smovee.conf exists
if [ ! -f "$ORIGINAL_CONFIG_PATH" ]; then
    echo "Error: Configuration file does not exist at $ORIGINAL_CONFIG_PATH"
    exit 1
fi

# Copy the smovee.conf to Supervisor's conf.d directory
sudo cp "$ORIGINAL_CONFIG_PATH" "$SUPERVISOR_CONFIG_DIR"

# Construct the supervisor config file path
SUPERVISOR_CONFIG_PATH="$SUPERVISOR_CONFIG_DIR/smovee.conf"

# Check if the copy was successful
if [ ! -f "$SUPERVISOR_CONFIG_PATH" ]; then
    echo "Error: Failed to copy configuration file to $SUPERVISOR_CONFIG_PATH"
    exit 1
fi


# Replace /project/path with the current project path in the supervisor configuration file
sudo sed -i "s|/project/path|$PROJECT_PATH|g" "$SUPERVISOR_CONFIG_PATH"


# Replace user=ubuntu with the current Linux username
sudo sed -i "s/user=.*/user=$CURRENT_USER/g" "$SUPERVISOR_CONFIG_PATH"


# Restart the supervisor service to apply changes
sudo systemctl restart supervisor.service


echo "Supervisor configuration has been updated successfully."
