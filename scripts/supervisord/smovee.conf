[supervisord]
minfds=10000

[program:octane]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan octane:start --host=0.0.0.0
directory=/project/path
autostart=true
autorestart=true
startsecs=5
startretries=10
user=ubuntu
stdout_logfile=/project/path/storage/logs/octane.log
redirect_stderr=true
environment=APP_ENV="production"

[program:inertia-ssr]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan inertia:start-ssr
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/inertia-ssr.log
redirect_stderr=true
environment=APP_ENV="production"

[program:pulse-check]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan pulse:check
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/pulse-check.log
redirect_stderr=true
stopasgroup=true
killasgroup=true

[program:pulse-work]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan pulse:work
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/pulse-work.log
redirect_stderr=true
stopasgroup=true
killasgroup=true

[program:queue-worker-default]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan queue:work --sleep=3 --tries=3 --max-time=3600
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
numprocs=10
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/queue-worker-default.log
redirect_stderr=true
stopasgroup=true
killasgroup=true
stopwaitsecs=3600


[program:queue-worker-recommender]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan queue:work --queue=recommender --sleep=3 --tries=3 --max-time=3600
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
numprocs=10
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/queue-worker-recommender.log
redirect_stderr=true
stopasgroup=true
killasgroup=true
stopwaitsecs=3600


[program:reverb-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /project/path/artisan reverb:start
directory=/project/path
autostart=true
autorestart=true
user=ubuntu
startsecs=5
startretries=10
stdout_logfile=/project/path/storage/logs/reverb-worker.log
redirect_stderr=true
stopasgroup=true
killasgroup=true
stopwaitsecs=3600
