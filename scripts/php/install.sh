#!/bin/bash

# Define the PHP version variable
PV=8.3

# Update and upgrade the package list and installed packages
sudo apt update && sudo apt upgrade -y

# Install necessary packages
sudo apt install software-properties-common ca-certificates lsb-release apt-transport-https -y

# Add the PHP repository from Ondrej Sury
sudo add-apt-repository ppa:ondrej/php -y

# Update the package list after adding new repositories
sudo apt update

# Install the specified version of PHP
sudo apt install php$PV -y

# Install PHP core CLI and various common extensions
sudo apt install php$PV-cli php$PV-swoole php$PV-mysql php$PV-mbstring \
    php$PV-xml php$PV-curl php$PV-common php$PV-pdo php$PV-tokenizer -y

# Install additional PHP extensions that are often used
sudo apt install php$PV-gmp php$PV-gd php$PV-zip php$PV-bcmath php$PV-pgsql \
    php$PV-ctype php$PV-intl php$PV-soap php$PV-gettext php$PV-exif -y

# Install some other PHP extensions
sudo apt install php$PV-fileinfo php$PV-iconv php$PV-bz2 -y

echo "PHP $PV installation and configuration complete."
