// K6 Load Test Script for http://localhost:8000/

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Trend, Rate, Counter } from 'k6/metrics';

// --- Configuration ---

// Base URL of the target application
const BASE_URL = 'http://localhost:8000/';

// Custom Metrics
// Track duration of homepage requests specifically
let homepageDuration = new Trend('homepage_duration', true);
// Track the rate of successful checks
let checkFailureRate = new Rate('check_failure_rate');
// Track the rate of requests resulting in non-200 status codes
let errorRate = new Rate('errors');

// --- Test Options ---
export let options = {
  // Define stages for ramp-up, steady load, and ramp-down
  // Adjust these based on your expected traffic and test goals
  stages: [
    { duration: '30s', target: 10 }, // Ramp up to 10 virtual users over 30 seconds
    { duration: '1m', target: 10 },  // Stay at 10 virtual users for 1 minute
    { duration: '15s', target: 0 },  // Ramp down to 0 users over 15 seconds
  ],

  // Define thresholds for pass/fail criteria
  thresholds: {
    // --- HTTP Errors ---
    // Less than 1% of requests should fail (network error, timeout, etc.)
    'http_req_failed': ['rate<0.01'],

    // --- Performance ---
    // 95th percentile of *all* request durations should be below 800ms. Adjust as needed.
    'http_req_duration': ['p(95)<800'],
    // 95th percentile of specifically homepage requests should be below 750ms. Adjust as needed.
    'homepage_duration': ['p(95)<750'],

    // --- Checks ---
    // More than 98% of checks should pass
    'checks': ['rate>0.98'],
    // Explicit check failure rate (should be low)
    'check_failure_rate': ['rate<0.02'],

    // --- Custom Error Rate ---
    // Less than 1% of requests should result in a non-200 status code
    'errors': ['rate<0.01'],
  },

  // Optional: Define user agent
  userAgent: 'MyK6LoadTest/1.0',

  // Optional: Disable TLS verification if using self-signed certs (NOT recommended for production URLs)
  // insecureSkipTLSVerify: false,
};

// --- Virtual User Code ---
export default function () {
  group('Homepage Load', function () {
    // Make a GET request to the base URL
    let res = http.get(BASE_URL, {
      tags: { name: 'Homepage' }, // Tag requests for better filtering in results
    });

    // --- Checks ---
    // 1. Check if the HTTP status code is 200 (OK)
    const statusCheck = check(res, {
      'status is 200': (r) => r.status === 200,
    });

    // Record check failures
    checkFailureRate.add(!statusCheck);
    // checkFailureRate.add(!contentCheck); // Add this if using the content check

    // Record errors based on status code
    errorRate.add(res.status !== 200);

    // Add the request duration to our custom trend metric
    if (res.timings && res.timings.duration) {
      homepageDuration.add(res.timings.duration);
    }

  }); // End Group: Homepage Load

  // --- Think Time ---
  // Simulate user pausing for 1 to 3 seconds between actions
  sleep(Math.random() * 2 + 1);
}

// --- Optional Setup/Teardown ---
export function setup() {
  console.log('Starting k6 test for ' + BASE_URL);
  // You could potentially login here once if needed for other tests
}

export function teardown(data) {
  console.log('Finished k6 test.');
  // You could perform cleanup actions here
}