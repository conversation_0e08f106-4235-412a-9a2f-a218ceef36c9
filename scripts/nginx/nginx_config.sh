#!/bin/bash

PROJECT_PATH=$(pwd)
ORIGINAL_CONFIG_FILE="scripts/nginx/smovee.conf"

NGINX_CONF_DIR="/etc/nginx/sites-available/"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled/"
NGINX_CONFIG_FILE="smovee.conf"

# Check if the Nginx configuration file exists in the project directory
if [ ! -f "$PROJECT_PATH/$ORIGINAL_CONFIG_FILE" ]; then
    echo "The Nginx configuration file does not exist in the project directory."
    exit 1
fi

# Copy the Nginx configuration file to /etc/nginx/sites-available
sudo cp "$PROJECT_PATH/$ORIGINAL_CONFIG_FILE" "$NGINX_CONF_DIR/$NGINX_CONFIG_FILE"

# Check if the symlink already exists in /etc/nginx/sites-enabled
if [ ! -L "$NGINX_ENABLED_DIR/$NGINX_CONFIG_FILE" ]; then
    # Create the symlink
    sudo ln -s "$NGINX_CONF_DIR/$NGINX_CONFIG_FILE" "$NGINX_ENABLED_DIR"
fi

# Test the Nginx configuration for syntax errors
if ! sudo nginx -t; then
    echo "Nginx configuration test failed"
    exit 1
else
    echo "Nginx configuration is OK"
fi

# Restart Nginx to apply changes
sudo systemctl restart nginx && echo "Nginx restarted successfully!"
