#!/bin/bash

# Update the package list
sudo apt-get update

# Define an array of system packages to check and install if necessary
SYSTEM_PACKAGES=(
    python3
    ffmpeg
    supervisor
    unzip
    zstd
    nginx
)

PYTHON_PACKAGES=(
    audio_metadata
)

# Loop through each system package to check if it's installed and install it if it isn't
for pkg in "${SYSTEM_PACKAGES[@]}"; do
    if ! dpkg -s "$pkg" >/dev/null 2>&1; then
        echo "Package $pkg is not installed. Installing..."
        sudo apt-get install -y "$pkg"
    else
        echo "Package $pkg is already installed."
    fi
done


# Check for Snap installation of 'task', install if not present
if ! snap list | grep -q "^task\s"; then
    echo "Snap package 'task' is not installed. Installing..."
    sudo snap install task --classic
else
    echo "Snap package 'task' is already installed."
fi



# Check if pip3 is installed for Python 3, install it if it isn't
if ! command -v pip3 >/dev/null 2>&1; then
    echo "pip3 not found. Installing..."
    sudo apt-get install -y python3-pip
else
    echo "pip3 is already installed."
fi


# Install Python packages with pip3
for py_pkg in "${PYTHON_PACKAGES[@]}"; do
    pip3 install "$py_pkg"
done
