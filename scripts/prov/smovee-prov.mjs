import * as mm from "music-metadata";
import path from "path";
import fs from "fs/promises";
import util from 'node:util'

import { createReadStream, createWriteStream } from "fs";
import { fileURLToPath } from "url";

import { pipeline as streamPipeline } from "node:stream/promises";
import { Stream } from "node:stream";

import Scraper from "images-scraper";
import albumArt from "album-art"

import got from "got";
import FormData from "form-data";

import { fileTypeFromFile } from "file-type";

import { Queue } from "./utils/queue.mjs";
import { generateRandomFilenameForImage, getRandomElement, sanitizeSongName } from "./utils/helpers.mjs";

const queue = new Queue(1);

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const files = await fs.readdir(__dirname);

const ignoreFiles = ["node_modules"]

const token = ""
const baseUrl = "";

const headers = {
  Authorization: "Bearer " + token,
  Accept: "application/json",
  "X-Requested-With": "XMLHttpRequest",
  "X-Inertia": "true",
};

const google = new Scraper({
  puppeteer: {
    headless: true,
  },
});

function emailfy(text, domain = "smovee.app") {
  // Remove any invalid email character from the text input
  const validEmailChars = /[^a-zA-Z0-9]/g;
  const emailUsername = text.toLowerCase().replace(validEmailChars, "");

  // Combine the cleaned-up text with the domain to form an email address
  const emailAddress = `${emailUsername}@${domain}`;

  return emailAddress.toLowerCase();
}


const randomArtistRole = () => getRandomElement(["SINGER", "PODCASTER"])


const randomAlbumType = () => getRandomElement(["CHANNEL", "ALBUM"])


/**
 * @param {string} artist
 * @param {string | undefined} album
 **/
async function searchImage(artist, album, query) {
  let results = []
  try {
   const url = await albumArt(artist, { album, size: "large"})
   if(!url) {
      throw new Error("undefined")
   }
   results.push({ url })
  } catch (error) {
    results = await google.scrape(query, 20).catch(() => void 0);
    results = results || []
  }

  let tmpResult = null
  let result = null


  for (result of results) {
    const response = await got.head(result.url).catch(() => void 0)
    if (!response) {
      continue
    }

    const fileSize = (+response.headers['content-length']) / 1024;
    const contentType = response.headers["content-type"]

    const validImage = contentType && contentType.startsWith("image/")


    if (fileSize < 50 && fileSize >= 20) {
      if (validImage && (!tmpResult || tmpResult.fileSize < fileSize)) {
        tmpResult = {
          fileSize,
          result
        }
      }

      if (result === results[results.length - 1] && tmpResult) {
        result = tmpResult.result
      } else {
        continue;
      }

    } else if (fileSize > 8192) {
      continue
    } else if (fileSize < 20) {
      continue
    } else if (!validImage) {
      continue
    }

    let filename = null
    try {
      filename = generateRandomFilenameForImage(contentType);
    } catch (_) {
      continue
    }

    const filepath = "tmp/" + filename

    const fileStream = createWriteStream(filepath)
    await streamPipeline(
      got.stream(result.url),
      fileStream
    )

    const fsize = await fs.stat(filepath).catch(() => void 0)

    if (fsize && fsize.size / 1024 < 20) {
      continue
    }


    return {
      stream: createReadStream(filepath),
      result,
      filename,
      cleanUp: () => fs.unlink(filepath).catch(() => void 0)
    };
  }

  return null;
}

// APIs
async function getArtistOrCreateAPI(name) {
  const email = emailfy(name);

  let artist = await got(baseUrl + "/artists/search/email?q=" + encodeURIComponent(email), {
    headers,
  }).json().catch(() => void 0)

  if (!artist) {
    const form = new FormData();

    const imageStream = await searchImage(name);

    form.append("name", name);
    form.append("email", email);
    form.append("role", randomArtistRole());
    if (imageStream) {
      form.append("image", imageStream.stream);
    }


    artist = await got.post(baseUrl + "/artists", {
      headers: { ...headers, ...form.getHeaders() },
      body: form,
    }).json();

    await imageStream?.cleanUp()
  }

  return artist;
}

/**
 * @param {string} name
 **/
async function getRandomGenre(name) {
  const genre = await got(baseUrl + "/genres/random" + (name ? "?q=" + name : ''), { headers }).json();

  return genre;
}

/**
 * @param {string} name
 * @param {object} artistAPI
 * @param {string} genreName
**/
async function getAlbumOrCreateAPI(name, artistAPI, genreName) {
  let album = await got(
    baseUrl + "/artists/" + artistAPI.id + "/search/channel/name?q=" + encodeURIComponent(name), {
    headers,
  })
    .json()
    .catch(() => void 0);

  if (!album) {
    const form = new FormData()

    const imageStream = await searchImage(artistAPI.name, name);

    const genre = await getRandomGenre(genreName);

    form.append("name", name)
    form.append("description", name)
    form.append("type", randomAlbumType())
    if (imageStream) {
      form.append("image", imageStream.stream)
    }
    form.append("genre_id", genre.id)
    form.append("user_id", artistAPI.id)

    album = await got
      .post(baseUrl + "/channels", {
        headers: { ...headers, ...form.getHeaders() },
        body: form
      }).json()

    await imageStream?.cleanUp()
  }

  return album;
}

/**
 * @param {{albumAPI: object, artistAPI: object, title: string, file: Stream, year: string, featuring: string[]}} params
**/
async function createArticleAudioAPI(params) {
  const article = await got.get(baseUrl + "/channels/" + params.albumAPI.id + "/search/article/name" + "?q=" + encodeURIComponent(params.title), {
    headers
  })
    .json()
    .catch(() => void 0)


  if (!article) {
    const form = new FormData()
    form.append("name", params.title)
    form.append("description", params.title)
    form.append("channel_id", params.albumAPI.id)
    form.append("genre_id", params.albumAPI.genre.id)
    form.append("user_id", params.artistAPI.id)
    form.append("audio", params.file)
    if (params.year) {
      form.append("year", params.year)
    }
    params.featuring.forEach((feat) => {
      form.append("featuring[]", feat)
    })

    await got.post(baseUrl + "/articles", {
      headers: { ...headers, ...form.getHeaders() },
      body: form,
    });
  }
}

/**
 * @param {string[]} array
 */
function fm(array) {
  const keys = array.reduce(
    (acc, value) => ({ ...acc, [value]: (acc[value] || 0) + 1 }),
    {}
  );
  return Object.keys(keys).reduce(
    (acc, v) => (keys[v] > keys[acc] ? v : acc),
    array[0]
  )?.trim();
}

/**
 * @param {string} file
 * @param {{ parent: string, albums: string[], artists: string[] }} history
 **/
async function run(file, history) {
  const stat = await fs.stat(file);


  const basename = path.basename(file)

  if (ignoreFiles.includes(basename)) {
    return;
  }


  if (stat.isFile()) {
    const fileType = await fileTypeFromFile(file)

    if (!fileType || !fileType.mime.startsWith('audio/mpeg')) {
      return
    }
  } else if (stat.isDirectory()) {
    const files = await fs.readdir(file);

    files.forEach(function(filename) {
      const history = {
        parent: path.basename(file),
        artists: [],
        albums: [],
      };
      run(path.join(file, filename), history);
    });

    return;
  }

  const filesize = stat.size / 1024;
  if (filesize < 200) {
    return;
  }

  const metadata = await mm.parseFile(file).catch(() => void 0);
  if (!metadata) {
    return;
  }

  const filename = path.basename(file, ".mp3").trim();

  let title = metadata.common.title?.trim() || filename;
  title = sanitizeSongName(title)

  const titles = title.split("-");

  const artists = metadata.common.artists?.reduce((acc, v) => {
    acc.push(...v.split(/[,;]/).map((v) => sanitizeSongName(v)).filter(Boolean))
    return acc
  }, [])
  const featuring = artists ? artists.slice(1, artists.length) : []

  let artist = artists?.at(0) || fm(history.artists) || (titles.length > 1 ? titles[0].trim() : undefined) || history.parent;
  artist = sanitizeSongName(artist)

  let album = metadata.common.album?.trim() || fm(history.albums) || title;
  album = sanitizeSongName(album)

  let genreName = metadata.common.genre?.at(0)
  genreName = genreName ? sanitizeSongName(genreName) : undefined

  let year = metadata.common.year?.toString()
  year = year && year.length === 4 ? year : undefined


  queue.task(async () => {
    const percentage = (queue.success_tasks + queue.failure_tasks) * 100 / queue.submitted_tasks
    console.log(
      "percentage: ", percentage.toFixed(2),
      "busy_workers: ", queue.busy_workers,
      "success_tasks: ", queue.success_tasks,
      "submitted_tasks: ", queue.submitted_tasks,
      "failure_tasks: ", queue.failure_tasks,
      "\r"
    )

    try {

      const artistAPI = await getArtistOrCreateAPI(artist);

      if (artistAPI) {
        const albumAPI = await getAlbumOrCreateAPI(album, artistAPI, genreName);
        const fileStream = createReadStream(file);

        albumAPI &&
          (await createArticleAudioAPI({
            artistAPI,
            albumAPI,
            title: title || album,
            featuring,
            year,
            file: fileStream,
          }));
      }
    } catch (error) {
      let errorObj = error
      if (error.response) {
        errorObj = error.response.body
      }

      await fs.appendFile("logs.txt",
        "\nDate:" + new Date().toLocaleString() +
        "\nFile: " + file +
        "\nError: " + util.inspect(errorObj, true) + "\n"
      ).catch(() => void 0)

      throw new Error(error)
    }

  });

  // defer call
  if (metadata.common.album) {
    history.albums.push(metadata.common.album);
  }

  if (metadata.common.artists) {
    history.artists.push(...metadata.common.artists);
  }
}

files.forEach(function(filename) {
  const history = {
    parent: filename,
    artists: [],
    albums: [],
  };
  run(path.join(__dirname, filename), history);
});
