export function wait(sec = 1) {
  return new Promise(resolve => setTimeout(resolve, sec * 1000))
}

export class Queue {
  running = false
  queues = []

  _failure_tasks = 0
  _success_tasks = 0
  _submitted_tasks = 0

  constructor(poolSize = 1) {
    this.poolSize = poolSize
  }

  get busy_workers() {
    return this.queues.length
  }

  get failure_tasks() {
    return this._failure_tasks
  }

  get success_tasks() {
    return this._success_tasks
  }

  get submitted_tasks() {
    return this._submitted_tasks
  }

  _task(func, ...params) {
    return () => func(...params)
  }

  task(func, ...params) {
    this._submitted_tasks += 1
    this.queues.push(this._task(func, ...params))

    if (this.running === false) {
      this.running = true
      wait(0.1).then(() => this.work())
    }
  }

  async work() {
    const tasks = this.queues.splice(0, this.poolSize)
    if (tasks.length === 0) {
      this.running = false
      return
    }

    const results = await Promise.allSettled(tasks.map(fn => fn()))
    results.forEach(({ status }) => {
      switch (status) {
        case "fulfilled":
          this._success_tasks += 1
          break
        case "rejected":
          this._failure_tasks += 1
          break
      }
    })

    this.work()
  }
}

