export function generateRandomFilenameForImage(mimeType) {
    const extensionMap = {
        "image/jpeg": ".jpg",
        "image/png": ".png",
        "image/gif": ".gif",
        "image/webp": ".webp",
        "image/svg+xml": ".svg",
        // Add any other image MIME types you need to support
    };

    function getRandomString(length) {
        const possibleChars =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        let randomString = "";
        for (let i = 0; i < length; i++) {
            randomString += possibleChars.charAt(
                Math.floor(Math.random() * possibleChars.length)
            );
        }
        return randomString;
    }

    if (!extensionMap.hasOwnProperty(mimeType)) {
        throw new Error("Unsupported MIME type for images");
    }

    const extension = extensionMap[mimeType];
    const filename = getRandomString(10) + extension; // Generate a 10-character random string followed by the extension
    return filename;
}

export function getRandomElement(arr) {
    const randomIndex = Math.floor(Math.random() * arr.length);
    return arr[randomIndex];
}

export function sanitizeSongName(songName) {
    if (!songName || typeof songName !== "string") {
        return "";
    }

    // Define known TLDs for domain removal
    const tlds = [
        "com",
        "org",
        "net",
        "edu",
        "gov",
        "mil",
        "biz",
        "info",
        "mobi",
        "name",
        "aero",
        "asia",
        "cat",
        "coop",
        "jobs",
        "pro",
        "tel",
        "travel",
        "fr",
        "de",
        "uk",
        "us",
        "it",
        "es",
        "nl",
        "ru",
        "ca",
        "au",
        "nz",
        "ch",
        "be",
        "se",
        "no",
        "dk",
        "fi",
        "br",
        "mx",
        "ar",
        "za",
        "in",
        "jp",
        "cn",
        "kr",
        "sg",
        "my",
        "id",
        "ph",
        "vn",
        "th",
        "tr",
        "sa",
        "ae",
        "il",
        "eg",
        "ma",
        "ng",
        "ke",
    ];
    const tldsRegex = `(${tlds.join("|")})`;

    // Remove leading track numbers (e.g., "01 - ", "12. ")
    let cleanName = songName.replace(/^\d+[\s.-]*/, "");

    // Remove file extensions (e.g., .mp3, .flac)
    cleanName = cleanName.replace(/\.(mp3|wav|flac|m4a|ogg|wma|aac)$/i, "");

    // Remove URLs and domains ending with known TLDs (e.g., "www.example.com", "http://example.fr")
    const urlDomainRegex = new RegExp(
        `(?:https?://)?(?:www\\.)?(?:[a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.${tldsRegex}`,
        "gi"
    );
    cleanName = cleanName.replace(urlDomainRegex, "");

    // Remove domain patterns with connectors (e.g., "site.com & other.org")
    const connectorDomainRegex = new RegExp(
        `\\b(?:[a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.${tldsRegex}(?:\\s*[&-]\\s*(?:[a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.${tldsRegex})*\\b`,
        "gi"
    );
    cleanName = cleanName.replace(connectorDomainRegex, "");

    // Replace invalid filename characters with spaces
    const invalidChars = /[<>:"/\\|?*\x00-\x1F]/g;
    cleanName = cleanName.replace(invalidChars, " ");

    // Normalize spaces and dashes
    cleanName = cleanName.replace(/\s+/g, " ").trim();
    cleanName = cleanName.replace(/(\w)-(\w)/g, "$1 - $2");

    // Remove all content within brackets or parentheses (e.g., [Official], (Remix))
    cleanName = cleanName.replace(
        /\[(.*?)\]|\((Official|Audio|Video|HQ|HD|Lyrics)[^)]*\)/gi,
        ""
    );

    // Remove trailing special characters except closing ), ], }
    cleanName = cleanName.replace(/[^A-Za-z0-9)\]}]+$/g, "");

    // Capitalize each word and trim
    cleanName = cleanName
        .replace(/\b\w/g, (match) => match.toUpperCase())
        .trim();

    return cleanName;
}
