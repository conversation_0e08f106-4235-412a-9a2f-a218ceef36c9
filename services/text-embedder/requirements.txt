annotated-types==0.6.0
anyio==4.3.0
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
exceptiongroup==1.2.0
fastapi==0.109.2
filelock==3.13.1
fsspec==2024.2.0
gunicorn==21.2.0
h11==0.14.0
httptools==0.6.1
huggingface-hub==0.20.3
idna==3.6
Jinja2==3.1.3
joblib==1.3.2
MarkupSafe==2.1.5
mpmath==1.3.0
networkx==3.2.1
nltk==3.8.1
numpy==1.26.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.3.101
nvidia-nvtx-cu12==12.1.105
packaging==23.2
pillow==10.2.0
pydantic==2.6.1
pydantic_core==2.16.2
python-dotenv==1.0.1
PyYAML==6.0.1
regex==2023.12.25
requests==2.31.0
safetensors==0.4.2
scikit-learn==1.4.1.post1
scipy==1.12.0
sentence-transformers==2.3.1
sentencepiece==0.1.99
sniffio==1.3.0
starlette==0.36.3
sympy==1.12
threadpoolctl==3.3.0
tokenizers==0.15.2
torch==2.2.0
tqdm==4.66.2
transformers==4.37.2
triton==2.2.0
typing_extensions==4.9.0
urllib3==2.2.1
uvicorn==0.27.1
uvloop==0.19.0
watchfiles==0.21.0
websockets==12.0
