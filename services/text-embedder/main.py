import os
from fastapi import FastAPI
from sentence_transformers import SentenceTransformer
from pydantic import BaseModel


class Item(BaseModel):
    text: str


app = FastAPI()

MODEL_NAME = os.getenv("MODEL_NAME", "all-MiniLM-L6-v2")
model = SentenceTransformer(MODEL_NAME)


@app.get("/")
async def read_root():
    return {"Hello": "embedding Encoder"}


@app.post("/embedding/encode")
async def extract(item: Item):
    embedding = model.encode(item.text)
    return {"status": "ok", "embedding": embedding.tolist()}
