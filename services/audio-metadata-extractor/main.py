import os
import librosa

from dateutil.parser import parse

from typing import Annotated
from tempfile import NamedTemporaryFile
from tinytag import <PERSON><PERSON>ag
from audio_split import audio_split

from fastapi import FastAPI, File, HTTPException, UploadFile


app = FastAPI()


@app.get("/")
async def read_root():
    return {"Hello": "Audio Metadata Extractor"}


@app.post("/extract")
async def extract(audio: Annotated[UploadFile, File()]):
    temp = NamedTemporaryFile(delete=False)

    try:
        try:
            contents = audio.file.read()
            with temp as f:
                f.write(contents)
        except Exception:
            raise HTTPException(status_code=500, detail="Error on uploading the file")
        finally:
            audio.file.close()

        return extract_metadata(temp.name)

    except Exception:
        raise HTTPException(status_code=500, detail="Something went wrong")
    finally:
        temp.close()
        os.remove(temp.name)


# Functions
def extract_metadata(filename: str):
    tags = TinyTag(None, None)

    try:
        tags = TinyTag.get(filename)
        tags.year = str(parse(tags.year).year)
    except Exception:
        tags.year = None
        pass

    return {
        "tempo": get_bpm_from_mp3(filename),
        "tags": tags.as_dict(),
    }


def to_dict(obj):
    if isinstance(obj, dict):
        return {k: to_dict(v) for k, v in obj.items()}
    elif hasattr(obj, "__dict__"):
        return {k: to_dict(v) for k, v in obj.__dict__.items() if not k.startswith("_")}
    elif isinstance(obj, list):
        return [to_dict(v) for v in obj]
    elif isinstance(obj, tuple):
        return tuple(to_dict(v) for v in obj)
    elif isinstance(obj, set):
        return {to_dict(v) for v in obj}
    else:
        return obj


def get_bpm_from_mp3(file_path: str):
    mini_file = audio_split(file_path, "./")

    try:
        # Load the MP3 file using librosa
        y, sr = librosa.load(mini_file, sr=None)
        # Estimate the tempo (BPM)
        onset_env = librosa.onset.onset_strength(y=y, sr=sr)
        bpm, _ = librosa.beat.beat_track(onset_envelope=onset_env, sr=sr)
    except Exception:
        bpm = 0
    finally:
        os.remove(mini_file)

    return bpm
