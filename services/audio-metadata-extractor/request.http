@base=https://localhost:5000

POST {{base}}/extract
Content-Type: multipart/form-data
Accept: application/json

{
    "audio": "file-binary"
}

// Response
# {
#   "tempo": 117.45383522727273,
#   "tags": {
#     "_filehandler": {},
#     "_filename": "/tmp/tmpszwwlt0d",
#     "_default_encoding": null,
#     "filesize": 2283425,
#     "album": "Red-Eye Effect",
#     "albumartist": null,
#     "artist": "Lowswimmer",
#     "audio_offset": 134698,
#     "bitrate": 128,
#     "channels": 2,
#     "comment": null,
#     "composer": null,
#     "disc": null,
#     "disc_total": null,
#     "duration": 134.**************,
#     "extra": {},
#     "genre": null,
#     "samplerate": 44100,
#     "bitdepth": null,
#     "title": "Memory Card",
#     "track": null,
#     "track_total": null,
#     "year": "2023",
#     "_parse_tags": true,
#     "_parse_duration": true,
#     "_load_image": false,
#     "_image_data": null,
#     "_ignore_errors": false,
#     "_bytepos_after_id3v2": 134688
#   }
# }