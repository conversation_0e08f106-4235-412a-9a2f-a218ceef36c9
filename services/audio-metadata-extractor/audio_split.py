import os
from pydub import AudioSegment
import uuid


def audio_split(input_file_path, output_dir, seconds=1.5):
    # Load the audio file (with metadata if available)
    audio = AudioSegment.from_file(input_file_path)

    ninety_seconds = seconds * 60 * 1000

    # Extract the first 1.5 minutes of the audio
    first_ninety_seconds = audio[:ninety_seconds]

    # Generate a unique random filename for the output file
    random_filename = str(uuid.uuid4()) + ".mp3"

    # Set the full output file path
    output_file_path = os.path.join(output_dir, random_filename)

    first_ninety_seconds.export(output_file_path, format="mp3")

    return output_file_path
