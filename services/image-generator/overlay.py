from enum import Enum
from PIL import Image
from helpers import generate_random_filename


def calculate_percentage(image_size: tuple[int, int], percentage=10):
    width, height = image_size
    factor = percentage / 100.0

    new_width = int(round(width * factor))
    new_height = int(round(height * factor))

    return (new_width, new_height)


def resize_with_aspect_ratio(original_size: tuple[int, int], target_width: int):
    # Unpack original size
    original_width, original_height = original_size

    # Calculate the aspect ratio
    aspect_ratio = original_height / original_width

    # Calculate the new height preserving the aspect ratio
    new_height = int(target_width * aspect_ratio)

    # Return the new size as a tuple
    return (target_width, new_height)


class Positions(Enum):
    CENTER = 20.00
    TOP_RIGHT = 10.01
    TOP_LEFT = 10.02
    BOTTOM_RIGHT = 10.03
    BOTTOM_LEFT = 10.04

    @staticmethod
    def from_str(position_str: str):
        match position_str:
            case "CENTER":
                return Positions.CENTER
            case "TOP_RIGHT":
                return Positions.TOP_RIGHT
            case "TOP_LEFT":
                return Positions.TOP_LEFT
            case "BOTTOM_RIGHT":
                return Positions.BOTTOM_RIGHT
            case "BOTTOM_LEFT":
                return Positions.BOTTOM_LEFT
            case _:
                raise Exception("Invalid Position")


def create_image_overlay(image1, image2, position: Positions):
    # Opening the primary image (used in background)
    img1 = Image.open(image1)
    # Opening the secondary image (overlay image)
    img2 = Image.open(image2).convert("RGBA")

    img1_n_size = calculate_percentage(img1.size, position.value)
    img2_new_size = resize_with_aspect_ratio(img2.size, img1_n_size[0])

    img2 = img2.resize(img2_new_size)

    match position:
        case Positions.CENTER:
            box_width = int(round((img1.width / 2) - (img2.width / 2)))
            box_height = int(round((img1.height / 2) - (img2.height / 2)))

        case Positions.TOP_RIGHT:
            box_width = img1.width - img2_new_size[0] - int(round(img1.width * 0.03))
            box_height = int(round(img1.height * 0.02))

        case Positions.TOP_LEFT:
            box_width = int(round(img1.width * 0.03))
            box_height = int(round(img1.height * 0.02))

        case Positions.BOTTOM_LEFT:
            box_width = int(round(img1.width * 0.03))
            box_height = img1.height - img2_new_size[1] - int(round(img1.height * 0.02))

        case Positions.BOTTOM_RIGHT:
            box_width = img1.width - img2_new_size[0] - int(round(img1.width * 0.03))
            box_height = img1.height - img2_new_size[1] - int(round(img1.height * 0.02))

        case _:
            raise Exception("Invalid Position")

    img1.paste(img2, (box_width, box_height), mask=img2)

    output = generate_random_filename(".png")
    output = f"tmp/{output}"

    img1.save(output)

    return output
