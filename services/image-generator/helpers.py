import uuid


def generate_random_filename(extension=""):
    """
    Generates a random filename string with an optional file extension.

    :param extension: The file extension to append to the filename. Include the dot, e.g., '.txt'.
    :return: A random filename string.
    """
    random_filename = str(uuid.uuid4())  # Generates a random UUID
    if extension:
        return f"{random_filename}{extension}"
    return random_filename
