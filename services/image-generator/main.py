import os
from typing import Annotated
from fastapi import FastAPI, UploadFile, BackgroundTasks, Form
from fastapi.responses import FileResponse
from overlay import create_image_overlay, Positions
from tempfile import NamedTemporaryFile
from gradient_generator import generate_gradient


app = FastAPI()


@app.get("/")
async def index():
    return {"Hello": "Image Generator"}


@app.post("/overlay/generate")
async def overlay(
    background: UploadFile,
    overlay: UploadFile,
    background_tasks: BackgroundTasks,
    position: Annotated[str, Form()] = "TOP_RIGHT",
):
    image1 = NamedTemporaryFile()
    image2 = NamedTemporaryFile()

    image1.write(background.file.read())
    image2.write(overlay.file.read())

    new_image = create_image_overlay(
        image1.name,
        image2.name,
        Positions.from_str(position),
    )

    background_tasks.add_task(
        lambda: (image1.close(), image2.close(), os.remove(new_image))
    )

    return FileResponse(new_image)


@app.post("/overlay/gradient/generate")
async def gradientOverlay(
    overlay: UploadFile,
    background_tasks: BackgroundTasks,
    position: Annotated[str, Form()] = "CENTER",
):
    gradient = generate_gradient(640, 635)

    image1 = NamedTemporaryFile()
    image2 = NamedTemporaryFile()

    image1.write(gradient.read())
    image2.write(overlay.file.read())

    new_image = create_image_overlay(
        image1.name,
        image2.name,
        Positions.from_str(position),
    )

    background_tasks.add_task(
        lambda: (image1.close(), image2.close(), os.remove(new_image))
    )

    return FileResponse(new_image)
