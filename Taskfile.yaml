version: 3

tasks:
  vite-dev:
    cmd: pnpm vite

  vite-build:
    cmd: pnpm build

  serve:
    cmd: pnpm laravel-serve

  serve-octane-dev:
    cmd: php artisan octane:start --watch

  queue-dev:
    cmd: php artisan queue:listen

  dev:
    deps:
      - serve-octane-dev
      - vite-dev
      # - queue-dev

  serve-octane:
    cmd: php artisan octane:start

  ssr:
    cmd: php artisan inertia:start-ssr

  prod-serve:
    deps:
      - serve-octane
      - ssr

  git-pull:
    cmd: git pull origin main

  prod-refresh:
    deps:
      - git-pull
    cmds:
      - composer install --optimize-autoloader --no-dev

      - php artisan config:clear
      - php artisan config:cache

      - php artisan event:clear
      - php artisan event:cache

      - php artisan view:clear
      - php artisan view:cache

      - php artisan icons:cache

      - php artisan migrate

      - pnpm install

      - task: vite-build

      - sudo systemctl restart supervisor

  default:
    deps:
      - serve-octane
      - vite-dev

  ide:
    cmds:
      - php artisan ide-helper:generate
      - php artisan ide-helper:models -M

  # Services
  audio-metadata-extractor:
    dir: services/audio-metadata-extractor
    cmd: ".venv/bin/python -m uvicorn main:app --reload"
    aliases:
      - ame
